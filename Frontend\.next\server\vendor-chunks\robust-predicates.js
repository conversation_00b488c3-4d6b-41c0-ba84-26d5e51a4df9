"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/robust-predicates";
exports.ids = ["vendor-chunks/robust-predicates"];
exports.modules = {

/***/ "(ssr)/./node_modules/robust-predicates/esm/incircle.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/incircle.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   incircle: () => (/* binding */ incircle),\n/* harmony export */   incirclefast: () => (/* binding */ incirclefast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\nconst iccerrboundA = (10 + 96 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst iccerrboundB = (4 + 48 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst iccerrboundC = (44 + 576 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst aa = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst cc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst v = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst axtbc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst aytbc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bxtca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bytca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cxtab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cytab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bct = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cat = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abtt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bctt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst catt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _16b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _16c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _32 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(32);\nconst _32b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(32);\nconst _48 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _64 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(64);\nlet fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nlet fin2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nfunction finadd(finlen, a, alen) {\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(finlen, fin, a, alen, fin2);\n    const tmp = fin;\n    fin = fin2;\n    fin2 = tmp;\n    return finlen;\n}\nfunction incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail, adytail, bdytail, cdytail;\n    let axtbclen, aytbclen, bxtcalen, bytcalen, cxtablen, cytablen;\n    let abtlen, bctlen, catlen;\n    let abttlen, bcttlen, cattlen;\n    let n1, n0;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    s1 = bdx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adx, _8), _8, adx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, ady, _8), _8, ady, _16b), _16b, _32), _32, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdx, _8), _8, bdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdy, _8), _8, bdy, _16b), _16b, _32b), _32b, _64), _64, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdx, _8), _8, cdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdy, _8), _8, cdy, _16b), _16b, _32), _32, fin);\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = iccerrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0) {\n        return det;\n    }\n    errbound = iccerrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += (adx * adx + ady * ady) * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + 2 * (adx * adxtail + ady * adytail) * (bdx * cdy - bdy * cdx) + ((bdx * bdx + bdy * bdy) * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + 2 * (bdx * bdxtail + bdy * bdytail) * (cdx * ady - cdy * adx)) + ((cdx * cdx + cdy * cdy) * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + 2 * (cdx * cdxtail + cdy * cdytail) * (adx * bdy - ady * bdx));\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n        s1 = adx * adx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n        ahi = c - (c - adx);\n        alo = adx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = ady * ady;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n        ahi = c - (c - ady);\n        alo = ady - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        aa[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        aa[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        aa[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        aa[3] = u3;\n    }\n    if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n        s1 = bdx * bdx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n        ahi = c - (c - bdx);\n        alo = bdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = bdy * bdy;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n        ahi = c - (c - bdy);\n        alo = bdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        bb[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        bb[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        bb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        bb[3] = u3;\n    }\n    if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n        s1 = cdx * cdx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n        ahi = c - (c - cdx);\n        alo = cdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = cdy * cdy;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n        ahi = c - (c - cdy);\n        alo = cdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        cc[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        cc[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        cc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        cc[3] = u3;\n    }\n    if (adxtail !== 0) {\n        axtbclen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adxtail, axtbc);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(axtbclen, axtbc, 2 * adx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adxtail, _8), _8, bdy, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, adxtail, _8), _8, -cdy, _16c), _16c, _32, _48), _48);\n    }\n    if (adytail !== 0) {\n        aytbclen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adytail, aytbc);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(aytbclen, aytbc, 2 * ady, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, adytail, _8), _8, cdx, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adytail, _8), _8, -bdx, _16c), _16c, _32, _48), _48);\n    }\n    if (bdxtail !== 0) {\n        bxtcalen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdxtail, bxtca);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bxtcalen, bxtca, 2 * bdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdxtail, _8), _8, cdy, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, bdxtail, _8), _8, -ady, _16c), _16c, _32, _48), _48);\n    }\n    if (bdytail !== 0) {\n        bytcalen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdytail, bytca);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bytcalen, bytca, 2 * bdy, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, bdytail, _8), _8, adx, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdytail, _8), _8, -cdx, _16c), _16c, _32, _48), _48);\n    }\n    if (cdxtail !== 0) {\n        cxtablen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdxtail, cxtab);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cxtablen, cxtab, 2 * cdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdxtail, _8), _8, ady, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, cdxtail, _8), _8, -bdy, _16c), _16c, _32, _48), _48);\n    }\n    if (cdytail !== 0) {\n        cytablen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdytail, cytab);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cytablen, cytab, 2 * cdy, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, cdytail, _8), _8, bdx, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdytail, _8), _8, -adx, _16c), _16c, _32, _48), _48);\n    }\n    if (adxtail !== 0 || adytail !== 0) {\n        if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n            s1 = bdxtail * cdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n            bhi = c - (c - cdy);\n            blo = cdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            s1 = cdxtail * -bdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * -bdy;\n            bhi = c - (c - -bdy);\n            blo = -bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * -bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * -bdytail;\n            bhi = c - (c - -bdytail);\n            blo = -bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            bctlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, bct);\n            s1 = bdxtail * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdxtail * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            bctt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            bctt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            bctt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            bctt[3] = u3;\n            bcttlen = 4;\n        } else {\n            bct[0] = 0;\n            bctlen = 1;\n            bctt[0] = 0;\n            bcttlen = 1;\n        }\n        if (adxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(axtbclen, axtbc, adxtail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * adx, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bcttlen, bctt, adxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * adx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, adxtail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, adxtail, _32), _32, _32b, _64), _64);\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adxtail, _8), _8, bdytail, _16), _16);\n            }\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, -adxtail, _8), _8, cdytail, _16), _16);\n            }\n        }\n        if (adytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(aytbclen, aytbc, adytail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * ady, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bcttlen, bctt, adytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * ady, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, adytail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, adytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    if (bdxtail !== 0 || bdytail !== 0) {\n        if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n            s1 = cdxtail * ady;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n            bhi = c - (c - ady);\n            blo = ady - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -cdy;\n            n0 = -cdytail;\n            s1 = adxtail * n1;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * n0;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            catlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, cat);\n            s1 = cdxtail * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adxtail * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            catt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            catt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            catt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            catt[3] = u3;\n            cattlen = 4;\n        } else {\n            cat[0] = 0;\n            catlen = 1;\n            catt[0] = 0;\n            cattlen = 1;\n        }\n        if (bdxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bxtcalen, bxtca, bdxtail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * bdx, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cattlen, catt, bdxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * bdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, bdxtail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, bdxtail, _32), _32, _32b, _64), _64);\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdxtail, _8), _8, cdytail, _16), _16);\n            }\n            if (adytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, -bdxtail, _8), _8, adytail, _16), _16);\n            }\n        }\n        if (bdytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bytcalen, bytca, bdytail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * bdy, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cattlen, catt, bdytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * bdy, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, bdytail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, bdytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    if (cdxtail !== 0 || cdytail !== 0) {\n        if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n            s1 = adxtail * bdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n            bhi = c - (c - bdy);\n            blo = bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -ady;\n            n0 = -adytail;\n            s1 = bdxtail * n1;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * n0;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            abtlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, abt);\n            s1 = adxtail * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdxtail * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            abtt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            abtt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            abtt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            abtt[3] = u3;\n            abttlen = 4;\n        } else {\n            abt[0] = 0;\n            abtlen = 1;\n            abtt[0] = 0;\n            abttlen = 1;\n        }\n        if (cdxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cxtablen, cxtab, cdxtail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * cdx, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abttlen, abtt, cdxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * cdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, cdxtail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, cdxtail, _32), _32, _32b, _64), _64);\n            if (adytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdxtail, _8), _8, adytail, _16), _16);\n            }\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, -cdxtail, _8), _8, bdytail, _16), _16);\n            }\n        }\n        if (cdytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cytablen, cytab, cdytail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * cdy, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abttlen, abtt, cdytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * cdy, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, cdytail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, cdytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    return fin[finlen - 1];\n}\nfunction incircle(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n    const alift = adx * adx + ady * ady;\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n    const blift = bdx * bdx + bdy * bdy;\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n    const clift = cdx * cdx + cdy * cdy;\n    const det = alift * (bdxcdy - cdxbdy) + blift * (cdxady - adxcdy) + clift * (adxbdy - bdxady);\n    const permanent = (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * alift + (Math.abs(cdxady) + Math.abs(adxcdy)) * blift + (Math.abs(adxbdy) + Math.abs(bdxady)) * clift;\n    const errbound = iccerrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent);\n}\nfunction incirclefast(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const ady = ay - dy;\n    const bdx = bx - dx;\n    const bdy = by - dy;\n    const cdx = cx - dx;\n    const cdy = cy - dy;\n    const abdet = adx * bdy - bdx * ady;\n    const bcdet = bdx * cdy - cdx * bdy;\n    const cadet = cdx * ady - adx * cdy;\n    const alift = adx * adx + ady * ady;\n    const blift = bdx * bdx + bdy * bdy;\n    const clift = cdx * cdx + cdy * cdy;\n    return alift * bcdet + blift * cadet + clift * abdet;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcm9idXN0LXByZWRpY2F0ZXMvZXNtL2luY2lyY2xlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRztBQUVsRyxNQUFNUSxlQUFlLENBQUMsS0FBSyxLQUFLUiw2Q0FBTSxJQUFLQSw2Q0FBT0E7QUFDbEQsTUFBTVMsZUFBZSxDQUFDLElBQUksS0FBS1QsNkNBQU0sSUFBS0EsNkNBQU9BO0FBQ2pELE1BQU1VLGVBQWUsQ0FBQyxLQUFLLE1BQU1WLDZDQUFNLElBQUtBLDZDQUFPQSxHQUFHQSw2Q0FBT0E7QUFFN0QsTUFBTVcsS0FBS1AsNkNBQUdBLENBQUM7QUFDZixNQUFNUSxLQUFLUiw2Q0FBR0EsQ0FBQztBQUNmLE1BQU1TLEtBQUtULDZDQUFHQSxDQUFDO0FBQ2YsTUFBTVUsS0FBS1YsNkNBQUdBLENBQUM7QUFDZixNQUFNVyxLQUFLWCw2Q0FBR0EsQ0FBQztBQUNmLE1BQU1ZLEtBQUtaLDZDQUFHQSxDQUFDO0FBQ2YsTUFBTWEsSUFBSWIsNkNBQUdBLENBQUM7QUFDZCxNQUFNYyxJQUFJZCw2Q0FBR0EsQ0FBQztBQUNkLE1BQU1lLFFBQVFmLDZDQUFHQSxDQUFDO0FBQ2xCLE1BQU1nQixRQUFRaEIsNkNBQUdBLENBQUM7QUFDbEIsTUFBTWlCLFFBQVFqQiw2Q0FBR0EsQ0FBQztBQUNsQixNQUFNa0IsUUFBUWxCLDZDQUFHQSxDQUFDO0FBQ2xCLE1BQU1tQixRQUFRbkIsNkNBQUdBLENBQUM7QUFDbEIsTUFBTW9CLFFBQVFwQiw2Q0FBR0EsQ0FBQztBQUNsQixNQUFNcUIsTUFBTXJCLDZDQUFHQSxDQUFDO0FBQ2hCLE1BQU1zQixNQUFNdEIsNkNBQUdBLENBQUM7QUFDaEIsTUFBTXVCLE1BQU12Qiw2Q0FBR0EsQ0FBQztBQUNoQixNQUFNd0IsT0FBT3hCLDZDQUFHQSxDQUFDO0FBQ2pCLE1BQU15QixPQUFPekIsNkNBQUdBLENBQUM7QUFDakIsTUFBTTBCLE9BQU8xQiw2Q0FBR0EsQ0FBQztBQUVqQixNQUFNMkIsS0FBSzNCLDZDQUFHQSxDQUFDO0FBQ2YsTUFBTTRCLE1BQU01Qiw2Q0FBR0EsQ0FBQztBQUNoQixNQUFNNkIsT0FBTzdCLDZDQUFHQSxDQUFDO0FBQ2pCLE1BQU04QixPQUFPOUIsNkNBQUdBLENBQUM7QUFDakIsTUFBTStCLE1BQU0vQiw2Q0FBR0EsQ0FBQztBQUNoQixNQUFNZ0MsT0FBT2hDLDZDQUFHQSxDQUFDO0FBQ2pCLE1BQU1pQyxNQUFNakMsNkNBQUdBLENBQUM7QUFDaEIsTUFBTWtDLE1BQU1sQyw2Q0FBR0EsQ0FBQztBQUVoQixJQUFJbUMsTUFBTW5DLDZDQUFHQSxDQUFDO0FBQ2QsSUFBSW9DLE9BQU9wQyw2Q0FBR0EsQ0FBQztBQUVmLFNBQVNxQyxPQUFPQyxNQUFNLEVBQUVDLENBQUMsRUFBRUMsSUFBSTtJQUMzQkYsU0FBU3JDLDZDQUFHQSxDQUFDcUMsUUFBUUgsS0FBS0ksR0FBR0MsTUFBTUo7SUFDbkMsTUFBTUssTUFBTU47SUFBS0EsTUFBTUM7SUFBTUEsT0FBT0s7SUFDcEMsT0FBT0g7QUFDWDtBQUVBLFNBQVNJLGNBQWNDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxTQUFTO0lBQzVELElBQUliO0lBQ0osSUFBSWMsU0FBU0MsU0FBU0MsU0FBU0MsU0FBU0MsU0FBU0M7SUFDakQsSUFBSUMsVUFBVUMsVUFBVUMsVUFBVUMsVUFBVUMsVUFBVUM7SUFDdEQsSUFBSUMsUUFBUUMsUUFBUUM7SUFDcEIsSUFBSUMsU0FBU0MsU0FBU0M7SUFDdEIsSUFBSUMsSUFBSUM7SUFFUixJQUFJQyxPQUFPQyxHQUFHQyxLQUFLQyxLQUFLQyxLQUFLQyxLQUFLQyxJQUFJQyxJQUFJQyxJQUFJQyxJQUFJQyxJQUFJQyxJQUFJQyxJQUFJQztJQUU5RCxNQUFNQyxNQUFNM0MsS0FBS007SUFDakIsTUFBTXNDLE1BQU0xQyxLQUFLSTtJQUNqQixNQUFNdUMsTUFBTXpDLEtBQUtFO0lBQ2pCLE1BQU13QyxNQUFNN0MsS0FBS007SUFDakIsTUFBTXdDLE1BQU01QyxLQUFLSTtJQUNqQixNQUFNeUMsTUFBTTNDLEtBQUtFO0lBRWpCK0IsS0FBS00sTUFBTUk7SUFDWGxCLElBQUk1RSw4Q0FBUUEsR0FBRzBGO0lBQ2ZiLE1BQU1ELElBQUtBLENBQUFBLElBQUljLEdBQUU7SUFDakJaLE1BQU1ZLE1BQU1iO0lBQ1pELElBQUk1RSw4Q0FBUUEsR0FBRzhGO0lBQ2ZmLE1BQU1ILElBQUtBLENBQUFBLElBQUlrQixHQUFFO0lBQ2pCZCxNQUFNYyxNQUFNZjtJQUNaTSxLQUFLUCxNQUFNRSxNQUFPSSxDQUFBQSxLQUFLUCxNQUFNRSxNQUFNRCxNQUFNQyxNQUFNRixNQUFNRyxHQUFFO0lBQ3ZETSxLQUFLSyxNQUFNRTtJQUNYakIsSUFBSTVFLDhDQUFRQSxHQUFHMkY7SUFDZmQsTUFBTUQsSUFBS0EsQ0FBQUEsSUFBSWUsR0FBRTtJQUNqQmIsTUFBTWEsTUFBTWQ7SUFDWkQsSUFBSTVFLDhDQUFRQSxHQUFHNkY7SUFDZmQsTUFBTUgsSUFBS0EsQ0FBQUEsSUFBSWlCLEdBQUU7SUFDakJiLE1BQU1hLE1BQU1kO0lBQ1pRLEtBQUtULE1BQU1FLE1BQU9NLENBQUFBLEtBQUtULE1BQU1FLE1BQU1ELE1BQU1DLE1BQU1GLE1BQU1HLEdBQUU7SUFDdkRDLEtBQUtJLEtBQUtFO0lBQ1ZaLFFBQVFVLEtBQUtKO0lBQ2J2RSxFQUFFLENBQUMsRUFBRSxHQUFHMkUsS0FBTUosQ0FBQUEsS0FBS04sS0FBSSxJQUFNQSxDQUFBQSxRQUFRWSxFQUFDO0lBQ3RDTCxLQUFLRSxLQUFLSDtJQUNWTixRQUFRTyxLQUFLRTtJQUNiRCxLQUFLQyxLQUFNRixDQUFBQSxLQUFLUCxLQUFJLElBQU1NLENBQUFBLEtBQUtOLEtBQUk7SUFDbkNNLEtBQUtFLEtBQUtHO0lBQ1ZYLFFBQVFRLEtBQUtGO0lBQ2J2RSxFQUFFLENBQUMsRUFBRSxHQUFHeUUsS0FBTUYsQ0FBQUEsS0FBS04sS0FBSSxJQUFNQSxDQUFBQSxRQUFRVyxFQUFDO0lBQ3RDRSxLQUFLTixLQUFLRDtJQUNWTixRQUFRYSxLQUFLTjtJQUNieEUsRUFBRSxDQUFDLEVBQUUsR0FBR3dFLEtBQU1NLENBQUFBLEtBQUtiLEtBQUksSUFBTU0sQ0FBQUEsS0FBS04sS0FBSTtJQUN0Q2pFLEVBQUUsQ0FBQyxFQUFFLEdBQUc4RTtJQUNSSixLQUFLTyxNQUFNQztJQUNYaEIsSUFBSTVFLDhDQUFRQSxHQUFHMkY7SUFDZmQsTUFBTUQsSUFBS0EsQ0FBQUEsSUFBSWUsR0FBRTtJQUNqQmIsTUFBTWEsTUFBTWQ7SUFDWkQsSUFBSTVFLDhDQUFRQSxHQUFHNEY7SUFDZmIsTUFBTUgsSUFBS0EsQ0FBQUEsSUFBSWdCLEdBQUU7SUFDakJaLE1BQU1ZLE1BQU1iO0lBQ1pNLEtBQUtQLE1BQU1FLE1BQU9JLENBQUFBLEtBQUtQLE1BQU1FLE1BQU1ELE1BQU1DLE1BQU1GLE1BQU1HLEdBQUU7SUFDdkRNLEtBQUtHLE1BQU1LO0lBQ1hsQixJQUFJNUUsOENBQVFBLEdBQUd5RjtJQUNmWixNQUFNRCxJQUFLQSxDQUFBQSxJQUFJYSxHQUFFO0lBQ2pCWCxNQUFNVyxNQUFNWjtJQUNaRCxJQUFJNUUsOENBQVFBLEdBQUc4RjtJQUNmZixNQUFNSCxJQUFLQSxDQUFBQSxJQUFJa0IsR0FBRTtJQUNqQmQsTUFBTWMsTUFBTWY7SUFDWlEsS0FBS1QsTUFBTUUsTUFBT00sQ0FBQUEsS0FBS1QsTUFBTUUsTUFBTUQsTUFBTUMsTUFBTUYsTUFBTUcsR0FBRTtJQUN2REMsS0FBS0ksS0FBS0U7SUFDVlosUUFBUVUsS0FBS0o7SUFDYnRFLEVBQUUsQ0FBQyxFQUFFLEdBQUcwRSxLQUFNSixDQUFBQSxLQUFLTixLQUFJLElBQU1BLENBQUFBLFFBQVFZLEVBQUM7SUFDdENMLEtBQUtFLEtBQUtIO0lBQ1ZOLFFBQVFPLEtBQUtFO0lBQ2JELEtBQUtDLEtBQU1GLENBQUFBLEtBQUtQLEtBQUksSUFBTU0sQ0FBQUEsS0FBS04sS0FBSTtJQUNuQ00sS0FBS0UsS0FBS0c7SUFDVlgsUUFBUVEsS0FBS0Y7SUFDYnRFLEVBQUUsQ0FBQyxFQUFFLEdBQUd3RSxLQUFNRixDQUFBQSxLQUFLTixLQUFJLElBQU1BLENBQUFBLFFBQVFXLEVBQUM7SUFDdENFLEtBQUtOLEtBQUtEO0lBQ1ZOLFFBQVFhLEtBQUtOO0lBQ2J2RSxFQUFFLENBQUMsRUFBRSxHQUFHdUUsS0FBTU0sQ0FBQUEsS0FBS2IsS0FBSSxJQUFNTSxDQUFBQSxLQUFLTixLQUFJO0lBQ3RDaEUsRUFBRSxDQUFDLEVBQUUsR0FBRzZFO0lBQ1JKLEtBQUtLLE1BQU1JO0lBQ1hqQixJQUFJNUUsOENBQVFBLEdBQUd5RjtJQUNmWixNQUFNRCxJQUFLQSxDQUFBQSxJQUFJYSxHQUFFO0lBQ2pCWCxNQUFNVyxNQUFNWjtJQUNaRCxJQUFJNUUsOENBQVFBLEdBQUc2RjtJQUNmZCxNQUFNSCxJQUFLQSxDQUFBQSxJQUFJaUIsR0FBRTtJQUNqQmIsTUFBTWEsTUFBTWQ7SUFDWk0sS0FBS1AsTUFBTUUsTUFBT0ksQ0FBQUEsS0FBS1AsTUFBTUUsTUFBTUQsTUFBTUMsTUFBTUYsTUFBTUcsR0FBRTtJQUN2RE0sS0FBS0ksTUFBTUU7SUFDWGhCLElBQUk1RSw4Q0FBUUEsR0FBRzBGO0lBQ2ZiLE1BQU1ELElBQUtBLENBQUFBLElBQUljLEdBQUU7SUFDakJaLE1BQU1ZLE1BQU1iO0lBQ1pELElBQUk1RSw4Q0FBUUEsR0FBRzRGO0lBQ2ZiLE1BQU1ILElBQUtBLENBQUFBLElBQUlnQixHQUFFO0lBQ2pCWixNQUFNWSxNQUFNYjtJQUNaUSxLQUFLVCxNQUFNRSxNQUFPTSxDQUFBQSxLQUFLVCxNQUFNRSxNQUFNRCxNQUFNQyxNQUFNRixNQUFNRyxHQUFFO0lBQ3ZEQyxLQUFLSSxLQUFLRTtJQUNWWixRQUFRVSxLQUFLSjtJQUNickUsRUFBRSxDQUFDLEVBQUUsR0FBR3lFLEtBQU1KLENBQUFBLEtBQUtOLEtBQUksSUFBTUEsQ0FBQUEsUUFBUVksRUFBQztJQUN0Q0wsS0FBS0UsS0FBS0g7SUFDVk4sUUFBUU8sS0FBS0U7SUFDYkQsS0FBS0MsS0FBTUYsQ0FBQUEsS0FBS1AsS0FBSSxJQUFNTSxDQUFBQSxLQUFLTixLQUFJO0lBQ25DTSxLQUFLRSxLQUFLRztJQUNWWCxRQUFRUSxLQUFLRjtJQUNickUsRUFBRSxDQUFDLEVBQUUsR0FBR3VFLEtBQU1GLENBQUFBLEtBQUtOLEtBQUksSUFBTUEsQ0FBQUEsUUFBUVcsRUFBQztJQUN0Q0UsS0FBS04sS0FBS0Q7SUFDVk4sUUFBUWEsS0FBS047SUFDYnRFLEVBQUUsQ0FBQyxFQUFFLEdBQUdzRSxLQUFNTSxDQUFBQSxLQUFLYixLQUFJLElBQU1NLENBQUFBLEtBQUtOLEtBQUk7SUFDdEMvRCxFQUFFLENBQUMsRUFBRSxHQUFHNEU7SUFFUi9DLFNBQVNyQyw2Q0FBR0EsQ0FDUkEsNkNBQUdBLENBQ0NBLDZDQUFHQSxDQUNDRSwrQ0FBS0EsQ0FBQ0EsK0NBQUtBLENBQUMsR0FBR0ksSUFBSStFLEtBQUszRCxLQUFLQSxJQUFJMkQsS0FBSzFELE1BQU1BLEtBQzVDekIsK0NBQUtBLENBQUNBLCtDQUFLQSxDQUFDLEdBQUdJLElBQUlrRixLQUFLOUQsS0FBS0EsSUFBSThELEtBQUs1RCxPQUFPQSxNQUFNRSxNQUFNQSxLQUM3RDlCLDZDQUFHQSxDQUNDRSwrQ0FBS0EsQ0FBQ0EsK0NBQUtBLENBQUMsR0FBR0ssSUFBSStFLEtBQUs1RCxLQUFLQSxJQUFJNEQsS0FBSzNELE1BQU1BLEtBQzVDekIsK0NBQUtBLENBQUNBLCtDQUFLQSxDQUFDLEdBQUdLLElBQUlrRixLQUFLL0QsS0FBS0EsSUFBSStELEtBQUs3RCxPQUFPQSxNQUFNRyxPQUFPQSxNQUFNRSxNQUFNQSxLQUM5RWpDLDZDQUFHQSxDQUNDRSwrQ0FBS0EsQ0FBQ0EsK0NBQUtBLENBQUMsR0FBR00sSUFBSStFLEtBQUs3RCxLQUFLQSxJQUFJNkQsS0FBSzVELE1BQU1BLEtBQzVDekIsK0NBQUtBLENBQUNBLCtDQUFLQSxDQUFDLEdBQUdNLElBQUlrRixLQUFLaEUsS0FBS0EsSUFBSWdFLEtBQUs5RCxPQUFPQSxNQUFNRSxNQUFNQSxLQUFLSTtJQUV0RSxJQUFJeUQsTUFBTTdGLGtEQUFRQSxDQUFDdUMsUUFBUUg7SUFDM0IsSUFBSTBELFdBQVd4RixlQUFlOEM7SUFDOUIsSUFBSXlDLE9BQU9DLFlBQVksQ0FBQ0QsT0FBT0MsVUFBVTtRQUNyQyxPQUFPRDtJQUNYO0lBRUFwQixRQUFRN0IsS0FBSzJDO0lBQ2JsQyxVQUFVVCxLQUFNMkMsQ0FBQUEsTUFBTWQsS0FBSSxJQUFNQSxDQUFBQSxRQUFRdkIsRUFBQztJQUN6Q3VCLFFBQVE1QixLQUFLNkM7SUFDYmxDLFVBQVVYLEtBQU02QyxDQUFBQSxNQUFNakIsS0FBSSxJQUFNQSxDQUFBQSxRQUFRdEIsRUFBQztJQUN6Q3NCLFFBQVEzQixLQUFLMEM7SUFDYmxDLFVBQVVSLEtBQU0wQyxDQUFBQSxNQUFNZixLQUFJLElBQU1BLENBQUFBLFFBQVF2QixFQUFDO0lBQ3pDdUIsUUFBUTFCLEtBQUs0QztJQUNibEMsVUFBVVYsS0FBTTRDLENBQUFBLE1BQU1sQixLQUFJLElBQU1BLENBQUFBLFFBQVF0QixFQUFDO0lBQ3pDc0IsUUFBUXpCLEtBQUt5QztJQUNibEMsVUFBVVAsS0FBTXlDLENBQUFBLE1BQU1oQixLQUFJLElBQU1BLENBQUFBLFFBQVF2QixFQUFDO0lBQ3pDdUIsUUFBUXhCLEtBQUsyQztJQUNibEMsVUFBVVQsS0FBTTJDLENBQUFBLE1BQU1uQixLQUFJLElBQU1BLENBQUFBLFFBQVF0QixFQUFDO0lBQ3pDLElBQUlFLFlBQVksS0FBS0MsWUFBWSxLQUFLQyxZQUFZLEtBQUtDLFlBQVksS0FBS0MsWUFBWSxLQUFLQyxZQUFZLEdBQUc7UUFDcEcsT0FBT21DO0lBQ1g7SUFFQUMsV0FBV3ZGLGVBQWU2QyxZQUFZckQsb0RBQWNBLEdBQUdnRyxLQUFLQyxHQUFHLENBQUNIO0lBQ2hFQSxPQUFPLENBQUVOLE1BQU1BLE1BQU1HLE1BQU1BLEdBQUUsSUFBTSxPQUFPaEMsVUFBVWtDLE1BQU10QyxVQUFZcUMsQ0FBQUEsTUFBTXBDLFVBQVVrQyxNQUFNaEMsT0FBTSxDQUFDLElBQy9GLElBQUs4QixDQUFBQSxNQUFNbEMsVUFBVXFDLE1BQU1sQyxPQUFNLElBQU1nQyxDQUFBQSxNQUFNSSxNQUFNRCxNQUFNRixHQUFFLElBQzFELEVBQUNELE1BQU1BLE1BQU1HLE1BQU1BLEdBQUUsSUFBTSxPQUFPbkMsVUFBVWtDLE1BQU1uQyxVQUFZcUMsQ0FBQUEsTUFBTXZDLFVBQVVrQyxNQUFNN0IsT0FBTSxDQUFDLElBQzVGLElBQUs4QixDQUFBQSxNQUFNbEMsVUFBVXFDLE1BQU1sQyxPQUFNLElBQU1nQyxDQUFBQSxNQUFNQyxNQUFNRSxNQUFNTCxHQUFFLENBQUMsSUFDM0QsRUFBQ0UsTUFBTUEsTUFBTUcsTUFBTUEsR0FBRSxJQUFNLE9BQU9uQyxVQUFVa0MsTUFBTXRDLFVBQVlxQyxDQUFBQSxNQUFNcEMsVUFBVWtDLE1BQU1oQyxPQUFNLENBQUMsSUFDNUYsSUFBS2lDLENBQUFBLE1BQU1sQyxVQUFVcUMsTUFBTWxDLE9BQU0sSUFBTTZCLENBQUFBLE1BQU1JLE1BQU1ELE1BQU1GLEdBQUUsQ0FBQztJQUVoRSxJQUFJSyxPQUFPQyxZQUFZLENBQUNELE9BQU9DLFVBQVU7UUFDckMsT0FBT0Q7SUFDWDtJQUVBLElBQUl2QyxZQUFZLEtBQUtHLFlBQVksS0FBS0YsWUFBWSxLQUFLRyxZQUFZLEdBQUc7UUFDbEV3QixLQUFLSyxNQUFNQTtRQUNYYixJQUFJNUUsOENBQVFBLEdBQUd5RjtRQUNmWixNQUFNRCxJQUFLQSxDQUFBQSxJQUFJYSxHQUFFO1FBQ2pCWCxNQUFNVyxNQUFNWjtRQUNaUSxLQUFLUCxNQUFNQSxNQUFPTSxDQUFBQSxLQUFLUCxNQUFNQSxNQUFNLENBQUNBLE1BQU1BLEdBQUUsSUFBS0MsR0FBRTtRQUNuRFEsS0FBS00sTUFBTUE7UUFDWGhCLElBQUk1RSw4Q0FBUUEsR0FBRzRGO1FBQ2ZmLE1BQU1ELElBQUtBLENBQUFBLElBQUlnQixHQUFFO1FBQ2pCZCxNQUFNYyxNQUFNZjtRQUNaVSxLQUFLVCxNQUFNQSxNQUFPUSxDQUFBQSxLQUFLVCxNQUFNQSxNQUFNLENBQUNBLE1BQU1BLEdBQUUsSUFBS0MsR0FBRTtRQUNuREcsS0FBS0ksS0FBS0U7UUFDVlosUUFBUU0sS0FBS0k7UUFDYnhFLEVBQUUsQ0FBQyxFQUFFLEdBQUd3RSxLQUFNSixDQUFBQSxLQUFLTixLQUFJLElBQU1ZLENBQUFBLEtBQUtaLEtBQUk7UUFDdENPLEtBQUtFLEtBQUtIO1FBQ1ZOLFFBQVFPLEtBQUtFO1FBQ2JELEtBQUtDLEtBQU1GLENBQUFBLEtBQUtQLEtBQUksSUFBTU0sQ0FBQUEsS0FBS04sS0FBSTtRQUNuQ00sS0FBS0UsS0FBS0c7UUFDVlgsUUFBUU0sS0FBS0U7UUFDYnRFLEVBQUUsQ0FBQyxFQUFFLEdBQUdzRSxLQUFNRixDQUFBQSxLQUFLTixLQUFJLElBQU1XLENBQUFBLEtBQUtYLEtBQUk7UUFDdENhLEtBQUtOLEtBQUtEO1FBQ1ZOLFFBQVFhLEtBQUtOO1FBQ2JyRSxFQUFFLENBQUMsRUFBRSxHQUFHcUUsS0FBTU0sQ0FBQUEsS0FBS2IsS0FBSSxJQUFNTSxDQUFBQSxLQUFLTixLQUFJO1FBQ3RDOUQsRUFBRSxDQUFDLEVBQUUsR0FBRzJFO0lBQ1o7SUFDQSxJQUFJL0IsWUFBWSxLQUFLRyxZQUFZLEtBQUtMLFlBQVksS0FBS0csWUFBWSxHQUFHO1FBQ2xFMEIsS0FBS00sTUFBTUE7UUFDWGQsSUFBSTVFLDhDQUFRQSxHQUFHMEY7UUFDZmIsTUFBTUQsSUFBS0EsQ0FBQUEsSUFBSWMsR0FBRTtRQUNqQlosTUFBTVksTUFBTWI7UUFDWlEsS0FBS1AsTUFBTUEsTUFBT00sQ0FBQUEsS0FBS1AsTUFBTUEsTUFBTSxDQUFDQSxNQUFNQSxHQUFFLElBQUtDLEdBQUU7UUFDbkRRLEtBQUtPLE1BQU1BO1FBQ1hqQixJQUFJNUUsOENBQVFBLEdBQUc2RjtRQUNmaEIsTUFBTUQsSUFBS0EsQ0FBQUEsSUFBSWlCLEdBQUU7UUFDakJmLE1BQU1lLE1BQU1oQjtRQUNaVSxLQUFLVCxNQUFNQSxNQUFPUSxDQUFBQSxLQUFLVCxNQUFNQSxNQUFNLENBQUNBLE1BQU1BLEdBQUUsSUFBS0MsR0FBRTtRQUNuREcsS0FBS0ksS0FBS0U7UUFDVlosUUFBUU0sS0FBS0k7UUFDYnZFLEVBQUUsQ0FBQyxFQUFFLEdBQUd1RSxLQUFNSixDQUFBQSxLQUFLTixLQUFJLElBQU1ZLENBQUFBLEtBQUtaLEtBQUk7UUFDdENPLEtBQUtFLEtBQUtIO1FBQ1ZOLFFBQVFPLEtBQUtFO1FBQ2JELEtBQUtDLEtBQU1GLENBQUFBLEtBQUtQLEtBQUksSUFBTU0sQ0FBQUEsS0FBS04sS0FBSTtRQUNuQ00sS0FBS0UsS0FBS0c7UUFDVlgsUUFBUU0sS0FBS0U7UUFDYnJFLEVBQUUsQ0FBQyxFQUFFLEdBQUdxRSxLQUFNRixDQUFBQSxLQUFLTixLQUFJLElBQU1XLENBQUFBLEtBQUtYLEtBQUk7UUFDdENhLEtBQUtOLEtBQUtEO1FBQ1ZOLFFBQVFhLEtBQUtOO1FBQ2JwRSxFQUFFLENBQUMsRUFBRSxHQUFHb0UsS0FBTU0sQ0FBQUEsS0FBS2IsS0FBSSxJQUFNTSxDQUFBQSxLQUFLTixLQUFJO1FBQ3RDN0QsRUFBRSxDQUFDLEVBQUUsR0FBRzBFO0lBQ1o7SUFDQSxJQUFJakMsWUFBWSxLQUFLRyxZQUFZLEtBQUtGLFlBQVksS0FBS0csWUFBWSxHQUFHO1FBQ2xFeUIsS0FBS08sTUFBTUE7UUFDWGYsSUFBSTVFLDhDQUFRQSxHQUFHMkY7UUFDZmQsTUFBTUQsSUFBS0EsQ0FBQUEsSUFBSWUsR0FBRTtRQUNqQmIsTUFBTWEsTUFBTWQ7UUFDWlEsS0FBS1AsTUFBTUEsTUFBT00sQ0FBQUEsS0FBS1AsTUFBTUEsTUFBTSxDQUFDQSxNQUFNQSxHQUFFLElBQUtDLEdBQUU7UUFDbkRRLEtBQUtRLE1BQU1BO1FBQ1hsQixJQUFJNUUsOENBQVFBLEdBQUc4RjtRQUNmakIsTUFBTUQsSUFBS0EsQ0FBQUEsSUFBSWtCLEdBQUU7UUFDakJoQixNQUFNZ0IsTUFBTWpCO1FBQ1pVLEtBQUtULE1BQU1BLE1BQU9RLENBQUFBLEtBQUtULE1BQU1BLE1BQU0sQ0FBQ0EsTUFBTUEsR0FBRSxJQUFLQyxHQUFFO1FBQ25ERyxLQUFLSSxLQUFLRTtRQUNWWixRQUFRTSxLQUFLSTtRQUNidEUsRUFBRSxDQUFDLEVBQUUsR0FBR3NFLEtBQU1KLENBQUFBLEtBQUtOLEtBQUksSUFBTVksQ0FBQUEsS0FBS1osS0FBSTtRQUN0Q08sS0FBS0UsS0FBS0g7UUFDVk4sUUFBUU8sS0FBS0U7UUFDYkQsS0FBS0MsS0FBTUYsQ0FBQUEsS0FBS1AsS0FBSSxJQUFNTSxDQUFBQSxLQUFLTixLQUFJO1FBQ25DTSxLQUFLRSxLQUFLRztRQUNWWCxRQUFRTSxLQUFLRTtRQUNicEUsRUFBRSxDQUFDLEVBQUUsR0FBR29FLEtBQU1GLENBQUFBLEtBQUtOLEtBQUksSUFBTVcsQ0FBQUEsS0FBS1gsS0FBSTtRQUN0Q2EsS0FBS04sS0FBS0Q7UUFDVk4sUUFBUWEsS0FBS047UUFDYm5FLEVBQUUsQ0FBQyxFQUFFLEdBQUdtRSxLQUFNTSxDQUFBQSxLQUFLYixLQUFJLElBQU1NLENBQUFBLEtBQUtOLEtBQUk7UUFDdEM1RCxFQUFFLENBQUMsRUFBRSxHQUFHeUU7SUFDWjtJQUVBLElBQUlqQyxZQUFZLEdBQUc7UUFDZk0sV0FBV3ZELCtDQUFLQSxDQUFDLEdBQUdJLElBQUk2QyxTQUFTckM7UUFDakN1QixTQUFTRCxPQUFPQyxRQUFRcEMsbURBQVNBLENBQzdCQywrQ0FBS0EsQ0FBQ3VELFVBQVUzQyxPQUFPLElBQUl1RSxLQUFLMUQsTUFBTUEsS0FDdEN6QiwrQ0FBS0EsQ0FBQ0EsK0NBQUtBLENBQUMsR0FBR1MsSUFBSXdDLFNBQVN6QixLQUFLQSxJQUFJK0QsS0FBSzdELE9BQU9BLE1BQ2pEMUIsK0NBQUtBLENBQUNBLCtDQUFLQSxDQUFDLEdBQUdRLElBQUl5QyxTQUFTekIsS0FBS0EsSUFBSSxDQUFDZ0UsS0FBSzdELE9BQU9BLE1BQU1DLEtBQUtFLE1BQU1BO0lBQzNFO0lBQ0EsSUFBSXNCLFlBQVksR0FBRztRQUNmSSxXQUFXeEQsK0NBQUtBLENBQUMsR0FBR0ksSUFBSWdELFNBQVN2QztRQUNqQ3NCLFNBQVNELE9BQU9DLFFBQVFwQyxtREFBU0EsQ0FDN0JDLCtDQUFLQSxDQUFDd0QsVUFBVTNDLE9BQU8sSUFBSXlFLEtBQUs3RCxNQUFNQSxLQUN0Q3pCLCtDQUFLQSxDQUFDQSwrQ0FBS0EsQ0FBQyxHQUFHUSxJQUFJNEMsU0FBUzVCLEtBQUtBLElBQUk2RCxLQUFLM0QsT0FBT0EsTUFDakQxQiwrQ0FBS0EsQ0FBQ0EsK0NBQUtBLENBQUMsR0FBR1MsSUFBSTJDLFNBQVM1QixLQUFLQSxJQUFJLENBQUM0RCxLQUFLekQsT0FBT0EsTUFBTUMsS0FBS0UsTUFBTUE7SUFDM0U7SUFDQSxJQUFJb0IsWUFBWSxHQUFHO1FBQ2ZPLFdBQVd6RCwrQ0FBS0EsQ0FBQyxHQUFHSyxJQUFJNkMsU0FBU3BDO1FBQ2pDcUIsU0FBU0QsT0FBT0MsUUFBUXBDLG1EQUFTQSxDQUM3QkMsK0NBQUtBLENBQUN5RCxVQUFVM0MsT0FBTyxJQUFJc0UsS0FBSzNELE1BQU1BLEtBQ3RDekIsK0NBQUtBLENBQUNBLCtDQUFLQSxDQUFDLEdBQUdPLElBQUkyQyxTQUFTMUIsS0FBS0EsSUFBSWdFLEtBQUs5RCxPQUFPQSxNQUNqRDFCLCtDQUFLQSxDQUFDQSwrQ0FBS0EsQ0FBQyxHQUFHUyxJQUFJeUMsU0FBUzFCLEtBQUtBLElBQUksQ0FBQzhELEtBQUszRCxPQUFPQSxNQUFNQyxLQUFLRSxNQUFNQTtJQUMzRTtJQUNBLElBQUl1QixZQUFZLEdBQUc7UUFDZkssV0FBVzFELCtDQUFLQSxDQUFDLEdBQUdLLElBQUlnRCxTQUFTdEM7UUFDakNvQixTQUFTRCxPQUFPQyxRQUFRcEMsbURBQVNBLENBQzdCQywrQ0FBS0EsQ0FBQzBELFVBQVUzQyxPQUFPLElBQUl3RSxLQUFLOUQsTUFBTUEsS0FDdEN6QiwrQ0FBS0EsQ0FBQ0EsK0NBQUtBLENBQUMsR0FBR1MsSUFBSTRDLFNBQVM3QixLQUFLQSxJQUFJMkQsS0FBS3pELE9BQU9BLE1BQ2pEMUIsK0NBQUtBLENBQUNBLCtDQUFLQSxDQUFDLEdBQUdPLElBQUk4QyxTQUFTN0IsS0FBS0EsSUFBSSxDQUFDNkQsS0FBSzFELE9BQU9BLE1BQU1DLEtBQUtFLE1BQU1BO0lBQzNFO0lBQ0EsSUFBSXFCLFlBQVksR0FBRztRQUNmUSxXQUFXM0QsK0NBQUtBLENBQUMsR0FBR00sSUFBSTZDLFNBQVNuQztRQUNqQ21CLFNBQVNELE9BQU9DLFFBQVFwQyxtREFBU0EsQ0FDN0JDLCtDQUFLQSxDQUFDMkQsVUFBVTNDLE9BQU8sSUFBSXFFLEtBQUs1RCxNQUFNQSxLQUN0Q3pCLCtDQUFLQSxDQUFDQSwrQ0FBS0EsQ0FBQyxHQUFHUSxJQUFJMkMsU0FBUzNCLEtBQUtBLElBQUk4RCxLQUFLNUQsT0FBT0EsTUFDakQxQiwrQ0FBS0EsQ0FBQ0EsK0NBQUtBLENBQUMsR0FBR08sSUFBSTRDLFNBQVMzQixLQUFLQSxJQUFJLENBQUMrRCxLQUFLNUQsT0FBT0EsTUFBTUMsS0FBS0UsTUFBTUE7SUFDM0U7SUFDQSxJQUFJd0IsWUFBWSxHQUFHO1FBQ2ZNLFdBQVc1RCwrQ0FBS0EsQ0FBQyxHQUFHTSxJQUFJZ0QsU0FBU3JDO1FBQ2pDa0IsU0FBU0QsT0FBT0MsUUFBUXBDLG1EQUFTQSxDQUM3QkMsK0NBQUtBLENBQUM0RCxVQUFVM0MsT0FBTyxJQUFJdUUsS0FBSy9ELE1BQU1BLEtBQ3RDekIsK0NBQUtBLENBQUNBLCtDQUFLQSxDQUFDLEdBQUdPLElBQUkrQyxTQUFTOUIsS0FBS0EsSUFBSTRELEtBQUsxRCxPQUFPQSxNQUNqRDFCLCtDQUFLQSxDQUFDQSwrQ0FBS0EsQ0FBQyxHQUFHUSxJQUFJOEMsU0FBUzlCLEtBQUtBLElBQUksQ0FBQzJELEtBQUt4RCxPQUFPQSxNQUFNQyxLQUFLRSxNQUFNQTtJQUMzRTtJQUVBLElBQUltQixZQUFZLEtBQUtHLFlBQVksR0FBRztRQUNoQyxJQUFJRixZQUFZLEtBQUtHLFlBQVksS0FBS0YsWUFBWSxLQUFLRyxZQUFZLEdBQUc7WUFDbEV3QixLQUFLNUIsVUFBVXNDO1lBQ2ZsQixJQUFJNUUsOENBQVFBLEdBQUd3RDtZQUNmcUIsTUFBTUQsSUFBS0EsQ0FBQUEsSUFBSXBCLE9BQU07WUFDckJzQixNQUFNdEIsVUFBVXFCO1lBQ2hCRCxJQUFJNUUsOENBQVFBLEdBQUc4RjtZQUNmZixNQUFNSCxJQUFLQSxDQUFBQSxJQUFJa0IsR0FBRTtZQUNqQmQsTUFBTWMsTUFBTWY7WUFDWk0sS0FBS1AsTUFBTUUsTUFBT0ksQ0FBQUEsS0FBS1AsTUFBTUUsTUFBTUQsTUFBTUMsTUFBTUYsTUFBTUcsR0FBRTtZQUN2RE0sS0FBS0ksTUFBTTlCO1lBQ1hnQixJQUFJNUUsOENBQVFBLEdBQUcwRjtZQUNmYixNQUFNRCxJQUFLQSxDQUFBQSxJQUFJYyxHQUFFO1lBQ2pCWixNQUFNWSxNQUFNYjtZQUNaRCxJQUFJNUUsOENBQVFBLEdBQUc0RDtZQUNmbUIsTUFBTUgsSUFBS0EsQ0FBQUEsSUFBSWhCLE9BQU07WUFDckJvQixNQUFNcEIsVUFBVW1CO1lBQ2hCUSxLQUFLVCxNQUFNRSxNQUFPTSxDQUFBQSxLQUFLVCxNQUFNRSxNQUFNRCxNQUFNQyxNQUFNRixNQUFNRyxHQUFFO1lBQ3ZEQyxLQUFLSSxLQUFLRTtZQUNWWixRQUFRTSxLQUFLSTtZQUNickUsQ0FBQyxDQUFDLEVBQUUsR0FBR3FFLEtBQU1KLENBQUFBLEtBQUtOLEtBQUksSUFBTVksQ0FBQUEsS0FBS1osS0FBSTtZQUNyQ08sS0FBS0UsS0FBS0g7WUFDVk4sUUFBUU8sS0FBS0U7WUFDYkQsS0FBS0MsS0FBTUYsQ0FBQUEsS0FBS1AsS0FBSSxJQUFNTSxDQUFBQSxLQUFLTixLQUFJO1lBQ25DTSxLQUFLRSxLQUFLRztZQUNWWCxRQUFRTSxLQUFLRTtZQUNibkUsQ0FBQyxDQUFDLEVBQUUsR0FBR21FLEtBQU1GLENBQUFBLEtBQUtOLEtBQUksSUFBTVcsQ0FBQUEsS0FBS1gsS0FBSTtZQUNyQ2EsS0FBS04sS0FBS0Q7WUFDVk4sUUFBUWEsS0FBS047WUFDYmxFLENBQUMsQ0FBQyxFQUFFLEdBQUdrRSxLQUFNTSxDQUFBQSxLQUFLYixLQUFJLElBQU1NLENBQUFBLEtBQUtOLEtBQUk7WUFDckMzRCxDQUFDLENBQUMsRUFBRSxHQUFHd0U7WUFDUEosS0FBSzNCLFVBQVUsQ0FBQ29DO1lBQ2hCakIsSUFBSTVFLDhDQUFRQSxHQUFHeUQ7WUFDZm9CLE1BQU1ELElBQUtBLENBQUFBLElBQUluQixPQUFNO1lBQ3JCcUIsTUFBTXJCLFVBQVVvQjtZQUNoQkQsSUFBSTVFLDhDQUFRQSxHQUFHLENBQUM2RjtZQUNoQmQsTUFBTUgsSUFBS0EsQ0FBQUEsSUFBSSxDQUFDaUIsR0FBRTtZQUNsQmIsTUFBTSxDQUFDYSxNQUFNZDtZQUNiTSxLQUFLUCxNQUFNRSxNQUFPSSxDQUFBQSxLQUFLUCxNQUFNRSxNQUFNRCxNQUFNQyxNQUFNRixNQUFNRyxHQUFFO1lBQ3ZETSxLQUFLSyxNQUFNLENBQUNoQztZQUNaaUIsSUFBSTVFLDhDQUFRQSxHQUFHMkY7WUFDZmQsTUFBTUQsSUFBS0EsQ0FBQUEsSUFBSWUsR0FBRTtZQUNqQmIsTUFBTWEsTUFBTWQ7WUFDWkQsSUFBSTVFLDhDQUFRQSxHQUFHLENBQUMyRDtZQUNoQm9CLE1BQU1ILElBQUtBLENBQUFBLElBQUksQ0FBQ2pCLE9BQU07WUFDdEJxQixNQUFNLENBQUNyQixVQUFVb0I7WUFDakJRLEtBQUtULE1BQU1FLE1BQU9NLENBQUFBLEtBQUtULE1BQU1FLE1BQU1ELE1BQU1DLE1BQU1GLE1BQU1HLEdBQUU7WUFDdkRDLEtBQUtJLEtBQUtFO1lBQ1ZaLFFBQVFNLEtBQUtJO1lBQ2JwRSxDQUFDLENBQUMsRUFBRSxHQUFHb0UsS0FBTUosQ0FBQUEsS0FBS04sS0FBSSxJQUFNWSxDQUFBQSxLQUFLWixLQUFJO1lBQ3JDTyxLQUFLRSxLQUFLSDtZQUNWTixRQUFRTyxLQUFLRTtZQUNiRCxLQUFLQyxLQUFNRixDQUFBQSxLQUFLUCxLQUFJLElBQU1NLENBQUFBLEtBQUtOLEtBQUk7WUFDbkNNLEtBQUtFLEtBQUtHO1lBQ1ZYLFFBQVFNLEtBQUtFO1lBQ2JsRSxDQUFDLENBQUMsRUFBRSxHQUFHa0UsS0FBTUYsQ0FBQUEsS0FBS04sS0FBSSxJQUFNVyxDQUFBQSxLQUFLWCxLQUFJO1lBQ3JDYSxLQUFLTixLQUFLRDtZQUNWTixRQUFRYSxLQUFLTjtZQUNiakUsQ0FBQyxDQUFDLEVBQUUsR0FBR2lFLEtBQU1NLENBQUFBLEtBQUtiLEtBQUksSUFBTU0sQ0FBQUEsS0FBS04sS0FBSTtZQUNyQzFELENBQUMsQ0FBQyxFQUFFLEdBQUd1RTtZQUNQcEIsU0FBU2hFLDZDQUFHQSxDQUFDLEdBQUdZLEdBQUcsR0FBR0MsR0FBR1E7WUFDekIyRCxLQUFLNUIsVUFBVUk7WUFDZmdCLElBQUk1RSw4Q0FBUUEsR0FBR3dEO1lBQ2ZxQixNQUFNRCxJQUFLQSxDQUFBQSxJQUFJcEIsT0FBTTtZQUNyQnNCLE1BQU10QixVQUFVcUI7WUFDaEJELElBQUk1RSw4Q0FBUUEsR0FBRzREO1lBQ2ZtQixNQUFNSCxJQUFLQSxDQUFBQSxJQUFJaEIsT0FBTTtZQUNyQm9CLE1BQU1wQixVQUFVbUI7WUFDaEJNLEtBQUtQLE1BQU1FLE1BQU9JLENBQUFBLEtBQUtQLE1BQU1FLE1BQU1ELE1BQU1DLE1BQU1GLE1BQU1HLEdBQUU7WUFDdkRNLEtBQUs3QixVQUFVRTtZQUNmaUIsSUFBSTVFLDhDQUFRQSxHQUFHeUQ7WUFDZm9CLE1BQU1ELElBQUtBLENBQUFBLElBQUluQixPQUFNO1lBQ3JCcUIsTUFBTXJCLFVBQVVvQjtZQUNoQkQsSUFBSTVFLDhDQUFRQSxHQUFHMkQ7WUFDZm9CLE1BQU1ILElBQUtBLENBQUFBLElBQUlqQixPQUFNO1lBQ3JCcUIsTUFBTXJCLFVBQVVvQjtZQUNoQlEsS0FBS1QsTUFBTUUsTUFBT00sQ0FBQUEsS0FBS1QsTUFBTUUsTUFBTUQsTUFBTUMsTUFBTUYsTUFBTUcsR0FBRTtZQUN2REMsS0FBS0ksS0FBS0U7WUFDVlosUUFBUVUsS0FBS0o7WUFDYnJELElBQUksQ0FBQyxFQUFFLEdBQUd5RCxLQUFNSixDQUFBQSxLQUFLTixLQUFJLElBQU1BLENBQUFBLFFBQVFZLEVBQUM7WUFDeENMLEtBQUtFLEtBQUtIO1lBQ1ZOLFFBQVFPLEtBQUtFO1lBQ2JELEtBQUtDLEtBQU1GLENBQUFBLEtBQUtQLEtBQUksSUFBTU0sQ0FBQUEsS0FBS04sS0FBSTtZQUNuQ00sS0FBS0UsS0FBS0c7WUFDVlgsUUFBUVEsS0FBS0Y7WUFDYnJELElBQUksQ0FBQyxFQUFFLEdBQUd1RCxLQUFNRixDQUFBQSxLQUFLTixLQUFJLElBQU1BLENBQUFBLFFBQVFXLEVBQUM7WUFDeENFLEtBQUtOLEtBQUtEO1lBQ1ZOLFFBQVFhLEtBQUtOO1lBQ2J0RCxJQUFJLENBQUMsRUFBRSxHQUFHc0QsS0FBTU0sQ0FBQUEsS0FBS2IsS0FBSSxJQUFNTSxDQUFBQSxLQUFLTixLQUFJO1lBQ3hDL0MsSUFBSSxDQUFDLEVBQUUsR0FBRzREO1lBQ1ZqQixVQUFVO1FBQ2QsT0FBTztZQUNIOUMsR0FBRyxDQUFDLEVBQUUsR0FBRztZQUNUMkMsU0FBUztZQUNUeEMsSUFBSSxDQUFDLEVBQUUsR0FBRztZQUNWMkMsVUFBVTtRQUNkO1FBQ0EsSUFBSWhCLFlBQVksR0FBRztZQUNmLE1BQU00QyxNQUFNN0YsK0NBQUtBLENBQUM4RCxRQUFRM0MsS0FBSzhCLFNBQVN0QjtZQUN4Q1EsU0FBU0QsT0FBT0MsUUFBUXJDLDZDQUFHQSxDQUN2QkUsK0NBQUtBLENBQUN1RCxVQUFVM0MsT0FBT3FDLFNBQVN4QixNQUFNQSxLQUN0Q3pCLCtDQUFLQSxDQUFDNkYsS0FBS2xFLE1BQU0sSUFBSXdELEtBQUt2RCxNQUFNQSxLQUFLRSxNQUFNQTtZQUUvQyxNQUFNZ0UsT0FBTzlGLCtDQUFLQSxDQUFDaUUsU0FBUzNDLE1BQU0yQixTQUFTekI7WUFDM0NXLFNBQVNELE9BQU9DLFFBQVFwQyxtREFBU0EsQ0FDN0JDLCtDQUFLQSxDQUFDOEYsTUFBTXRFLElBQUksSUFBSTJELEtBQUsxRCxNQUFNQSxLQUMvQnpCLCtDQUFLQSxDQUFDOEYsTUFBTXRFLElBQUl5QixTQUFTdkIsT0FBT0EsTUFDaEMxQiwrQ0FBS0EsQ0FBQzZGLEtBQUtsRSxNQUFNc0IsU0FBU3JCLE1BQU1BLEtBQUtDLE1BQU1FLE1BQU1BO1lBRXJELElBQUlzQixZQUFZLEdBQUc7Z0JBQ2ZsQixTQUFTRCxPQUFPQyxRQUFRbkMsK0NBQUtBLENBQUNBLCtDQUFLQSxDQUFDLEdBQUdTLElBQUl3QyxTQUFTekIsS0FBS0EsSUFBSTZCLFNBQVM1QixNQUFNQTtZQUNoRjtZQUNBLElBQUk2QixZQUFZLEdBQUc7Z0JBQ2ZuQixTQUFTRCxPQUFPQyxRQUFRbkMsK0NBQUtBLENBQUNBLCtDQUFLQSxDQUFDLEdBQUdRLElBQUksQ0FBQ3lDLFNBQVN6QixLQUFLQSxJQUFJOEIsU0FBUzdCLE1BQU1BO1lBQ2pGO1FBQ0o7UUFDQSxJQUFJMkIsWUFBWSxHQUFHO1lBQ2YsTUFBTXlDLE1BQU03RiwrQ0FBS0EsQ0FBQzhELFFBQVEzQyxLQUFLaUMsU0FBU3pCO1lBQ3hDUSxTQUFTRCxPQUFPQyxRQUFRckMsNkNBQUdBLENBQ3ZCRSwrQ0FBS0EsQ0FBQ3dELFVBQVUzQyxPQUFPdUMsU0FBUzNCLE1BQU1BLEtBQ3RDekIsK0NBQUtBLENBQUM2RixLQUFLbEUsTUFBTSxJQUFJMkQsS0FBSzFELE1BQU1BLEtBQUtFLE1BQU1BO1lBRS9DLE1BQU1nRSxPQUFPOUYsK0NBQUtBLENBQUNpRSxTQUFTM0MsTUFBTThCLFNBQVM1QjtZQUMzQ1csU0FBU0QsT0FBT0MsUUFBUXBDLG1EQUFTQSxDQUM3QkMsK0NBQUtBLENBQUM4RixNQUFNdEUsSUFBSSxJQUFJOEQsS0FBSzdELE1BQU1BLEtBQy9CekIsK0NBQUtBLENBQUM4RixNQUFNdEUsSUFBSTRCLFNBQVMxQixPQUFPQSxNQUNoQzFCLCtDQUFLQSxDQUFDNkYsS0FBS2xFLE1BQU15QixTQUFTeEIsTUFBTUEsS0FBS0MsTUFBTUUsTUFBTUE7UUFDekQ7SUFDSjtJQUNBLElBQUltQixZQUFZLEtBQUtHLFlBQVksR0FBRztRQUNoQyxJQUFJRixZQUFZLEtBQUtHLFlBQVksS0FBS0wsWUFBWSxLQUFLRyxZQUFZLEdBQUc7WUFDbEUwQixLQUFLM0IsVUFBVW1DO1lBQ2ZoQixJQUFJNUUsOENBQVFBLEdBQUd5RDtZQUNmb0IsTUFBTUQsSUFBS0EsQ0FBQUEsSUFBSW5CLE9BQU07WUFDckJxQixNQUFNckIsVUFBVW9CO1lBQ2hCRCxJQUFJNUUsOENBQVFBLEdBQUc0RjtZQUNmYixNQUFNSCxJQUFLQSxDQUFBQSxJQUFJZ0IsR0FBRTtZQUNqQlosTUFBTVksTUFBTWI7WUFDWk0sS0FBS1AsTUFBTUUsTUFBT0ksQ0FBQUEsS0FBS1AsTUFBTUUsTUFBTUQsTUFBTUMsTUFBTUYsTUFBTUcsR0FBRTtZQUN2RE0sS0FBS0ssTUFBTWpDO1lBQ1hrQixJQUFJNUUsOENBQVFBLEdBQUcyRjtZQUNmZCxNQUFNRCxJQUFLQSxDQUFBQSxJQUFJZSxHQUFFO1lBQ2pCYixNQUFNYSxNQUFNZDtZQUNaRCxJQUFJNUUsOENBQVFBLEdBQUcwRDtZQUNmcUIsTUFBTUgsSUFBS0EsQ0FBQUEsSUFBSWxCLE9BQU07WUFDckJzQixNQUFNdEIsVUFBVXFCO1lBQ2hCUSxLQUFLVCxNQUFNRSxNQUFPTSxDQUFBQSxLQUFLVCxNQUFNRSxNQUFNRCxNQUFNQyxNQUFNRixNQUFNRyxHQUFFO1lBQ3ZEQyxLQUFLSSxLQUFLRTtZQUNWWixRQUFRTSxLQUFLSTtZQUNickUsQ0FBQyxDQUFDLEVBQUUsR0FBR3FFLEtBQU1KLENBQUFBLEtBQUtOLEtBQUksSUFBTVksQ0FBQUEsS0FBS1osS0FBSTtZQUNyQ08sS0FBS0UsS0FBS0g7WUFDVk4sUUFBUU8sS0FBS0U7WUFDYkQsS0FBS0MsS0FBTUYsQ0FBQUEsS0FBS1AsS0FBSSxJQUFNTSxDQUFBQSxLQUFLTixLQUFJO1lBQ25DTSxLQUFLRSxLQUFLRztZQUNWWCxRQUFRTSxLQUFLRTtZQUNibkUsQ0FBQyxDQUFDLEVBQUUsR0FBR21FLEtBQU1GLENBQUFBLEtBQUtOLEtBQUksSUFBTVcsQ0FBQUEsS0FBS1gsS0FBSTtZQUNyQ2EsS0FBS04sS0FBS0Q7WUFDVk4sUUFBUWEsS0FBS047WUFDYmxFLENBQUMsQ0FBQyxFQUFFLEdBQUdrRSxLQUFNTSxDQUFBQSxLQUFLYixLQUFJLElBQU1NLENBQUFBLEtBQUtOLEtBQUk7WUFDckMzRCxDQUFDLENBQUMsRUFBRSxHQUFHd0U7WUFDUGYsS0FBSyxDQUFDcUI7WUFDTnBCLEtBQUssQ0FBQ2Q7WUFDTndCLEtBQUs3QixVQUFVa0I7WUFDZkcsSUFBSTVFLDhDQUFRQSxHQUFHdUQ7WUFDZnNCLE1BQU1ELElBQUtBLENBQUFBLElBQUlyQixPQUFNO1lBQ3JCdUIsTUFBTXZCLFVBQVVzQjtZQUNoQkQsSUFBSTVFLDhDQUFRQSxHQUFHeUU7WUFDZk0sTUFBTUgsSUFBS0EsQ0FBQUEsSUFBSUgsRUFBQztZQUNoQk8sTUFBTVAsS0FBS007WUFDWE0sS0FBS1AsTUFBTUUsTUFBT0ksQ0FBQUEsS0FBS1AsTUFBTUUsTUFBTUQsTUFBTUMsTUFBTUYsTUFBTUcsR0FBRTtZQUN2RE0sS0FBS0csTUFBTWY7WUFDWEUsSUFBSTVFLDhDQUFRQSxHQUFHeUY7WUFDZlosTUFBTUQsSUFBS0EsQ0FBQUEsSUFBSWEsR0FBRTtZQUNqQlgsTUFBTVcsTUFBTVo7WUFDWkQsSUFBSTVFLDhDQUFRQSxHQUFHMEU7WUFDZkssTUFBTUgsSUFBS0EsQ0FBQUEsSUFBSUYsRUFBQztZQUNoQk0sTUFBTU4sS0FBS0s7WUFDWFEsS0FBS1QsTUFBTUUsTUFBT00sQ0FBQUEsS0FBS1QsTUFBTUUsTUFBTUQsTUFBTUMsTUFBTUYsTUFBTUcsR0FBRTtZQUN2REMsS0FBS0ksS0FBS0U7WUFDVlosUUFBUU0sS0FBS0k7WUFDYnBFLENBQUMsQ0FBQyxFQUFFLEdBQUdvRSxLQUFNSixDQUFBQSxLQUFLTixLQUFJLElBQU1ZLENBQUFBLEtBQUtaLEtBQUk7WUFDckNPLEtBQUtFLEtBQUtIO1lBQ1ZOLFFBQVFPLEtBQUtFO1lBQ2JELEtBQUtDLEtBQU1GLENBQUFBLEtBQUtQLEtBQUksSUFBTU0sQ0FBQUEsS0FBS04sS0FBSTtZQUNuQ00sS0FBS0UsS0FBS0c7WUFDVlgsUUFBUU0sS0FBS0U7WUFDYmxFLENBQUMsQ0FBQyxFQUFFLEdBQUdrRSxLQUFNRixDQUFBQSxLQUFLTixLQUFJLElBQU1XLENBQUFBLEtBQUtYLEtBQUk7WUFDckNhLEtBQUtOLEtBQUtEO1lBQ1ZOLFFBQVFhLEtBQUtOO1lBQ2JqRSxDQUFDLENBQUMsRUFBRSxHQUFHaUUsS0FBTU0sQ0FBQUEsS0FBS2IsS0FBSSxJQUFNTSxDQUFBQSxLQUFLTixLQUFJO1lBQ3JDMUQsQ0FBQyxDQUFDLEVBQUUsR0FBR3VFO1lBQ1BuQixTQUFTakUsNkNBQUdBLENBQUMsR0FBR1ksR0FBRyxHQUFHQyxHQUFHUztZQUN6QjBELEtBQUszQixVQUFVQztZQUNma0IsSUFBSTVFLDhDQUFRQSxHQUFHeUQ7WUFDZm9CLE1BQU1ELElBQUtBLENBQUFBLElBQUluQixPQUFNO1lBQ3JCcUIsTUFBTXJCLFVBQVVvQjtZQUNoQkQsSUFBSTVFLDhDQUFRQSxHQUFHMEQ7WUFDZnFCLE1BQU1ILElBQUtBLENBQUFBLElBQUlsQixPQUFNO1lBQ3JCc0IsTUFBTXRCLFVBQVVxQjtZQUNoQk0sS0FBS1AsTUFBTUUsTUFBT0ksQ0FBQUEsS0FBS1AsTUFBTUUsTUFBTUQsTUFBTUMsTUFBTUYsTUFBTUcsR0FBRTtZQUN2RE0sS0FBSy9CLFVBQVVLO1lBQ2ZnQixJQUFJNUUsOENBQVFBLEdBQUd1RDtZQUNmc0IsTUFBTUQsSUFBS0EsQ0FBQUEsSUFBSXJCLE9BQU07WUFDckJ1QixNQUFNdkIsVUFBVXNCO1lBQ2hCRCxJQUFJNUUsOENBQVFBLEdBQUc0RDtZQUNmbUIsTUFBTUgsSUFBS0EsQ0FBQUEsSUFBSWhCLE9BQU07WUFDckJvQixNQUFNcEIsVUFBVW1CO1lBQ2hCUSxLQUFLVCxNQUFNRSxNQUFPTSxDQUFBQSxLQUFLVCxNQUFNRSxNQUFNRCxNQUFNQyxNQUFNRixNQUFNRyxHQUFFO1lBQ3ZEQyxLQUFLSSxLQUFLRTtZQUNWWixRQUFRVSxLQUFLSjtZQUNicEQsSUFBSSxDQUFDLEVBQUUsR0FBR3dELEtBQU1KLENBQUFBLEtBQUtOLEtBQUksSUFBTUEsQ0FBQUEsUUFBUVksRUFBQztZQUN4Q0wsS0FBS0UsS0FBS0g7WUFDVk4sUUFBUU8sS0FBS0U7WUFDYkQsS0FBS0MsS0FBTUYsQ0FBQUEsS0FBS1AsS0FBSSxJQUFNTSxDQUFBQSxLQUFLTixLQUFJO1lBQ25DTSxLQUFLRSxLQUFLRztZQUNWWCxRQUFRUSxLQUFLRjtZQUNicEQsSUFBSSxDQUFDLEVBQUUsR0FBR3NELEtBQU1GLENBQUFBLEtBQUtOLEtBQUksSUFBTUEsQ0FBQUEsUUFBUVcsRUFBQztZQUN4Q0UsS0FBS04sS0FBS0Q7WUFDVk4sUUFBUWEsS0FBS047WUFDYnJELElBQUksQ0FBQyxFQUFFLEdBQUdxRCxLQUFNTSxDQUFBQSxLQUFLYixLQUFJLElBQU1NLENBQUFBLEtBQUtOLEtBQUk7WUFDeEM5QyxJQUFJLENBQUMsRUFBRSxHQUFHMkQ7WUFDVmhCLFVBQVU7UUFDZCxPQUFPO1lBQ0g5QyxHQUFHLENBQUMsRUFBRSxHQUFHO1lBQ1QyQyxTQUFTO1lBQ1R4QyxJQUFJLENBQUMsRUFBRSxHQUFHO1lBQ1YyQyxVQUFVO1FBQ2Q7UUFDQSxJQUFJaEIsWUFBWSxHQUFHO1lBQ2YsTUFBTTJDLE1BQU03RiwrQ0FBS0EsQ0FBQytELFFBQVEzQyxLQUFLOEIsU0FBU3ZCO1lBQ3hDUSxTQUFTRCxPQUFPQyxRQUFRckMsNkNBQUdBLENBQ3ZCRSwrQ0FBS0EsQ0FBQ3lELFVBQVUzQyxPQUFPb0MsU0FBU3pCLE1BQU1BLEtBQ3RDekIsK0NBQUtBLENBQUM2RixLQUFLbEUsTUFBTSxJQUFJeUQsS0FBS3hELE1BQU1BLEtBQUtFLE1BQU1BO1lBRS9DLE1BQU1nRSxPQUFPOUYsK0NBQUtBLENBQUNrRSxTQUFTM0MsTUFBTTJCLFNBQVMxQjtZQUMzQ1csU0FBU0QsT0FBT0MsUUFBUXBDLG1EQUFTQSxDQUM3QkMsK0NBQUtBLENBQUM4RixNQUFNdEUsSUFBSSxJQUFJNEQsS0FBSzNELE1BQU1BLEtBQy9CekIsK0NBQUtBLENBQUM4RixNQUFNdEUsSUFBSTBCLFNBQVN4QixPQUFPQSxNQUNoQzFCLCtDQUFLQSxDQUFDNkYsS0FBS2xFLE1BQU11QixTQUFTdEIsTUFBTUEsS0FBS0MsTUFBTUUsTUFBTUE7WUFFckQsSUFBSXVCLFlBQVksR0FBRztnQkFDZm5CLFNBQVNELE9BQU9DLFFBQVFuQywrQ0FBS0EsQ0FBQ0EsK0NBQUtBLENBQUMsR0FBR08sSUFBSTJDLFNBQVMxQixLQUFLQSxJQUFJOEIsU0FBUzdCLE1BQU1BO1lBQ2hGO1lBQ0EsSUFBSTJCLFlBQVksR0FBRztnQkFDZmpCLFNBQVNELE9BQU9DLFFBQVFuQywrQ0FBS0EsQ0FBQ0EsK0NBQUtBLENBQUMsR0FBR1MsSUFBSSxDQUFDeUMsU0FBUzFCLEtBQUtBLElBQUk0QixTQUFTM0IsTUFBTUE7WUFDakY7UUFDSjtRQUNBLElBQUk0QixZQUFZLEdBQUc7WUFDZixNQUFNd0MsTUFBTTdGLCtDQUFLQSxDQUFDK0QsUUFBUTNDLEtBQUtpQyxTQUFTMUI7WUFDeENRLFNBQVNELE9BQU9DLFFBQVFyQyw2Q0FBR0EsQ0FDdkJFLCtDQUFLQSxDQUFDMEQsVUFBVTNDLE9BQU9zQyxTQUFTNUIsTUFBTUEsS0FDdEN6QiwrQ0FBS0EsQ0FBQzZGLEtBQUtsRSxNQUFNLElBQUk0RCxLQUFLM0QsTUFBTUEsS0FBS0UsTUFBTUE7WUFFL0MsTUFBTWdFLE9BQU85RiwrQ0FBS0EsQ0FBQ2tFLFNBQVMzQyxNQUFNOEIsU0FBUzdCO1lBQzNDVyxTQUFTRCxPQUFPQyxRQUFRcEMsbURBQVNBLENBQzdCQywrQ0FBS0EsQ0FBQzhGLE1BQU10RSxJQUFJLElBQUkrRCxLQUFLOUQsTUFBTUEsS0FDL0J6QiwrQ0FBS0EsQ0FBQzhGLE1BQU10RSxJQUFJNkIsU0FBUzNCLE9BQU9BLE1BQ2hDMUIsK0NBQUtBLENBQUM2RixLQUFLbEUsTUFBTTBCLFNBQVN6QixNQUFNQSxLQUFNQyxNQUFNRSxNQUFNQTtRQUMxRDtJQUNKO0lBQ0EsSUFBSW9CLFlBQVksS0FBS0csWUFBWSxHQUFHO1FBQ2hDLElBQUlMLFlBQVksS0FBS0csWUFBWSxLQUFLRixZQUFZLEtBQUtHLFlBQVksR0FBRztZQUNsRXlCLEtBQUs3QixVQUFVc0M7WUFDZmpCLElBQUk1RSw4Q0FBUUEsR0FBR3VEO1lBQ2ZzQixNQUFNRCxJQUFLQSxDQUFBQSxJQUFJckIsT0FBTTtZQUNyQnVCLE1BQU12QixVQUFVc0I7WUFDaEJELElBQUk1RSw4Q0FBUUEsR0FBRzZGO1lBQ2ZkLE1BQU1ILElBQUtBLENBQUFBLElBQUlpQixHQUFFO1lBQ2pCYixNQUFNYSxNQUFNZDtZQUNaTSxLQUFLUCxNQUFNRSxNQUFPSSxDQUFBQSxLQUFLUCxNQUFNRSxNQUFNRCxNQUFNQyxNQUFNRixNQUFNRyxHQUFFO1lBQ3ZETSxLQUFLRyxNQUFNOUI7WUFDWGlCLElBQUk1RSw4Q0FBUUEsR0FBR3lGO1lBQ2ZaLE1BQU1ELElBQUtBLENBQUFBLElBQUlhLEdBQUU7WUFDakJYLE1BQU1XLE1BQU1aO1lBQ1pELElBQUk1RSw4Q0FBUUEsR0FBRzJEO1lBQ2ZvQixNQUFNSCxJQUFLQSxDQUFBQSxJQUFJakIsT0FBTTtZQUNyQnFCLE1BQU1yQixVQUFVb0I7WUFDaEJRLEtBQUtULE1BQU1FLE1BQU9NLENBQUFBLEtBQUtULE1BQU1FLE1BQU1ELE1BQU1DLE1BQU1GLE1BQU1HLEdBQUU7WUFDdkRDLEtBQUtJLEtBQUtFO1lBQ1ZaLFFBQVFNLEtBQUtJO1lBQ2JyRSxDQUFDLENBQUMsRUFBRSxHQUFHcUUsS0FBTUosQ0FBQUEsS0FBS04sS0FBSSxJQUFNWSxDQUFBQSxLQUFLWixLQUFJO1lBQ3JDTyxLQUFLRSxLQUFLSDtZQUNWTixRQUFRTyxLQUFLRTtZQUNiRCxLQUFLQyxLQUFNRixDQUFBQSxLQUFLUCxLQUFJLElBQU1NLENBQUFBLEtBQUtOLEtBQUk7WUFDbkNNLEtBQUtFLEtBQUtHO1lBQ1ZYLFFBQVFNLEtBQUtFO1lBQ2JuRSxDQUFDLENBQUMsRUFBRSxHQUFHbUUsS0FBTUYsQ0FBQUEsS0FBS04sS0FBSSxJQUFNVyxDQUFBQSxLQUFLWCxLQUFJO1lBQ3JDYSxLQUFLTixLQUFLRDtZQUNWTixRQUFRYSxLQUFLTjtZQUNibEUsQ0FBQyxDQUFDLEVBQUUsR0FBR2tFLEtBQU1NLENBQUFBLEtBQUtiLEtBQUksSUFBTU0sQ0FBQUEsS0FBS04sS0FBSTtZQUNyQzNELENBQUMsQ0FBQyxFQUFFLEdBQUd3RTtZQUNQZixLQUFLLENBQUNtQjtZQUNObEIsS0FBSyxDQUFDaEI7WUFDTjBCLEtBQUs1QixVQUFVaUI7WUFDZkcsSUFBSTVFLDhDQUFRQSxHQUFHd0Q7WUFDZnFCLE1BQU1ELElBQUtBLENBQUFBLElBQUlwQixPQUFNO1lBQ3JCc0IsTUFBTXRCLFVBQVVxQjtZQUNoQkQsSUFBSTVFLDhDQUFRQSxHQUFHeUU7WUFDZk0sTUFBTUgsSUFBS0EsQ0FBQUEsSUFBSUgsRUFBQztZQUNoQk8sTUFBTVAsS0FBS007WUFDWE0sS0FBS1AsTUFBTUUsTUFBT0ksQ0FBQUEsS0FBS1AsTUFBTUUsTUFBTUQsTUFBTUMsTUFBTUYsTUFBTUcsR0FBRTtZQUN2RE0sS0FBS0ksTUFBTWhCO1lBQ1hFLElBQUk1RSw4Q0FBUUEsR0FBRzBGO1lBQ2ZiLE1BQU1ELElBQUtBLENBQUFBLElBQUljLEdBQUU7WUFDakJaLE1BQU1ZLE1BQU1iO1lBQ1pELElBQUk1RSw4Q0FBUUEsR0FBRzBFO1lBQ2ZLLE1BQU1ILElBQUtBLENBQUFBLElBQUlGLEVBQUM7WUFDaEJNLE1BQU1OLEtBQUtLO1lBQ1hRLEtBQUtULE1BQU1FLE1BQU9NLENBQUFBLEtBQUtULE1BQU1FLE1BQU1ELE1BQU1DLE1BQU1GLE1BQU1HLEdBQUU7WUFDdkRDLEtBQUtJLEtBQUtFO1lBQ1ZaLFFBQVFNLEtBQUtJO1lBQ2JwRSxDQUFDLENBQUMsRUFBRSxHQUFHb0UsS0FBTUosQ0FBQUEsS0FBS04sS0FBSSxJQUFNWSxDQUFBQSxLQUFLWixLQUFJO1lBQ3JDTyxLQUFLRSxLQUFLSDtZQUNWTixRQUFRTyxLQUFLRTtZQUNiRCxLQUFLQyxLQUFNRixDQUFBQSxLQUFLUCxLQUFJLElBQU1NLENBQUFBLEtBQUtOLEtBQUk7WUFDbkNNLEtBQUtFLEtBQUtHO1lBQ1ZYLFFBQVFNLEtBQUtFO1lBQ2JsRSxDQUFDLENBQUMsRUFBRSxHQUFHa0UsS0FBTUYsQ0FBQUEsS0FBS04sS0FBSSxJQUFNVyxDQUFBQSxLQUFLWCxLQUFJO1lBQ3JDYSxLQUFLTixLQUFLRDtZQUNWTixRQUFRYSxLQUFLTjtZQUNiakUsQ0FBQyxDQUFDLEVBQUUsR0FBR2lFLEtBQU1NLENBQUFBLEtBQUtiLEtBQUksSUFBTU0sQ0FBQUEsS0FBS04sS0FBSTtZQUNyQzFELENBQUMsQ0FBQyxFQUFFLEdBQUd1RTtZQUNQckIsU0FBUy9ELDZDQUFHQSxDQUFDLEdBQUdZLEdBQUcsR0FBR0MsR0FBR087WUFDekI0RCxLQUFLN0IsVUFBVUk7WUFDZmlCLElBQUk1RSw4Q0FBUUEsR0FBR3VEO1lBQ2ZzQixNQUFNRCxJQUFLQSxDQUFBQSxJQUFJckIsT0FBTTtZQUNyQnVCLE1BQU12QixVQUFVc0I7WUFDaEJELElBQUk1RSw4Q0FBUUEsR0FBRzJEO1lBQ2ZvQixNQUFNSCxJQUFLQSxDQUFBQSxJQUFJakIsT0FBTTtZQUNyQnFCLE1BQU1yQixVQUFVb0I7WUFDaEJNLEtBQUtQLE1BQU1FLE1BQU9JLENBQUFBLEtBQUtQLE1BQU1FLE1BQU1ELE1BQU1DLE1BQU1GLE1BQU1HLEdBQUU7WUFDdkRNLEtBQUs5QixVQUFVRTtZQUNma0IsSUFBSTVFLDhDQUFRQSxHQUFHd0Q7WUFDZnFCLE1BQU1ELElBQUtBLENBQUFBLElBQUlwQixPQUFNO1lBQ3JCc0IsTUFBTXRCLFVBQVVxQjtZQUNoQkQsSUFBSTVFLDhDQUFRQSxHQUFHMEQ7WUFDZnFCLE1BQU1ILElBQUtBLENBQUFBLElBQUlsQixPQUFNO1lBQ3JCc0IsTUFBTXRCLFVBQVVxQjtZQUNoQlEsS0FBS1QsTUFBTUUsTUFBT00sQ0FBQUEsS0FBS1QsTUFBTUUsTUFBTUQsTUFBTUMsTUFBTUYsTUFBTUcsR0FBRTtZQUN2REMsS0FBS0ksS0FBS0U7WUFDVlosUUFBUVUsS0FBS0o7WUFDYnRELElBQUksQ0FBQyxFQUFFLEdBQUcwRCxLQUFNSixDQUFBQSxLQUFLTixLQUFJLElBQU1BLENBQUFBLFFBQVFZLEVBQUM7WUFDeENMLEtBQUtFLEtBQUtIO1lBQ1ZOLFFBQVFPLEtBQUtFO1lBQ2JELEtBQUtDLEtBQU1GLENBQUFBLEtBQUtQLEtBQUksSUFBTU0sQ0FBQUEsS0FBS04sS0FBSTtZQUNuQ00sS0FBS0UsS0FBS0c7WUFDVlgsUUFBUVEsS0FBS0Y7WUFDYnRELElBQUksQ0FBQyxFQUFFLEdBQUd3RCxLQUFNRixDQUFBQSxLQUFLTixLQUFJLElBQU1BLENBQUFBLFFBQVFXLEVBQUM7WUFDeENFLEtBQUtOLEtBQUtEO1lBQ1ZOLFFBQVFhLEtBQUtOO1lBQ2J2RCxJQUFJLENBQUMsRUFBRSxHQUFHdUQsS0FBTU0sQ0FBQUEsS0FBS2IsS0FBSSxJQUFNTSxDQUFBQSxLQUFLTixLQUFJO1lBQ3hDaEQsSUFBSSxDQUFDLEVBQUUsR0FBRzZEO1lBQ1ZsQixVQUFVO1FBQ2QsT0FBTztZQUNIOUMsR0FBRyxDQUFDLEVBQUUsR0FBRztZQUNUMkMsU0FBUztZQUNUeEMsSUFBSSxDQUFDLEVBQUUsR0FBRztZQUNWMkMsVUFBVTtRQUNkO1FBQ0EsSUFBSWIsWUFBWSxHQUFHO1lBQ2YsTUFBTTBDLE1BQU03RiwrQ0FBS0EsQ0FBQzZELFFBQVEzQyxLQUFLaUMsU0FBU3hCO1lBQ3hDUSxTQUFTRCxPQUFPQyxRQUFRckMsNkNBQUdBLENBQ3ZCRSwrQ0FBS0EsQ0FBQzJELFVBQVUzQyxPQUFPbUMsU0FBUzFCLE1BQU1BLEtBQ3RDekIsK0NBQUtBLENBQUM2RixLQUFLbEUsTUFBTSxJQUFJMEQsS0FBS3pELE1BQU1BLEtBQUtFLE1BQU1BO1lBRS9DLE1BQU1nRSxPQUFPOUYsK0NBQUtBLENBQUNnRSxTQUFTM0MsTUFBTThCLFNBQVMzQjtZQUMzQ1csU0FBU0QsT0FBT0MsUUFBUXBDLG1EQUFTQSxDQUM3QkMsK0NBQUtBLENBQUM4RixNQUFNdEUsSUFBSSxJQUFJNkQsS0FBSzVELE1BQU1BLEtBQy9CekIsK0NBQUtBLENBQUM4RixNQUFNdEUsSUFBSTJCLFNBQVN6QixPQUFPQSxNQUNoQzFCLCtDQUFLQSxDQUFDNkYsS0FBS2xFLE1BQU13QixTQUFTdkIsTUFBTUEsS0FBS0MsTUFBTUUsTUFBTUE7WUFFckQsSUFBSXFCLFlBQVksR0FBRztnQkFDZmpCLFNBQVNELE9BQU9DLFFBQVFuQywrQ0FBS0EsQ0FBQ0EsK0NBQUtBLENBQUMsR0FBR1EsSUFBSTJDLFNBQVMzQixLQUFLQSxJQUFJNEIsU0FBUzNCLE1BQU1BO1lBQ2hGO1lBQ0EsSUFBSTRCLFlBQVksR0FBRztnQkFDZmxCLFNBQVNELE9BQU9DLFFBQVFuQywrQ0FBS0EsQ0FBQ0EsK0NBQUtBLENBQUMsR0FBR08sSUFBSSxDQUFDNEMsU0FBUzNCLEtBQUtBLElBQUk2QixTQUFTNUIsTUFBTUE7WUFDakY7UUFDSjtRQUNBLElBQUk2QixZQUFZLEdBQUc7WUFDZixNQUFNdUMsTUFBTTdGLCtDQUFLQSxDQUFDNkQsUUFBUTNDLEtBQUtvQyxTQUFTM0I7WUFDeENRLFNBQVNELE9BQU9DLFFBQVFyQyw2Q0FBR0EsQ0FDdkJFLCtDQUFLQSxDQUFDNEQsVUFBVTNDLE9BQU9xQyxTQUFTN0IsTUFBTUEsS0FDdEN6QiwrQ0FBS0EsQ0FBQzZGLEtBQUtsRSxNQUFNLElBQUk2RCxLQUFLNUQsTUFBTUEsS0FBS0UsTUFBTUE7WUFFL0MsTUFBTWdFLE9BQU85RiwrQ0FBS0EsQ0FBQ2dFLFNBQVMzQyxNQUFNaUMsU0FBUzlCO1lBQzNDVyxTQUFTRCxPQUFPQyxRQUFRcEMsbURBQVNBLENBQzdCQywrQ0FBS0EsQ0FBQzhGLE1BQU10RSxJQUFJLElBQUlnRSxLQUFLL0QsTUFBTUEsS0FDL0J6QiwrQ0FBS0EsQ0FBQzhGLE1BQU10RSxJQUFJOEIsU0FBUzVCLE9BQU9BLE1BQ2hDMUIsK0NBQUtBLENBQUM2RixLQUFLbEUsTUFBTTJCLFNBQVMxQixNQUFNQSxLQUFLQyxNQUFNRSxNQUFNQTtRQUN6RDtJQUNKO0lBRUEsT0FBT0MsR0FBRyxDQUFDRyxTQUFTLEVBQUU7QUFDMUI7QUFFTyxTQUFTNEQsU0FBU3ZELEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLEVBQUUsRUFBRUMsRUFBRTtJQUNuRCxNQUFNb0MsTUFBTTNDLEtBQUtNO0lBQ2pCLE1BQU1zQyxNQUFNMUMsS0FBS0k7SUFDakIsTUFBTXVDLE1BQU16QyxLQUFLRTtJQUNqQixNQUFNd0MsTUFBTTdDLEtBQUtNO0lBQ2pCLE1BQU13QyxNQUFNNUMsS0FBS0k7SUFDakIsTUFBTXlDLE1BQU0zQyxLQUFLRTtJQUVqQixNQUFNaUQsU0FBU1osTUFBTUk7SUFDckIsTUFBTVMsU0FBU1osTUFBTUU7SUFDckIsTUFBTVcsUUFBUWYsTUFBTUEsTUFBTUcsTUFBTUE7SUFFaEMsTUFBTWEsU0FBU2QsTUFBTUM7SUFDckIsTUFBTWMsU0FBU2pCLE1BQU1LO0lBQ3JCLE1BQU1hLFFBQVFqQixNQUFNQSxNQUFNRyxNQUFNQTtJQUVoQyxNQUFNZSxTQUFTbkIsTUFBTUk7SUFDckIsTUFBTWdCLFNBQVNuQixNQUFNRTtJQUNyQixNQUFNa0IsUUFBUW5CLE1BQU1BLE1BQU1HLE1BQU1BO0lBRWhDLE1BQU1DLE1BQ0ZTLFFBQVNGLENBQUFBLFNBQVNDLE1BQUssSUFDdkJJLFFBQVNGLENBQUFBLFNBQVNDLE1BQUssSUFDdkJJLFFBQVNGLENBQUFBLFNBQVNDLE1BQUs7SUFFM0IsTUFBTXZELFlBQ0YsQ0FBQzJDLEtBQUtDLEdBQUcsQ0FBQ0ksVUFBVUwsS0FBS0MsR0FBRyxDQUFDSyxPQUFNLElBQUtDLFFBQ3hDLENBQUNQLEtBQUtDLEdBQUcsQ0FBQ08sVUFBVVIsS0FBS0MsR0FBRyxDQUFDUSxPQUFNLElBQUtDLFFBQ3hDLENBQUNWLEtBQUtDLEdBQUcsQ0FBQ1UsVUFBVVgsS0FBS0MsR0FBRyxDQUFDVyxPQUFNLElBQUtDO0lBRTVDLE1BQU1kLFdBQVd6RixlQUFlK0M7SUFFaEMsSUFBSXlDLE1BQU1DLFlBQVksQ0FBQ0QsTUFBTUMsVUFBVTtRQUNuQyxPQUFPRDtJQUNYO0lBQ0EsT0FBT2xELGNBQWNDLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDLElBQUlDO0FBQ3pEO0FBRU8sU0FBU3lELGFBQWFqRSxFQUFFLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLEVBQUU7SUFDdkQsTUFBTW9DLE1BQU0zQyxLQUFLTTtJQUNqQixNQUFNd0MsTUFBTTdDLEtBQUtNO0lBQ2pCLE1BQU1xQyxNQUFNMUMsS0FBS0k7SUFDakIsTUFBTXlDLE1BQU01QyxLQUFLSTtJQUNqQixNQUFNc0MsTUFBTXpDLEtBQUtFO0lBQ2pCLE1BQU0wQyxNQUFNM0MsS0FBS0U7SUFFakIsTUFBTTJELFFBQVF2QixNQUFNSSxNQUFNSCxNQUFNRTtJQUNoQyxNQUFNcUIsUUFBUXZCLE1BQU1JLE1BQU1ILE1BQU1FO0lBQ2hDLE1BQU1xQixRQUFRdkIsTUFBTUMsTUFBTUgsTUFBTUs7SUFDaEMsTUFBTVUsUUFBUWYsTUFBTUEsTUFBTUcsTUFBTUE7SUFDaEMsTUFBTWUsUUFBUWpCLE1BQU1BLE1BQU1HLE1BQU1BO0lBQ2hDLE1BQU1pQixRQUFRbkIsTUFBTUEsTUFBTUcsTUFBTUE7SUFFaEMsT0FBT1UsUUFBUVMsUUFBUU4sUUFBUU8sUUFBUUosUUFBUUU7QUFDbkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9yb2J1c3QtcHJlZGljYXRlcy9lc20vaW5jaXJjbGUuanM/MmFhMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2Vwc2lsb24sIHNwbGl0dGVyLCByZXN1bHRlcnJib3VuZCwgZXN0aW1hdGUsIHZlYywgc3VtLCBzdW1fdGhyZWUsIHNjYWxlfSBmcm9tICcuL3V0aWwuanMnO1xuXG5jb25zdCBpY2NlcnJib3VuZEEgPSAoMTAgKyA5NiAqIGVwc2lsb24pICogZXBzaWxvbjtcbmNvbnN0IGljY2VycmJvdW5kQiA9ICg0ICsgNDggKiBlcHNpbG9uKSAqIGVwc2lsb247XG5jb25zdCBpY2NlcnJib3VuZEMgPSAoNDQgKyA1NzYgKiBlcHNpbG9uKSAqIGVwc2lsb24gKiBlcHNpbG9uO1xuXG5jb25zdCBiYyA9IHZlYyg0KTtcbmNvbnN0IGNhID0gdmVjKDQpO1xuY29uc3QgYWIgPSB2ZWMoNCk7XG5jb25zdCBhYSA9IHZlYyg0KTtcbmNvbnN0IGJiID0gdmVjKDQpO1xuY29uc3QgY2MgPSB2ZWMoNCk7XG5jb25zdCB1ID0gdmVjKDQpO1xuY29uc3QgdiA9IHZlYyg0KTtcbmNvbnN0IGF4dGJjID0gdmVjKDgpO1xuY29uc3QgYXl0YmMgPSB2ZWMoOCk7XG5jb25zdCBieHRjYSA9IHZlYyg4KTtcbmNvbnN0IGJ5dGNhID0gdmVjKDgpO1xuY29uc3QgY3h0YWIgPSB2ZWMoOCk7XG5jb25zdCBjeXRhYiA9IHZlYyg4KTtcbmNvbnN0IGFidCA9IHZlYyg4KTtcbmNvbnN0IGJjdCA9IHZlYyg4KTtcbmNvbnN0IGNhdCA9IHZlYyg4KTtcbmNvbnN0IGFidHQgPSB2ZWMoNCk7XG5jb25zdCBiY3R0ID0gdmVjKDQpO1xuY29uc3QgY2F0dCA9IHZlYyg0KTtcblxuY29uc3QgXzggPSB2ZWMoOCk7XG5jb25zdCBfMTYgPSB2ZWMoMTYpO1xuY29uc3QgXzE2YiA9IHZlYygxNik7XG5jb25zdCBfMTZjID0gdmVjKDE2KTtcbmNvbnN0IF8zMiA9IHZlYygzMik7XG5jb25zdCBfMzJiID0gdmVjKDMyKTtcbmNvbnN0IF80OCA9IHZlYyg0OCk7XG5jb25zdCBfNjQgPSB2ZWMoNjQpO1xuXG5sZXQgZmluID0gdmVjKDExNTIpO1xubGV0IGZpbjIgPSB2ZWMoMTE1Mik7XG5cbmZ1bmN0aW9uIGZpbmFkZChmaW5sZW4sIGEsIGFsZW4pIHtcbiAgICBmaW5sZW4gPSBzdW0oZmlubGVuLCBmaW4sIGEsIGFsZW4sIGZpbjIpO1xuICAgIGNvbnN0IHRtcCA9IGZpbjsgZmluID0gZmluMjsgZmluMiA9IHRtcDtcbiAgICByZXR1cm4gZmlubGVuO1xufVxuXG5mdW5jdGlvbiBpbmNpcmNsZWFkYXB0KGF4LCBheSwgYngsIGJ5LCBjeCwgY3ksIGR4LCBkeSwgcGVybWFuZW50KSB7XG4gICAgbGV0IGZpbmxlbjtcbiAgICBsZXQgYWR4dGFpbCwgYmR4dGFpbCwgY2R4dGFpbCwgYWR5dGFpbCwgYmR5dGFpbCwgY2R5dGFpbDtcbiAgICBsZXQgYXh0YmNsZW4sIGF5dGJjbGVuLCBieHRjYWxlbiwgYnl0Y2FsZW4sIGN4dGFibGVuLCBjeXRhYmxlbjtcbiAgICBsZXQgYWJ0bGVuLCBiY3RsZW4sIGNhdGxlbjtcbiAgICBsZXQgYWJ0dGxlbiwgYmN0dGxlbiwgY2F0dGxlbjtcbiAgICBsZXQgbjEsIG4wO1xuXG4gICAgbGV0IGJ2aXJ0LCBjLCBhaGksIGFsbywgYmhpLCBibG8sIF9pLCBfaiwgXzAsIHMxLCBzMCwgdDEsIHQwLCB1MztcblxuICAgIGNvbnN0IGFkeCA9IGF4IC0gZHg7XG4gICAgY29uc3QgYmR4ID0gYnggLSBkeDtcbiAgICBjb25zdCBjZHggPSBjeCAtIGR4O1xuICAgIGNvbnN0IGFkeSA9IGF5IC0gZHk7XG4gICAgY29uc3QgYmR5ID0gYnkgLSBkeTtcbiAgICBjb25zdCBjZHkgPSBjeSAtIGR5O1xuXG4gICAgczEgPSBiZHggKiBjZHk7XG4gICAgYyA9IHNwbGl0dGVyICogYmR4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGJkeCk7XG4gICAgYWxvID0gYmR4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGNkeTtcbiAgICBiaGkgPSBjIC0gKGMgLSBjZHkpO1xuICAgIGJsbyA9IGNkeSAtIGJoaTtcbiAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgdDEgPSBjZHggKiBiZHk7XG4gICAgYyA9IHNwbGl0dGVyICogY2R4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGNkeCk7XG4gICAgYWxvID0gY2R4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGJkeTtcbiAgICBiaGkgPSBjIC0gKGMgLSBiZHkpO1xuICAgIGJsbyA9IGJkeSAtIGJoaTtcbiAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2kgPSBzMCAtIHQwO1xuICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICBiY1swXSA9IHMwIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDApO1xuICAgIF9qID0gczEgKyBfaTtcbiAgICBidmlydCA9IF9qIC0gczE7XG4gICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBfaSA9IF8wIC0gdDE7XG4gICAgYnZpcnQgPSBfMCAtIF9pO1xuICAgIGJjWzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgdTMgPSBfaiArIF9pO1xuICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICBiY1syXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIGJjWzNdID0gdTM7XG4gICAgczEgPSBjZHggKiBhZHk7XG4gICAgYyA9IHNwbGl0dGVyICogY2R4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGNkeCk7XG4gICAgYWxvID0gY2R4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGFkeTtcbiAgICBiaGkgPSBjIC0gKGMgLSBhZHkpO1xuICAgIGJsbyA9IGFkeSAtIGJoaTtcbiAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgdDEgPSBhZHggKiBjZHk7XG4gICAgYyA9IHNwbGl0dGVyICogYWR4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGFkeCk7XG4gICAgYWxvID0gYWR4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGNkeTtcbiAgICBiaGkgPSBjIC0gKGMgLSBjZHkpO1xuICAgIGJsbyA9IGNkeSAtIGJoaTtcbiAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2kgPSBzMCAtIHQwO1xuICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICBjYVswXSA9IHMwIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDApO1xuICAgIF9qID0gczEgKyBfaTtcbiAgICBidmlydCA9IF9qIC0gczE7XG4gICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBfaSA9IF8wIC0gdDE7XG4gICAgYnZpcnQgPSBfMCAtIF9pO1xuICAgIGNhWzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgdTMgPSBfaiArIF9pO1xuICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICBjYVsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIGNhWzNdID0gdTM7XG4gICAgczEgPSBhZHggKiBiZHk7XG4gICAgYyA9IHNwbGl0dGVyICogYWR4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGFkeCk7XG4gICAgYWxvID0gYWR4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGJkeTtcbiAgICBiaGkgPSBjIC0gKGMgLSBiZHkpO1xuICAgIGJsbyA9IGJkeSAtIGJoaTtcbiAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgdDEgPSBiZHggKiBhZHk7XG4gICAgYyA9IHNwbGl0dGVyICogYmR4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGJkeCk7XG4gICAgYWxvID0gYmR4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGFkeTtcbiAgICBiaGkgPSBjIC0gKGMgLSBhZHkpO1xuICAgIGJsbyA9IGFkeSAtIGJoaTtcbiAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2kgPSBzMCAtIHQwO1xuICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICBhYlswXSA9IHMwIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDApO1xuICAgIF9qID0gczEgKyBfaTtcbiAgICBidmlydCA9IF9qIC0gczE7XG4gICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBfaSA9IF8wIC0gdDE7XG4gICAgYnZpcnQgPSBfMCAtIF9pO1xuICAgIGFiWzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgdTMgPSBfaiArIF9pO1xuICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICBhYlsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIGFiWzNdID0gdTM7XG5cbiAgICBmaW5sZW4gPSBzdW0oXG4gICAgICAgIHN1bShcbiAgICAgICAgICAgIHN1bShcbiAgICAgICAgICAgICAgICBzY2FsZShzY2FsZSg0LCBiYywgYWR4LCBfOCksIF84LCBhZHgsIF8xNiksIF8xNixcbiAgICAgICAgICAgICAgICBzY2FsZShzY2FsZSg0LCBiYywgYWR5LCBfOCksIF84LCBhZHksIF8xNmIpLCBfMTZiLCBfMzIpLCBfMzIsXG4gICAgICAgICAgICBzdW0oXG4gICAgICAgICAgICAgICAgc2NhbGUoc2NhbGUoNCwgY2EsIGJkeCwgXzgpLCBfOCwgYmR4LCBfMTYpLCBfMTYsXG4gICAgICAgICAgICAgICAgc2NhbGUoc2NhbGUoNCwgY2EsIGJkeSwgXzgpLCBfOCwgYmR5LCBfMTZiKSwgXzE2YiwgXzMyYiksIF8zMmIsIF82NCksIF82NCxcbiAgICAgICAgc3VtKFxuICAgICAgICAgICAgc2NhbGUoc2NhbGUoNCwgYWIsIGNkeCwgXzgpLCBfOCwgY2R4LCBfMTYpLCBfMTYsXG4gICAgICAgICAgICBzY2FsZShzY2FsZSg0LCBhYiwgY2R5LCBfOCksIF84LCBjZHksIF8xNmIpLCBfMTZiLCBfMzIpLCBfMzIsIGZpbik7XG5cbiAgICBsZXQgZGV0ID0gZXN0aW1hdGUoZmlubGVuLCBmaW4pO1xuICAgIGxldCBlcnJib3VuZCA9IGljY2VycmJvdW5kQiAqIHBlcm1hbmVudDtcbiAgICBpZiAoZGV0ID49IGVycmJvdW5kIHx8IC1kZXQgPj0gZXJyYm91bmQpIHtcbiAgICAgICAgcmV0dXJuIGRldDtcbiAgICB9XG5cbiAgICBidmlydCA9IGF4IC0gYWR4O1xuICAgIGFkeHRhaWwgPSBheCAtIChhZHggKyBidmlydCkgKyAoYnZpcnQgLSBkeCk7XG4gICAgYnZpcnQgPSBheSAtIGFkeTtcbiAgICBhZHl0YWlsID0gYXkgLSAoYWR5ICsgYnZpcnQpICsgKGJ2aXJ0IC0gZHkpO1xuICAgIGJ2aXJ0ID0gYnggLSBiZHg7XG4gICAgYmR4dGFpbCA9IGJ4IC0gKGJkeCArIGJ2aXJ0KSArIChidmlydCAtIGR4KTtcbiAgICBidmlydCA9IGJ5IC0gYmR5O1xuICAgIGJkeXRhaWwgPSBieSAtIChiZHkgKyBidmlydCkgKyAoYnZpcnQgLSBkeSk7XG4gICAgYnZpcnQgPSBjeCAtIGNkeDtcbiAgICBjZHh0YWlsID0gY3ggLSAoY2R4ICsgYnZpcnQpICsgKGJ2aXJ0IC0gZHgpO1xuICAgIGJ2aXJ0ID0gY3kgLSBjZHk7XG4gICAgY2R5dGFpbCA9IGN5IC0gKGNkeSArIGJ2aXJ0KSArIChidmlydCAtIGR5KTtcbiAgICBpZiAoYWR4dGFpbCA9PT0gMCAmJiBiZHh0YWlsID09PSAwICYmIGNkeHRhaWwgPT09IDAgJiYgYWR5dGFpbCA9PT0gMCAmJiBiZHl0YWlsID09PSAwICYmIGNkeXRhaWwgPT09IDApIHtcbiAgICAgICAgcmV0dXJuIGRldDtcbiAgICB9XG5cbiAgICBlcnJib3VuZCA9IGljY2VycmJvdW5kQyAqIHBlcm1hbmVudCArIHJlc3VsdGVycmJvdW5kICogTWF0aC5hYnMoZGV0KTtcbiAgICBkZXQgKz0gKChhZHggKiBhZHggKyBhZHkgKiBhZHkpICogKChiZHggKiBjZHl0YWlsICsgY2R5ICogYmR4dGFpbCkgLSAoYmR5ICogY2R4dGFpbCArIGNkeCAqIGJkeXRhaWwpKSArXG4gICAgICAgIDIgKiAoYWR4ICogYWR4dGFpbCArIGFkeSAqIGFkeXRhaWwpICogKGJkeCAqIGNkeSAtIGJkeSAqIGNkeCkpICtcbiAgICAgICAgKChiZHggKiBiZHggKyBiZHkgKiBiZHkpICogKChjZHggKiBhZHl0YWlsICsgYWR5ICogY2R4dGFpbCkgLSAoY2R5ICogYWR4dGFpbCArIGFkeCAqIGNkeXRhaWwpKSArXG4gICAgICAgIDIgKiAoYmR4ICogYmR4dGFpbCArIGJkeSAqIGJkeXRhaWwpICogKGNkeCAqIGFkeSAtIGNkeSAqIGFkeCkpICtcbiAgICAgICAgKChjZHggKiBjZHggKyBjZHkgKiBjZHkpICogKChhZHggKiBiZHl0YWlsICsgYmR5ICogYWR4dGFpbCkgLSAoYWR5ICogYmR4dGFpbCArIGJkeCAqIGFkeXRhaWwpKSArXG4gICAgICAgIDIgKiAoY2R4ICogY2R4dGFpbCArIGNkeSAqIGNkeXRhaWwpICogKGFkeCAqIGJkeSAtIGFkeSAqIGJkeCkpO1xuXG4gICAgaWYgKGRldCA+PSBlcnJib3VuZCB8fCAtZGV0ID49IGVycmJvdW5kKSB7XG4gICAgICAgIHJldHVybiBkZXQ7XG4gICAgfVxuXG4gICAgaWYgKGJkeHRhaWwgIT09IDAgfHwgYmR5dGFpbCAhPT0gMCB8fCBjZHh0YWlsICE9PSAwIHx8IGNkeXRhaWwgIT09IDApIHtcbiAgICAgICAgczEgPSBhZHggKiBhZHg7XG4gICAgICAgIGMgPSBzcGxpdHRlciAqIGFkeDtcbiAgICAgICAgYWhpID0gYyAtIChjIC0gYWR4KTtcbiAgICAgICAgYWxvID0gYWR4IC0gYWhpO1xuICAgICAgICBzMCA9IGFsbyAqIGFsbyAtIChzMSAtIGFoaSAqIGFoaSAtIChhaGkgKyBhaGkpICogYWxvKTtcbiAgICAgICAgdDEgPSBhZHkgKiBhZHk7XG4gICAgICAgIGMgPSBzcGxpdHRlciAqIGFkeTtcbiAgICAgICAgYWhpID0gYyAtIChjIC0gYWR5KTtcbiAgICAgICAgYWxvID0gYWR5IC0gYWhpO1xuICAgICAgICB0MCA9IGFsbyAqIGFsbyAtICh0MSAtIGFoaSAqIGFoaSAtIChhaGkgKyBhaGkpICogYWxvKTtcbiAgICAgICAgX2kgPSBzMCArIHQwO1xuICAgICAgICBidmlydCA9IF9pIC0gczA7XG4gICAgICAgIGFhWzBdID0gczAgLSAoX2kgLSBidmlydCkgKyAodDAgLSBidmlydCk7XG4gICAgICAgIF9qID0gczEgKyBfaTtcbiAgICAgICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgICAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICBfaSA9IF8wICsgdDE7XG4gICAgICAgIGJ2aXJ0ID0gX2kgLSBfMDtcbiAgICAgICAgYWFbMV0gPSBfMCAtIChfaSAtIGJ2aXJ0KSArICh0MSAtIGJ2aXJ0KTtcbiAgICAgICAgdTMgPSBfaiArIF9pO1xuICAgICAgICBidmlydCA9IHUzIC0gX2o7XG4gICAgICAgIGFhWzJdID0gX2ogLSAodTMgLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgICAgIGFhWzNdID0gdTM7XG4gICAgfVxuICAgIGlmIChjZHh0YWlsICE9PSAwIHx8IGNkeXRhaWwgIT09IDAgfHwgYWR4dGFpbCAhPT0gMCB8fCBhZHl0YWlsICE9PSAwKSB7XG4gICAgICAgIHMxID0gYmR4ICogYmR4O1xuICAgICAgICBjID0gc3BsaXR0ZXIgKiBiZHg7XG4gICAgICAgIGFoaSA9IGMgLSAoYyAtIGJkeCk7XG4gICAgICAgIGFsbyA9IGJkeCAtIGFoaTtcbiAgICAgICAgczAgPSBhbG8gKiBhbG8gLSAoczEgLSBhaGkgKiBhaGkgLSAoYWhpICsgYWhpKSAqIGFsbyk7XG4gICAgICAgIHQxID0gYmR5ICogYmR5O1xuICAgICAgICBjID0gc3BsaXR0ZXIgKiBiZHk7XG4gICAgICAgIGFoaSA9IGMgLSAoYyAtIGJkeSk7XG4gICAgICAgIGFsbyA9IGJkeSAtIGFoaTtcbiAgICAgICAgdDAgPSBhbG8gKiBhbG8gLSAodDEgLSBhaGkgKiBhaGkgLSAoYWhpICsgYWhpKSAqIGFsbyk7XG4gICAgICAgIF9pID0gczAgKyB0MDtcbiAgICAgICAgYnZpcnQgPSBfaSAtIHMwO1xuICAgICAgICBiYlswXSA9IHMwIC0gKF9pIC0gYnZpcnQpICsgKHQwIC0gYnZpcnQpO1xuICAgICAgICBfaiA9IHMxICsgX2k7XG4gICAgICAgIGJ2aXJ0ID0gX2ogLSBzMTtcbiAgICAgICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICAgICAgX2kgPSBfMCArIHQxO1xuICAgICAgICBidmlydCA9IF9pIC0gXzA7XG4gICAgICAgIGJiWzFdID0gXzAgLSAoX2kgLSBidmlydCkgKyAodDEgLSBidmlydCk7XG4gICAgICAgIHUzID0gX2ogKyBfaTtcbiAgICAgICAgYnZpcnQgPSB1MyAtIF9qO1xuICAgICAgICBiYlsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICBiYlszXSA9IHUzO1xuICAgIH1cbiAgICBpZiAoYWR4dGFpbCAhPT0gMCB8fCBhZHl0YWlsICE9PSAwIHx8IGJkeHRhaWwgIT09IDAgfHwgYmR5dGFpbCAhPT0gMCkge1xuICAgICAgICBzMSA9IGNkeCAqIGNkeDtcbiAgICAgICAgYyA9IHNwbGl0dGVyICogY2R4O1xuICAgICAgICBhaGkgPSBjIC0gKGMgLSBjZHgpO1xuICAgICAgICBhbG8gPSBjZHggLSBhaGk7XG4gICAgICAgIHMwID0gYWxvICogYWxvIC0gKHMxIC0gYWhpICogYWhpIC0gKGFoaSArIGFoaSkgKiBhbG8pO1xuICAgICAgICB0MSA9IGNkeSAqIGNkeTtcbiAgICAgICAgYyA9IHNwbGl0dGVyICogY2R5O1xuICAgICAgICBhaGkgPSBjIC0gKGMgLSBjZHkpO1xuICAgICAgICBhbG8gPSBjZHkgLSBhaGk7XG4gICAgICAgIHQwID0gYWxvICogYWxvIC0gKHQxIC0gYWhpICogYWhpIC0gKGFoaSArIGFoaSkgKiBhbG8pO1xuICAgICAgICBfaSA9IHMwICsgdDA7XG4gICAgICAgIGJ2aXJ0ID0gX2kgLSBzMDtcbiAgICAgICAgY2NbMF0gPSBzMCAtIChfaSAtIGJ2aXJ0KSArICh0MCAtIGJ2aXJ0KTtcbiAgICAgICAgX2ogPSBzMSArIF9pO1xuICAgICAgICBidmlydCA9IF9qIC0gczE7XG4gICAgICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgICAgIF9pID0gXzAgKyB0MTtcbiAgICAgICAgYnZpcnQgPSBfaSAtIF8wO1xuICAgICAgICBjY1sxXSA9IF8wIC0gKF9pIC0gYnZpcnQpICsgKHQxIC0gYnZpcnQpO1xuICAgICAgICB1MyA9IF9qICsgX2k7XG4gICAgICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICAgICAgY2NbMl0gPSBfaiAtICh1MyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICAgICAgY2NbM10gPSB1MztcbiAgICB9XG5cbiAgICBpZiAoYWR4dGFpbCAhPT0gMCkge1xuICAgICAgICBheHRiY2xlbiA9IHNjYWxlKDQsIGJjLCBhZHh0YWlsLCBheHRiYyk7XG4gICAgICAgIGZpbmxlbiA9IGZpbmFkZChmaW5sZW4sIHN1bV90aHJlZShcbiAgICAgICAgICAgIHNjYWxlKGF4dGJjbGVuLCBheHRiYywgMiAqIGFkeCwgXzE2KSwgXzE2LFxuICAgICAgICAgICAgc2NhbGUoc2NhbGUoNCwgY2MsIGFkeHRhaWwsIF84KSwgXzgsIGJkeSwgXzE2YiksIF8xNmIsXG4gICAgICAgICAgICBzY2FsZShzY2FsZSg0LCBiYiwgYWR4dGFpbCwgXzgpLCBfOCwgLWNkeSwgXzE2YyksIF8xNmMsIF8zMiwgXzQ4KSwgXzQ4KTtcbiAgICB9XG4gICAgaWYgKGFkeXRhaWwgIT09IDApIHtcbiAgICAgICAgYXl0YmNsZW4gPSBzY2FsZSg0LCBiYywgYWR5dGFpbCwgYXl0YmMpO1xuICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzdW1fdGhyZWUoXG4gICAgICAgICAgICBzY2FsZShheXRiY2xlbiwgYXl0YmMsIDIgKiBhZHksIF8xNiksIF8xNixcbiAgICAgICAgICAgIHNjYWxlKHNjYWxlKDQsIGJiLCBhZHl0YWlsLCBfOCksIF84LCBjZHgsIF8xNmIpLCBfMTZiLFxuICAgICAgICAgICAgc2NhbGUoc2NhbGUoNCwgY2MsIGFkeXRhaWwsIF84KSwgXzgsIC1iZHgsIF8xNmMpLCBfMTZjLCBfMzIsIF80OCksIF80OCk7XG4gICAgfVxuICAgIGlmIChiZHh0YWlsICE9PSAwKSB7XG4gICAgICAgIGJ4dGNhbGVuID0gc2NhbGUoNCwgY2EsIGJkeHRhaWwsIGJ4dGNhKTtcbiAgICAgICAgZmlubGVuID0gZmluYWRkKGZpbmxlbiwgc3VtX3RocmVlKFxuICAgICAgICAgICAgc2NhbGUoYnh0Y2FsZW4sIGJ4dGNhLCAyICogYmR4LCBfMTYpLCBfMTYsXG4gICAgICAgICAgICBzY2FsZShzY2FsZSg0LCBhYSwgYmR4dGFpbCwgXzgpLCBfOCwgY2R5LCBfMTZiKSwgXzE2YixcbiAgICAgICAgICAgIHNjYWxlKHNjYWxlKDQsIGNjLCBiZHh0YWlsLCBfOCksIF84LCAtYWR5LCBfMTZjKSwgXzE2YywgXzMyLCBfNDgpLCBfNDgpO1xuICAgIH1cbiAgICBpZiAoYmR5dGFpbCAhPT0gMCkge1xuICAgICAgICBieXRjYWxlbiA9IHNjYWxlKDQsIGNhLCBiZHl0YWlsLCBieXRjYSk7XG4gICAgICAgIGZpbmxlbiA9IGZpbmFkZChmaW5sZW4sIHN1bV90aHJlZShcbiAgICAgICAgICAgIHNjYWxlKGJ5dGNhbGVuLCBieXRjYSwgMiAqIGJkeSwgXzE2KSwgXzE2LFxuICAgICAgICAgICAgc2NhbGUoc2NhbGUoNCwgY2MsIGJkeXRhaWwsIF84KSwgXzgsIGFkeCwgXzE2YiksIF8xNmIsXG4gICAgICAgICAgICBzY2FsZShzY2FsZSg0LCBhYSwgYmR5dGFpbCwgXzgpLCBfOCwgLWNkeCwgXzE2YyksIF8xNmMsIF8zMiwgXzQ4KSwgXzQ4KTtcbiAgICB9XG4gICAgaWYgKGNkeHRhaWwgIT09IDApIHtcbiAgICAgICAgY3h0YWJsZW4gPSBzY2FsZSg0LCBhYiwgY2R4dGFpbCwgY3h0YWIpO1xuICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzdW1fdGhyZWUoXG4gICAgICAgICAgICBzY2FsZShjeHRhYmxlbiwgY3h0YWIsIDIgKiBjZHgsIF8xNiksIF8xNixcbiAgICAgICAgICAgIHNjYWxlKHNjYWxlKDQsIGJiLCBjZHh0YWlsLCBfOCksIF84LCBhZHksIF8xNmIpLCBfMTZiLFxuICAgICAgICAgICAgc2NhbGUoc2NhbGUoNCwgYWEsIGNkeHRhaWwsIF84KSwgXzgsIC1iZHksIF8xNmMpLCBfMTZjLCBfMzIsIF80OCksIF80OCk7XG4gICAgfVxuICAgIGlmIChjZHl0YWlsICE9PSAwKSB7XG4gICAgICAgIGN5dGFibGVuID0gc2NhbGUoNCwgYWIsIGNkeXRhaWwsIGN5dGFiKTtcbiAgICAgICAgZmlubGVuID0gZmluYWRkKGZpbmxlbiwgc3VtX3RocmVlKFxuICAgICAgICAgICAgc2NhbGUoY3l0YWJsZW4sIGN5dGFiLCAyICogY2R5LCBfMTYpLCBfMTYsXG4gICAgICAgICAgICBzY2FsZShzY2FsZSg0LCBhYSwgY2R5dGFpbCwgXzgpLCBfOCwgYmR4LCBfMTZiKSwgXzE2YixcbiAgICAgICAgICAgIHNjYWxlKHNjYWxlKDQsIGJiLCBjZHl0YWlsLCBfOCksIF84LCAtYWR4LCBfMTZjKSwgXzE2YywgXzMyLCBfNDgpLCBfNDgpO1xuICAgIH1cblxuICAgIGlmIChhZHh0YWlsICE9PSAwIHx8IGFkeXRhaWwgIT09IDApIHtcbiAgICAgICAgaWYgKGJkeHRhaWwgIT09IDAgfHwgYmR5dGFpbCAhPT0gMCB8fCBjZHh0YWlsICE9PSAwIHx8IGNkeXRhaWwgIT09IDApIHtcbiAgICAgICAgICAgIHMxID0gYmR4dGFpbCAqIGNkeTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGJkeHRhaWw7XG4gICAgICAgICAgICBhaGkgPSBjIC0gKGMgLSBiZHh0YWlsKTtcbiAgICAgICAgICAgIGFsbyA9IGJkeHRhaWwgLSBhaGk7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiBjZHk7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBjZHkpO1xuICAgICAgICAgICAgYmxvID0gY2R5IC0gYmhpO1xuICAgICAgICAgICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgICAgICAgICAgdDEgPSBiZHggKiBjZHl0YWlsO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYmR4O1xuICAgICAgICAgICAgYWhpID0gYyAtIChjIC0gYmR4KTtcbiAgICAgICAgICAgIGFsbyA9IGJkeCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGNkeXRhaWw7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBjZHl0YWlsKTtcbiAgICAgICAgICAgIGJsbyA9IGNkeXRhaWwgLSBiaGk7XG4gICAgICAgICAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICBfaSA9IHMwICsgdDA7XG4gICAgICAgICAgICBidmlydCA9IF9pIC0gczA7XG4gICAgICAgICAgICB1WzBdID0gczAgLSAoX2kgLSBidmlydCkgKyAodDAgLSBidmlydCk7XG4gICAgICAgICAgICBfaiA9IHMxICsgX2k7XG4gICAgICAgICAgICBidmlydCA9IF9qIC0gczE7XG4gICAgICAgICAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgX2kgPSBfMCArIHQxO1xuICAgICAgICAgICAgYnZpcnQgPSBfaSAtIF8wO1xuICAgICAgICAgICAgdVsxXSA9IF8wIC0gKF9pIC0gYnZpcnQpICsgKHQxIC0gYnZpcnQpO1xuICAgICAgICAgICAgdTMgPSBfaiArIF9pO1xuICAgICAgICAgICAgYnZpcnQgPSB1MyAtIF9qO1xuICAgICAgICAgICAgdVsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgdVszXSA9IHUzO1xuICAgICAgICAgICAgczEgPSBjZHh0YWlsICogLWJkeTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGNkeHRhaWw7XG4gICAgICAgICAgICBhaGkgPSBjIC0gKGMgLSBjZHh0YWlsKTtcbiAgICAgICAgICAgIGFsbyA9IGNkeHRhaWwgLSBhaGk7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiAtYmR5O1xuICAgICAgICAgICAgYmhpID0gYyAtIChjIC0gLWJkeSk7XG4gICAgICAgICAgICBibG8gPSAtYmR5IC0gYmhpO1xuICAgICAgICAgICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgICAgICAgICAgdDEgPSBjZHggKiAtYmR5dGFpbDtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGNkeDtcbiAgICAgICAgICAgIGFoaSA9IGMgLSAoYyAtIGNkeCk7XG4gICAgICAgICAgICBhbG8gPSBjZHggLSBhaGk7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiAtYmR5dGFpbDtcbiAgICAgICAgICAgIGJoaSA9IGMgLSAoYyAtIC1iZHl0YWlsKTtcbiAgICAgICAgICAgIGJsbyA9IC1iZHl0YWlsIC0gYmhpO1xuICAgICAgICAgICAgdDAgPSBhbG8gKiBibG8gLSAodDEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgICAgICAgICAgX2kgPSBzMCArIHQwO1xuICAgICAgICAgICAgYnZpcnQgPSBfaSAtIHMwO1xuICAgICAgICAgICAgdlswXSA9IHMwIC0gKF9pIC0gYnZpcnQpICsgKHQwIC0gYnZpcnQpO1xuICAgICAgICAgICAgX2ogPSBzMSArIF9pO1xuICAgICAgICAgICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgICAgICAgICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICAgICAgICAgIF9pID0gXzAgKyB0MTtcbiAgICAgICAgICAgIGJ2aXJ0ID0gX2kgLSBfMDtcbiAgICAgICAgICAgIHZbMV0gPSBfMCAtIChfaSAtIGJ2aXJ0KSArICh0MSAtIGJ2aXJ0KTtcbiAgICAgICAgICAgIHUzID0gX2ogKyBfaTtcbiAgICAgICAgICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICAgICAgICAgIHZbMl0gPSBfaiAtICh1MyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICAgICAgICAgIHZbM10gPSB1MztcbiAgICAgICAgICAgIGJjdGxlbiA9IHN1bSg0LCB1LCA0LCB2LCBiY3QpO1xuICAgICAgICAgICAgczEgPSBiZHh0YWlsICogY2R5dGFpbDtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGJkeHRhaWw7XG4gICAgICAgICAgICBhaGkgPSBjIC0gKGMgLSBiZHh0YWlsKTtcbiAgICAgICAgICAgIGFsbyA9IGJkeHRhaWwgLSBhaGk7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiBjZHl0YWlsO1xuICAgICAgICAgICAgYmhpID0gYyAtIChjIC0gY2R5dGFpbCk7XG4gICAgICAgICAgICBibG8gPSBjZHl0YWlsIC0gYmhpO1xuICAgICAgICAgICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgICAgICAgICAgdDEgPSBjZHh0YWlsICogYmR5dGFpbDtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGNkeHRhaWw7XG4gICAgICAgICAgICBhaGkgPSBjIC0gKGMgLSBjZHh0YWlsKTtcbiAgICAgICAgICAgIGFsbyA9IGNkeHRhaWwgLSBhaGk7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiBiZHl0YWlsO1xuICAgICAgICAgICAgYmhpID0gYyAtIChjIC0gYmR5dGFpbCk7XG4gICAgICAgICAgICBibG8gPSBiZHl0YWlsIC0gYmhpO1xuICAgICAgICAgICAgdDAgPSBhbG8gKiBibG8gLSAodDEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgICAgICAgICAgX2kgPSBzMCAtIHQwO1xuICAgICAgICAgICAgYnZpcnQgPSBzMCAtIF9pO1xuICAgICAgICAgICAgYmN0dFswXSA9IHMwIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDApO1xuICAgICAgICAgICAgX2ogPSBzMSArIF9pO1xuICAgICAgICAgICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgICAgICAgICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICAgICAgICAgIF9pID0gXzAgLSB0MTtcbiAgICAgICAgICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICAgICAgICAgIGJjdHRbMV0gPSBfMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQxKTtcbiAgICAgICAgICAgIHUzID0gX2ogKyBfaTtcbiAgICAgICAgICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICAgICAgICAgIGJjdHRbMl0gPSBfaiAtICh1MyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICAgICAgICAgIGJjdHRbM10gPSB1MztcbiAgICAgICAgICAgIGJjdHRsZW4gPSA0O1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgYmN0WzBdID0gMDtcbiAgICAgICAgICAgIGJjdGxlbiA9IDE7XG4gICAgICAgICAgICBiY3R0WzBdID0gMDtcbiAgICAgICAgICAgIGJjdHRsZW4gPSAxO1xuICAgICAgICB9XG4gICAgICAgIGlmIChhZHh0YWlsICE9PSAwKSB7XG4gICAgICAgICAgICBjb25zdCBsZW4gPSBzY2FsZShiY3RsZW4sIGJjdCwgYWR4dGFpbCwgXzE2Yyk7XG4gICAgICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzdW0oXG4gICAgICAgICAgICAgICAgc2NhbGUoYXh0YmNsZW4sIGF4dGJjLCBhZHh0YWlsLCBfMTYpLCBfMTYsXG4gICAgICAgICAgICAgICAgc2NhbGUobGVuLCBfMTZjLCAyICogYWR4LCBfMzIpLCBfMzIsIF80OCksIF80OCk7XG5cbiAgICAgICAgICAgIGNvbnN0IGxlbjIgPSBzY2FsZShiY3R0bGVuLCBiY3R0LCBhZHh0YWlsLCBfOCk7XG4gICAgICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzdW1fdGhyZWUoXG4gICAgICAgICAgICAgICAgc2NhbGUobGVuMiwgXzgsIDIgKiBhZHgsIF8xNiksIF8xNixcbiAgICAgICAgICAgICAgICBzY2FsZShsZW4yLCBfOCwgYWR4dGFpbCwgXzE2YiksIF8xNmIsXG4gICAgICAgICAgICAgICAgc2NhbGUobGVuLCBfMTZjLCBhZHh0YWlsLCBfMzIpLCBfMzIsIF8zMmIsIF82NCksIF82NCk7XG5cbiAgICAgICAgICAgIGlmIChiZHl0YWlsICE9PSAwKSB7XG4gICAgICAgICAgICAgICAgZmlubGVuID0gZmluYWRkKGZpbmxlbiwgc2NhbGUoc2NhbGUoNCwgY2MsIGFkeHRhaWwsIF84KSwgXzgsIGJkeXRhaWwsIF8xNiksIF8xNik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoY2R5dGFpbCAhPT0gMCkge1xuICAgICAgICAgICAgICAgIGZpbmxlbiA9IGZpbmFkZChmaW5sZW4sIHNjYWxlKHNjYWxlKDQsIGJiLCAtYWR4dGFpbCwgXzgpLCBfOCwgY2R5dGFpbCwgXzE2KSwgXzE2KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoYWR5dGFpbCAhPT0gMCkge1xuICAgICAgICAgICAgY29uc3QgbGVuID0gc2NhbGUoYmN0bGVuLCBiY3QsIGFkeXRhaWwsIF8xNmMpO1xuICAgICAgICAgICAgZmlubGVuID0gZmluYWRkKGZpbmxlbiwgc3VtKFxuICAgICAgICAgICAgICAgIHNjYWxlKGF5dGJjbGVuLCBheXRiYywgYWR5dGFpbCwgXzE2KSwgXzE2LFxuICAgICAgICAgICAgICAgIHNjYWxlKGxlbiwgXzE2YywgMiAqIGFkeSwgXzMyKSwgXzMyLCBfNDgpLCBfNDgpO1xuXG4gICAgICAgICAgICBjb25zdCBsZW4yID0gc2NhbGUoYmN0dGxlbiwgYmN0dCwgYWR5dGFpbCwgXzgpO1xuICAgICAgICAgICAgZmlubGVuID0gZmluYWRkKGZpbmxlbiwgc3VtX3RocmVlKFxuICAgICAgICAgICAgICAgIHNjYWxlKGxlbjIsIF84LCAyICogYWR5LCBfMTYpLCBfMTYsXG4gICAgICAgICAgICAgICAgc2NhbGUobGVuMiwgXzgsIGFkeXRhaWwsIF8xNmIpLCBfMTZiLFxuICAgICAgICAgICAgICAgIHNjYWxlKGxlbiwgXzE2YywgYWR5dGFpbCwgXzMyKSwgXzMyLCBfMzJiLCBfNjQpLCBfNjQpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChiZHh0YWlsICE9PSAwIHx8IGJkeXRhaWwgIT09IDApIHtcbiAgICAgICAgaWYgKGNkeHRhaWwgIT09IDAgfHwgY2R5dGFpbCAhPT0gMCB8fCBhZHh0YWlsICE9PSAwIHx8IGFkeXRhaWwgIT09IDApIHtcbiAgICAgICAgICAgIHMxID0gY2R4dGFpbCAqIGFkeTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGNkeHRhaWw7XG4gICAgICAgICAgICBhaGkgPSBjIC0gKGMgLSBjZHh0YWlsKTtcbiAgICAgICAgICAgIGFsbyA9IGNkeHRhaWwgLSBhaGk7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiBhZHk7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBhZHkpO1xuICAgICAgICAgICAgYmxvID0gYWR5IC0gYmhpO1xuICAgICAgICAgICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgICAgICAgICAgdDEgPSBjZHggKiBhZHl0YWlsO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogY2R4O1xuICAgICAgICAgICAgYWhpID0gYyAtIChjIC0gY2R4KTtcbiAgICAgICAgICAgIGFsbyA9IGNkeCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGFkeXRhaWw7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBhZHl0YWlsKTtcbiAgICAgICAgICAgIGJsbyA9IGFkeXRhaWwgLSBiaGk7XG4gICAgICAgICAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICBfaSA9IHMwICsgdDA7XG4gICAgICAgICAgICBidmlydCA9IF9pIC0gczA7XG4gICAgICAgICAgICB1WzBdID0gczAgLSAoX2kgLSBidmlydCkgKyAodDAgLSBidmlydCk7XG4gICAgICAgICAgICBfaiA9IHMxICsgX2k7XG4gICAgICAgICAgICBidmlydCA9IF9qIC0gczE7XG4gICAgICAgICAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgX2kgPSBfMCArIHQxO1xuICAgICAgICAgICAgYnZpcnQgPSBfaSAtIF8wO1xuICAgICAgICAgICAgdVsxXSA9IF8wIC0gKF9pIC0gYnZpcnQpICsgKHQxIC0gYnZpcnQpO1xuICAgICAgICAgICAgdTMgPSBfaiArIF9pO1xuICAgICAgICAgICAgYnZpcnQgPSB1MyAtIF9qO1xuICAgICAgICAgICAgdVsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgdVszXSA9IHUzO1xuICAgICAgICAgICAgbjEgPSAtY2R5O1xuICAgICAgICAgICAgbjAgPSAtY2R5dGFpbDtcbiAgICAgICAgICAgIHMxID0gYWR4dGFpbCAqIG4xO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYWR4dGFpbDtcbiAgICAgICAgICAgIGFoaSA9IGMgLSAoYyAtIGFkeHRhaWwpO1xuICAgICAgICAgICAgYWxvID0gYWR4dGFpbCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIG4xO1xuICAgICAgICAgICAgYmhpID0gYyAtIChjIC0gbjEpO1xuICAgICAgICAgICAgYmxvID0gbjEgLSBiaGk7XG4gICAgICAgICAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICB0MSA9IGFkeCAqIG4wO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYWR4O1xuICAgICAgICAgICAgYWhpID0gYyAtIChjIC0gYWR4KTtcbiAgICAgICAgICAgIGFsbyA9IGFkeCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIG4wO1xuICAgICAgICAgICAgYmhpID0gYyAtIChjIC0gbjApO1xuICAgICAgICAgICAgYmxvID0gbjAgLSBiaGk7XG4gICAgICAgICAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICBfaSA9IHMwICsgdDA7XG4gICAgICAgICAgICBidmlydCA9IF9pIC0gczA7XG4gICAgICAgICAgICB2WzBdID0gczAgLSAoX2kgLSBidmlydCkgKyAodDAgLSBidmlydCk7XG4gICAgICAgICAgICBfaiA9IHMxICsgX2k7XG4gICAgICAgICAgICBidmlydCA9IF9qIC0gczE7XG4gICAgICAgICAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgX2kgPSBfMCArIHQxO1xuICAgICAgICAgICAgYnZpcnQgPSBfaSAtIF8wO1xuICAgICAgICAgICAgdlsxXSA9IF8wIC0gKF9pIC0gYnZpcnQpICsgKHQxIC0gYnZpcnQpO1xuICAgICAgICAgICAgdTMgPSBfaiArIF9pO1xuICAgICAgICAgICAgYnZpcnQgPSB1MyAtIF9qO1xuICAgICAgICAgICAgdlsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgdlszXSA9IHUzO1xuICAgICAgICAgICAgY2F0bGVuID0gc3VtKDQsIHUsIDQsIHYsIGNhdCk7XG4gICAgICAgICAgICBzMSA9IGNkeHRhaWwgKiBhZHl0YWlsO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogY2R4dGFpbDtcbiAgICAgICAgICAgIGFoaSA9IGMgLSAoYyAtIGNkeHRhaWwpO1xuICAgICAgICAgICAgYWxvID0gY2R4dGFpbCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGFkeXRhaWw7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBhZHl0YWlsKTtcbiAgICAgICAgICAgIGJsbyA9IGFkeXRhaWwgLSBiaGk7XG4gICAgICAgICAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICB0MSA9IGFkeHRhaWwgKiBjZHl0YWlsO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYWR4dGFpbDtcbiAgICAgICAgICAgIGFoaSA9IGMgLSAoYyAtIGFkeHRhaWwpO1xuICAgICAgICAgICAgYWxvID0gYWR4dGFpbCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGNkeXRhaWw7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBjZHl0YWlsKTtcbiAgICAgICAgICAgIGJsbyA9IGNkeXRhaWwgLSBiaGk7XG4gICAgICAgICAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICBfaSA9IHMwIC0gdDA7XG4gICAgICAgICAgICBidmlydCA9IHMwIC0gX2k7XG4gICAgICAgICAgICBjYXR0WzBdID0gczAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MCk7XG4gICAgICAgICAgICBfaiA9IHMxICsgX2k7XG4gICAgICAgICAgICBidmlydCA9IF9qIC0gczE7XG4gICAgICAgICAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgX2kgPSBfMCAtIHQxO1xuICAgICAgICAgICAgYnZpcnQgPSBfMCAtIF9pO1xuICAgICAgICAgICAgY2F0dFsxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgICAgICAgICAgdTMgPSBfaiArIF9pO1xuICAgICAgICAgICAgYnZpcnQgPSB1MyAtIF9qO1xuICAgICAgICAgICAgY2F0dFsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgY2F0dFszXSA9IHUzO1xuICAgICAgICAgICAgY2F0dGxlbiA9IDQ7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjYXRbMF0gPSAwO1xuICAgICAgICAgICAgY2F0bGVuID0gMTtcbiAgICAgICAgICAgIGNhdHRbMF0gPSAwO1xuICAgICAgICAgICAgY2F0dGxlbiA9IDE7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGJkeHRhaWwgIT09IDApIHtcbiAgICAgICAgICAgIGNvbnN0IGxlbiA9IHNjYWxlKGNhdGxlbiwgY2F0LCBiZHh0YWlsLCBfMTZjKTtcbiAgICAgICAgICAgIGZpbmxlbiA9IGZpbmFkZChmaW5sZW4sIHN1bShcbiAgICAgICAgICAgICAgICBzY2FsZShieHRjYWxlbiwgYnh0Y2EsIGJkeHRhaWwsIF8xNiksIF8xNixcbiAgICAgICAgICAgICAgICBzY2FsZShsZW4sIF8xNmMsIDIgKiBiZHgsIF8zMiksIF8zMiwgXzQ4KSwgXzQ4KTtcblxuICAgICAgICAgICAgY29uc3QgbGVuMiA9IHNjYWxlKGNhdHRsZW4sIGNhdHQsIGJkeHRhaWwsIF84KTtcbiAgICAgICAgICAgIGZpbmxlbiA9IGZpbmFkZChmaW5sZW4sIHN1bV90aHJlZShcbiAgICAgICAgICAgICAgICBzY2FsZShsZW4yLCBfOCwgMiAqIGJkeCwgXzE2KSwgXzE2LFxuICAgICAgICAgICAgICAgIHNjYWxlKGxlbjIsIF84LCBiZHh0YWlsLCBfMTZiKSwgXzE2YixcbiAgICAgICAgICAgICAgICBzY2FsZShsZW4sIF8xNmMsIGJkeHRhaWwsIF8zMiksIF8zMiwgXzMyYiwgXzY0KSwgXzY0KTtcblxuICAgICAgICAgICAgaWYgKGNkeXRhaWwgIT09IDApIHtcbiAgICAgICAgICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzY2FsZShzY2FsZSg0LCBhYSwgYmR4dGFpbCwgXzgpLCBfOCwgY2R5dGFpbCwgXzE2KSwgXzE2KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChhZHl0YWlsICE9PSAwKSB7XG4gICAgICAgICAgICAgICAgZmlubGVuID0gZmluYWRkKGZpbmxlbiwgc2NhbGUoc2NhbGUoNCwgY2MsIC1iZHh0YWlsLCBfOCksIF84LCBhZHl0YWlsLCBfMTYpLCBfMTYpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChiZHl0YWlsICE9PSAwKSB7XG4gICAgICAgICAgICBjb25zdCBsZW4gPSBzY2FsZShjYXRsZW4sIGNhdCwgYmR5dGFpbCwgXzE2Yyk7XG4gICAgICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzdW0oXG4gICAgICAgICAgICAgICAgc2NhbGUoYnl0Y2FsZW4sIGJ5dGNhLCBiZHl0YWlsLCBfMTYpLCBfMTYsXG4gICAgICAgICAgICAgICAgc2NhbGUobGVuLCBfMTZjLCAyICogYmR5LCBfMzIpLCBfMzIsIF80OCksIF80OCk7XG5cbiAgICAgICAgICAgIGNvbnN0IGxlbjIgPSBzY2FsZShjYXR0bGVuLCBjYXR0LCBiZHl0YWlsLCBfOCk7XG4gICAgICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzdW1fdGhyZWUoXG4gICAgICAgICAgICAgICAgc2NhbGUobGVuMiwgXzgsIDIgKiBiZHksIF8xNiksIF8xNixcbiAgICAgICAgICAgICAgICBzY2FsZShsZW4yLCBfOCwgYmR5dGFpbCwgXzE2YiksIF8xNmIsXG4gICAgICAgICAgICAgICAgc2NhbGUobGVuLCBfMTZjLCBiZHl0YWlsLCBfMzIpLCBfMzIsICBfMzJiLCBfNjQpLCBfNjQpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChjZHh0YWlsICE9PSAwIHx8IGNkeXRhaWwgIT09IDApIHtcbiAgICAgICAgaWYgKGFkeHRhaWwgIT09IDAgfHwgYWR5dGFpbCAhPT0gMCB8fCBiZHh0YWlsICE9PSAwIHx8IGJkeXRhaWwgIT09IDApIHtcbiAgICAgICAgICAgIHMxID0gYWR4dGFpbCAqIGJkeTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGFkeHRhaWw7XG4gICAgICAgICAgICBhaGkgPSBjIC0gKGMgLSBhZHh0YWlsKTtcbiAgICAgICAgICAgIGFsbyA9IGFkeHRhaWwgLSBhaGk7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiBiZHk7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBiZHkpO1xuICAgICAgICAgICAgYmxvID0gYmR5IC0gYmhpO1xuICAgICAgICAgICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgICAgICAgICAgdDEgPSBhZHggKiBiZHl0YWlsO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYWR4O1xuICAgICAgICAgICAgYWhpID0gYyAtIChjIC0gYWR4KTtcbiAgICAgICAgICAgIGFsbyA9IGFkeCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGJkeXRhaWw7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBiZHl0YWlsKTtcbiAgICAgICAgICAgIGJsbyA9IGJkeXRhaWwgLSBiaGk7XG4gICAgICAgICAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICBfaSA9IHMwICsgdDA7XG4gICAgICAgICAgICBidmlydCA9IF9pIC0gczA7XG4gICAgICAgICAgICB1WzBdID0gczAgLSAoX2kgLSBidmlydCkgKyAodDAgLSBidmlydCk7XG4gICAgICAgICAgICBfaiA9IHMxICsgX2k7XG4gICAgICAgICAgICBidmlydCA9IF9qIC0gczE7XG4gICAgICAgICAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgX2kgPSBfMCArIHQxO1xuICAgICAgICAgICAgYnZpcnQgPSBfaSAtIF8wO1xuICAgICAgICAgICAgdVsxXSA9IF8wIC0gKF9pIC0gYnZpcnQpICsgKHQxIC0gYnZpcnQpO1xuICAgICAgICAgICAgdTMgPSBfaiArIF9pO1xuICAgICAgICAgICAgYnZpcnQgPSB1MyAtIF9qO1xuICAgICAgICAgICAgdVsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgdVszXSA9IHUzO1xuICAgICAgICAgICAgbjEgPSAtYWR5O1xuICAgICAgICAgICAgbjAgPSAtYWR5dGFpbDtcbiAgICAgICAgICAgIHMxID0gYmR4dGFpbCAqIG4xO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYmR4dGFpbDtcbiAgICAgICAgICAgIGFoaSA9IGMgLSAoYyAtIGJkeHRhaWwpO1xuICAgICAgICAgICAgYWxvID0gYmR4dGFpbCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIG4xO1xuICAgICAgICAgICAgYmhpID0gYyAtIChjIC0gbjEpO1xuICAgICAgICAgICAgYmxvID0gbjEgLSBiaGk7XG4gICAgICAgICAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICB0MSA9IGJkeCAqIG4wO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYmR4O1xuICAgICAgICAgICAgYWhpID0gYyAtIChjIC0gYmR4KTtcbiAgICAgICAgICAgIGFsbyA9IGJkeCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIG4wO1xuICAgICAgICAgICAgYmhpID0gYyAtIChjIC0gbjApO1xuICAgICAgICAgICAgYmxvID0gbjAgLSBiaGk7XG4gICAgICAgICAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICBfaSA9IHMwICsgdDA7XG4gICAgICAgICAgICBidmlydCA9IF9pIC0gczA7XG4gICAgICAgICAgICB2WzBdID0gczAgLSAoX2kgLSBidmlydCkgKyAodDAgLSBidmlydCk7XG4gICAgICAgICAgICBfaiA9IHMxICsgX2k7XG4gICAgICAgICAgICBidmlydCA9IF9qIC0gczE7XG4gICAgICAgICAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgX2kgPSBfMCArIHQxO1xuICAgICAgICAgICAgYnZpcnQgPSBfaSAtIF8wO1xuICAgICAgICAgICAgdlsxXSA9IF8wIC0gKF9pIC0gYnZpcnQpICsgKHQxIC0gYnZpcnQpO1xuICAgICAgICAgICAgdTMgPSBfaiArIF9pO1xuICAgICAgICAgICAgYnZpcnQgPSB1MyAtIF9qO1xuICAgICAgICAgICAgdlsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgdlszXSA9IHUzO1xuICAgICAgICAgICAgYWJ0bGVuID0gc3VtKDQsIHUsIDQsIHYsIGFidCk7XG4gICAgICAgICAgICBzMSA9IGFkeHRhaWwgKiBiZHl0YWlsO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYWR4dGFpbDtcbiAgICAgICAgICAgIGFoaSA9IGMgLSAoYyAtIGFkeHRhaWwpO1xuICAgICAgICAgICAgYWxvID0gYWR4dGFpbCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGJkeXRhaWw7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBiZHl0YWlsKTtcbiAgICAgICAgICAgIGJsbyA9IGJkeXRhaWwgLSBiaGk7XG4gICAgICAgICAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICB0MSA9IGJkeHRhaWwgKiBhZHl0YWlsO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYmR4dGFpbDtcbiAgICAgICAgICAgIGFoaSA9IGMgLSAoYyAtIGJkeHRhaWwpO1xuICAgICAgICAgICAgYWxvID0gYmR4dGFpbCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGFkeXRhaWw7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBhZHl0YWlsKTtcbiAgICAgICAgICAgIGJsbyA9IGFkeXRhaWwgLSBiaGk7XG4gICAgICAgICAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICBfaSA9IHMwIC0gdDA7XG4gICAgICAgICAgICBidmlydCA9IHMwIC0gX2k7XG4gICAgICAgICAgICBhYnR0WzBdID0gczAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MCk7XG4gICAgICAgICAgICBfaiA9IHMxICsgX2k7XG4gICAgICAgICAgICBidmlydCA9IF9qIC0gczE7XG4gICAgICAgICAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgX2kgPSBfMCAtIHQxO1xuICAgICAgICAgICAgYnZpcnQgPSBfMCAtIF9pO1xuICAgICAgICAgICAgYWJ0dFsxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgICAgICAgICAgdTMgPSBfaiArIF9pO1xuICAgICAgICAgICAgYnZpcnQgPSB1MyAtIF9qO1xuICAgICAgICAgICAgYWJ0dFsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgICAgICAgICAgYWJ0dFszXSA9IHUzO1xuICAgICAgICAgICAgYWJ0dGxlbiA9IDQ7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBhYnRbMF0gPSAwO1xuICAgICAgICAgICAgYWJ0bGVuID0gMTtcbiAgICAgICAgICAgIGFidHRbMF0gPSAwO1xuICAgICAgICAgICAgYWJ0dGxlbiA9IDE7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGNkeHRhaWwgIT09IDApIHtcbiAgICAgICAgICAgIGNvbnN0IGxlbiA9IHNjYWxlKGFidGxlbiwgYWJ0LCBjZHh0YWlsLCBfMTZjKTtcbiAgICAgICAgICAgIGZpbmxlbiA9IGZpbmFkZChmaW5sZW4sIHN1bShcbiAgICAgICAgICAgICAgICBzY2FsZShjeHRhYmxlbiwgY3h0YWIsIGNkeHRhaWwsIF8xNiksIF8xNixcbiAgICAgICAgICAgICAgICBzY2FsZShsZW4sIF8xNmMsIDIgKiBjZHgsIF8zMiksIF8zMiwgXzQ4KSwgXzQ4KTtcblxuICAgICAgICAgICAgY29uc3QgbGVuMiA9IHNjYWxlKGFidHRsZW4sIGFidHQsIGNkeHRhaWwsIF84KTtcbiAgICAgICAgICAgIGZpbmxlbiA9IGZpbmFkZChmaW5sZW4sIHN1bV90aHJlZShcbiAgICAgICAgICAgICAgICBzY2FsZShsZW4yLCBfOCwgMiAqIGNkeCwgXzE2KSwgXzE2LFxuICAgICAgICAgICAgICAgIHNjYWxlKGxlbjIsIF84LCBjZHh0YWlsLCBfMTZiKSwgXzE2YixcbiAgICAgICAgICAgICAgICBzY2FsZShsZW4sIF8xNmMsIGNkeHRhaWwsIF8zMiksIF8zMiwgXzMyYiwgXzY0KSwgXzY0KTtcblxuICAgICAgICAgICAgaWYgKGFkeXRhaWwgIT09IDApIHtcbiAgICAgICAgICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzY2FsZShzY2FsZSg0LCBiYiwgY2R4dGFpbCwgXzgpLCBfOCwgYWR5dGFpbCwgXzE2KSwgXzE2KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChiZHl0YWlsICE9PSAwKSB7XG4gICAgICAgICAgICAgICAgZmlubGVuID0gZmluYWRkKGZpbmxlbiwgc2NhbGUoc2NhbGUoNCwgYWEsIC1jZHh0YWlsLCBfOCksIF84LCBiZHl0YWlsLCBfMTYpLCBfMTYpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChjZHl0YWlsICE9PSAwKSB7XG4gICAgICAgICAgICBjb25zdCBsZW4gPSBzY2FsZShhYnRsZW4sIGFidCwgY2R5dGFpbCwgXzE2Yyk7XG4gICAgICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzdW0oXG4gICAgICAgICAgICAgICAgc2NhbGUoY3l0YWJsZW4sIGN5dGFiLCBjZHl0YWlsLCBfMTYpLCBfMTYsXG4gICAgICAgICAgICAgICAgc2NhbGUobGVuLCBfMTZjLCAyICogY2R5LCBfMzIpLCBfMzIsIF80OCksIF80OCk7XG5cbiAgICAgICAgICAgIGNvbnN0IGxlbjIgPSBzY2FsZShhYnR0bGVuLCBhYnR0LCBjZHl0YWlsLCBfOCk7XG4gICAgICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzdW1fdGhyZWUoXG4gICAgICAgICAgICAgICAgc2NhbGUobGVuMiwgXzgsIDIgKiBjZHksIF8xNiksIF8xNixcbiAgICAgICAgICAgICAgICBzY2FsZShsZW4yLCBfOCwgY2R5dGFpbCwgXzE2YiksIF8xNmIsXG4gICAgICAgICAgICAgICAgc2NhbGUobGVuLCBfMTZjLCBjZHl0YWlsLCBfMzIpLCBfMzIsIF8zMmIsIF82NCksIF82NCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gZmluW2ZpbmxlbiAtIDFdO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaW5jaXJjbGUoYXgsIGF5LCBieCwgYnksIGN4LCBjeSwgZHgsIGR5KSB7XG4gICAgY29uc3QgYWR4ID0gYXggLSBkeDtcbiAgICBjb25zdCBiZHggPSBieCAtIGR4O1xuICAgIGNvbnN0IGNkeCA9IGN4IC0gZHg7XG4gICAgY29uc3QgYWR5ID0gYXkgLSBkeTtcbiAgICBjb25zdCBiZHkgPSBieSAtIGR5O1xuICAgIGNvbnN0IGNkeSA9IGN5IC0gZHk7XG5cbiAgICBjb25zdCBiZHhjZHkgPSBiZHggKiBjZHk7XG4gICAgY29uc3QgY2R4YmR5ID0gY2R4ICogYmR5O1xuICAgIGNvbnN0IGFsaWZ0ID0gYWR4ICogYWR4ICsgYWR5ICogYWR5O1xuXG4gICAgY29uc3QgY2R4YWR5ID0gY2R4ICogYWR5O1xuICAgIGNvbnN0IGFkeGNkeSA9IGFkeCAqIGNkeTtcbiAgICBjb25zdCBibGlmdCA9IGJkeCAqIGJkeCArIGJkeSAqIGJkeTtcblxuICAgIGNvbnN0IGFkeGJkeSA9IGFkeCAqIGJkeTtcbiAgICBjb25zdCBiZHhhZHkgPSBiZHggKiBhZHk7XG4gICAgY29uc3QgY2xpZnQgPSBjZHggKiBjZHggKyBjZHkgKiBjZHk7XG5cbiAgICBjb25zdCBkZXQgPVxuICAgICAgICBhbGlmdCAqIChiZHhjZHkgLSBjZHhiZHkpICtcbiAgICAgICAgYmxpZnQgKiAoY2R4YWR5IC0gYWR4Y2R5KSArXG4gICAgICAgIGNsaWZ0ICogKGFkeGJkeSAtIGJkeGFkeSk7XG5cbiAgICBjb25zdCBwZXJtYW5lbnQgPVxuICAgICAgICAoTWF0aC5hYnMoYmR4Y2R5KSArIE1hdGguYWJzKGNkeGJkeSkpICogYWxpZnQgK1xuICAgICAgICAoTWF0aC5hYnMoY2R4YWR5KSArIE1hdGguYWJzKGFkeGNkeSkpICogYmxpZnQgK1xuICAgICAgICAoTWF0aC5hYnMoYWR4YmR5KSArIE1hdGguYWJzKGJkeGFkeSkpICogY2xpZnQ7XG5cbiAgICBjb25zdCBlcnJib3VuZCA9IGljY2VycmJvdW5kQSAqIHBlcm1hbmVudDtcblxuICAgIGlmIChkZXQgPiBlcnJib3VuZCB8fCAtZGV0ID4gZXJyYm91bmQpIHtcbiAgICAgICAgcmV0dXJuIGRldDtcbiAgICB9XG4gICAgcmV0dXJuIGluY2lyY2xlYWRhcHQoYXgsIGF5LCBieCwgYnksIGN4LCBjeSwgZHgsIGR5LCBwZXJtYW5lbnQpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaW5jaXJjbGVmYXN0KGF4LCBheSwgYngsIGJ5LCBjeCwgY3ksIGR4LCBkeSkge1xuICAgIGNvbnN0IGFkeCA9IGF4IC0gZHg7XG4gICAgY29uc3QgYWR5ID0gYXkgLSBkeTtcbiAgICBjb25zdCBiZHggPSBieCAtIGR4O1xuICAgIGNvbnN0IGJkeSA9IGJ5IC0gZHk7XG4gICAgY29uc3QgY2R4ID0gY3ggLSBkeDtcbiAgICBjb25zdCBjZHkgPSBjeSAtIGR5O1xuXG4gICAgY29uc3QgYWJkZXQgPSBhZHggKiBiZHkgLSBiZHggKiBhZHk7XG4gICAgY29uc3QgYmNkZXQgPSBiZHggKiBjZHkgLSBjZHggKiBiZHk7XG4gICAgY29uc3QgY2FkZXQgPSBjZHggKiBhZHkgLSBhZHggKiBjZHk7XG4gICAgY29uc3QgYWxpZnQgPSBhZHggKiBhZHggKyBhZHkgKiBhZHk7XG4gICAgY29uc3QgYmxpZnQgPSBiZHggKiBiZHggKyBiZHkgKiBiZHk7XG4gICAgY29uc3QgY2xpZnQgPSBjZHggKiBjZHggKyBjZHkgKiBjZHk7XG5cbiAgICByZXR1cm4gYWxpZnQgKiBiY2RldCArIGJsaWZ0ICogY2FkZXQgKyBjbGlmdCAqIGFiZGV0O1xufVxuIl0sIm5hbWVzIjpbImVwc2lsb24iLCJzcGxpdHRlciIsInJlc3VsdGVycmJvdW5kIiwiZXN0aW1hdGUiLCJ2ZWMiLCJzdW0iLCJzdW1fdGhyZWUiLCJzY2FsZSIsImljY2VycmJvdW5kQSIsImljY2VycmJvdW5kQiIsImljY2VycmJvdW5kQyIsImJjIiwiY2EiLCJhYiIsImFhIiwiYmIiLCJjYyIsInUiLCJ2IiwiYXh0YmMiLCJheXRiYyIsImJ4dGNhIiwiYnl0Y2EiLCJjeHRhYiIsImN5dGFiIiwiYWJ0IiwiYmN0IiwiY2F0IiwiYWJ0dCIsImJjdHQiLCJjYXR0IiwiXzgiLCJfMTYiLCJfMTZiIiwiXzE2YyIsIl8zMiIsIl8zMmIiLCJfNDgiLCJfNjQiLCJmaW4iLCJmaW4yIiwiZmluYWRkIiwiZmlubGVuIiwiYSIsImFsZW4iLCJ0bXAiLCJpbmNpcmNsZWFkYXB0IiwiYXgiLCJheSIsImJ4IiwiYnkiLCJjeCIsImN5IiwiZHgiLCJkeSIsInBlcm1hbmVudCIsImFkeHRhaWwiLCJiZHh0YWlsIiwiY2R4dGFpbCIsImFkeXRhaWwiLCJiZHl0YWlsIiwiY2R5dGFpbCIsImF4dGJjbGVuIiwiYXl0YmNsZW4iLCJieHRjYWxlbiIsImJ5dGNhbGVuIiwiY3h0YWJsZW4iLCJjeXRhYmxlbiIsImFidGxlbiIsImJjdGxlbiIsImNhdGxlbiIsImFidHRsZW4iLCJiY3R0bGVuIiwiY2F0dGxlbiIsIm4xIiwibjAiLCJidmlydCIsImMiLCJhaGkiLCJhbG8iLCJiaGkiLCJibG8iLCJfaSIsIl9qIiwiXzAiLCJzMSIsInMwIiwidDEiLCJ0MCIsInUzIiwiYWR4IiwiYmR4IiwiY2R4IiwiYWR5IiwiYmR5IiwiY2R5IiwiZGV0IiwiZXJyYm91bmQiLCJNYXRoIiwiYWJzIiwibGVuIiwibGVuMiIsImluY2lyY2xlIiwiYmR4Y2R5IiwiY2R4YmR5IiwiYWxpZnQiLCJjZHhhZHkiLCJhZHhjZHkiLCJibGlmdCIsImFkeGJkeSIsImJkeGFkeSIsImNsaWZ0IiwiaW5jaXJjbGVmYXN0IiwiYWJkZXQiLCJiY2RldCIsImNhZGV0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/incircle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/insphere.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/insphere.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   insphere: () => (/* binding */ insphere),\n/* harmony export */   inspherefast: () => (/* binding */ inspherefast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\nconst isperrboundA = (16 + 224 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst isperrboundB = (5 + 72 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst isperrboundC = (71 + 1408 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst cd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst de = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ea = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ac = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ce = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst da = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst eb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst abc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst bcd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst cde = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst dea = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst eab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst abd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst bce = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst cda = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst deb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst eac = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst adet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst bdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst cdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst ddet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst edet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst abdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(2304);\nconst cddet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(2304);\nconst cdedet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(3456);\nconst deter = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(5760);\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _24 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst _48 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _48b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _96 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst _192 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nconst _384x = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _384y = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _384z = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _768 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(768);\nfunction sum_three_scale(a, b, c, az, bz, cz, out) {\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, a, az, _8), _8, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, b, bz, _8b), _8b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, c, cz, _8c), _8c, _16, out);\n}\nfunction liftexact(alen, a, blen, b, clen, c, dlen, d, x, y, z, out) {\n    const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(alen, a, blen, b, _48), _48, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(clen, c, dlen, d, _48b), _48b), _48b, _96);\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, x, _192), _192, x, _384x), _384x, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, y, _192), _192, y, _384y), _384y, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, z, _192), _192, z, _384z), _384z, _768, out);\n}\nfunction insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n    s1 = ax * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    s1 = bx * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cx * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    cd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    cd[3] = u3;\n    s1 = dx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    de[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    de[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    de[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    de[3] = u3;\n    s1 = ex * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ea[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ea[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ea[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ea[3] = u3;\n    s1 = ax * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ac[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ac[3] = u3;\n    s1 = bx * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bd[3] = u3;\n    s1 = cx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ce[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ce[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ce[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ce[3] = u3;\n    s1 = dx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    da[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    da[3] = u3;\n    s1 = ex * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    eb[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    eb[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    eb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    eb[3] = u3;\n    const abclen = sum_three_scale(ab, bc, ac, cz, az, -bz, abc);\n    const bcdlen = sum_three_scale(bc, cd, bd, dz, bz, -cz, bcd);\n    const cdelen = sum_three_scale(cd, de, ce, ez, cz, -dz, cde);\n    const dealen = sum_three_scale(de, ea, da, az, dz, -ez, dea);\n    const eablen = sum_three_scale(ea, ab, eb, bz, ez, -az, eab);\n    const abdlen = sum_three_scale(ab, bd, da, dz, az, bz, abd);\n    const bcelen = sum_three_scale(bc, ce, eb, ez, bz, cz, bce);\n    const cdalen = sum_three_scale(cd, da, ac, az, cz, dz, cda);\n    const deblen = sum_three_scale(de, eb, bd, bz, dz, ez, deb);\n    const eaclen = sum_three_scale(ea, ac, ce, cz, ez, az, eac);\n    const deterlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(liftexact(cdelen, cde, bcelen, bce, deblen, deb, bcdlen, bcd, ax, ay, az, adet), adet, liftexact(dealen, dea, cdalen, cda, eaclen, eac, cdelen, cde, bx, by, bz, bdet), bdet, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(liftexact(eablen, eab, deblen, deb, abdlen, abd, dealen, dea, cx, cy, cz, cdet), cdet, liftexact(abclen, abc, eaclen, eac, bcelen, bce, eablen, eab, dx, dy, dz, ddet), ddet, liftexact(bcdlen, bcd, abdlen, abd, cdalen, cda, abclen, abc, ex, ey, ez, edet), edet, cddet, cdedet), cdedet, abdet, deter);\n    return deter[deterlen - 1];\n}\nconst xdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst ydet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst zdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nfunction liftadapt(a, b, c, az, bz, cz, x, y, z, out) {\n    const len = sum_three_scale(a, b, c, az, bz, cz, _24);\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, x, _48), _48, x, xdet), xdet, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, y, _48), _48, y, ydet), ydet, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, z, _48), _48, z, zdet), zdet, _192, out);\n}\nfunction insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent) {\n    let ab3, bc3, cd3, da3, ac3, bd3;\n    let aextail, bextail, cextail, dextail;\n    let aeytail, beytail, ceytail, deytail;\n    let aeztail, beztail, ceztail, deztail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0;\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n    s1 = aex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ab3 = _j + _i;\n    bvirt = ab3 - _j;\n    ab[2] = _j - (ab3 - bvirt) + (_i - bvirt);\n    ab[3] = ab3;\n    s1 = bex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bc3 = _j + _i;\n    bvirt = bc3 - _j;\n    bc[2] = _j - (bc3 - bvirt) + (_i - bvirt);\n    bc[3] = bc3;\n    s1 = cex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    cd3 = _j + _i;\n    bvirt = cd3 - _j;\n    cd[2] = _j - (cd3 - bvirt) + (_i - bvirt);\n    cd[3] = cd3;\n    s1 = dex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = aex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    da3 = _j + _i;\n    bvirt = da3 - _j;\n    da[2] = _j - (da3 - bvirt) + (_i - bvirt);\n    da[3] = da3;\n    s1 = aex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ac3 = _j + _i;\n    bvirt = ac3 - _j;\n    ac[2] = _j - (ac3 - bvirt) + (_i - bvirt);\n    ac[3] = ac3;\n    s1 = bex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bd3 = _j + _i;\n    bvirt = bd3 - _j;\n    bd[2] = _j - (bd3 - bvirt) + (_i - bvirt);\n    bd[3] = bd3;\n    const finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)(liftadapt(bc, cd, bd, dez, bez, -cez, aex, aey, aez, adet), adet), adet, liftadapt(cd, da, ac, aez, cez, dez, bex, bey, bez, bdet), bdet, abdet), abdet, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)(liftadapt(da, ab, bd, bez, dez, aez, cex, cey, cez, cdet), cdet), cdet, liftadapt(ab, bc, ac, cez, aez, -bez, dex, dey, dez, ddet), ddet, cddet), cddet, fin);\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = isperrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    bvirt = ax - aex;\n    aextail = ax - (aex + bvirt) + (bvirt - ex);\n    bvirt = ay - aey;\n    aeytail = ay - (aey + bvirt) + (bvirt - ey);\n    bvirt = az - aez;\n    aeztail = az - (aez + bvirt) + (bvirt - ez);\n    bvirt = bx - bex;\n    bextail = bx - (bex + bvirt) + (bvirt - ex);\n    bvirt = by - bey;\n    beytail = by - (bey + bvirt) + (bvirt - ey);\n    bvirt = bz - bez;\n    beztail = bz - (bez + bvirt) + (bvirt - ez);\n    bvirt = cx - cex;\n    cextail = cx - (cex + bvirt) + (bvirt - ex);\n    bvirt = cy - cey;\n    ceytail = cy - (cey + bvirt) + (bvirt - ey);\n    bvirt = cz - cez;\n    ceztail = cz - (cez + bvirt) + (bvirt - ez);\n    bvirt = dx - dex;\n    dextail = dx - (dex + bvirt) + (bvirt - ex);\n    bvirt = dy - dey;\n    deytail = dy - (dey + bvirt) + (bvirt - ey);\n    bvirt = dz - dez;\n    deztail = dz - (dez + bvirt) + (bvirt - ez);\n    if (aextail === 0 && aeytail === 0 && aeztail === 0 && bextail === 0 && beytail === 0 && beztail === 0 && cextail === 0 && ceytail === 0 && ceztail === 0 && dextail === 0 && deytail === 0 && deztail === 0) {\n        return det;\n    }\n    errbound = isperrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    const abeps = aex * beytail + bey * aextail - (aey * bextail + bex * aeytail);\n    const bceps = bex * ceytail + cey * bextail - (bey * cextail + cex * beytail);\n    const cdeps = cex * deytail + dey * cextail - (cey * dextail + dex * ceytail);\n    const daeps = dex * aeytail + aey * dextail - (dey * aextail + aex * deytail);\n    const aceps = aex * ceytail + cey * aextail - (aey * cextail + cex * aeytail);\n    const bdeps = bex * deytail + dey * bextail - (bey * dextail + dex * beytail);\n    det += (bex * bex + bey * bey + bez * bez) * (cez * daeps + dez * aceps + aez * cdeps + (ceztail * da3 + deztail * ac3 + aeztail * cd3)) + (dex * dex + dey * dey + dez * dez) * (aez * bceps - bez * aceps + cez * abeps + (aeztail * bc3 - beztail * ac3 + ceztail * ab3)) - ((aex * aex + aey * aey + aez * aez) * (bez * cdeps - cez * bdeps + dez * bceps + (beztail * cd3 - ceztail * bd3 + deztail * bc3)) + (cex * cex + cey * cey + cez * cez) * (dez * abeps + aez * bdeps + bez * daeps + (deztail * ab3 + aeztail * bd3 + beztail * da3))) + 2 * ((bex * bextail + bey * beytail + bez * beztail) * (cez * da3 + dez * ac3 + aez * cd3) + (dex * dextail + dey * deytail + dez * deztail) * (aez * bc3 - bez * ac3 + cez * ab3) - ((aex * aextail + aey * aeytail + aez * aeztail) * (bez * cd3 - cez * bd3 + dez * bc3) + (cex * cextail + cey * ceytail + cez * ceztail) * (dez * ab3 + aez * bd3 + bez * da3)));\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    return insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez);\n}\nfunction insphere(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n    const aexbey = aex * bey;\n    const bexaey = bex * aey;\n    const ab = aexbey - bexaey;\n    const bexcey = bex * cey;\n    const cexbey = cex * bey;\n    const bc = bexcey - cexbey;\n    const cexdey = cex * dey;\n    const dexcey = dex * cey;\n    const cd = cexdey - dexcey;\n    const dexaey = dex * aey;\n    const aexdey = aex * dey;\n    const da = dexaey - aexdey;\n    const aexcey = aex * cey;\n    const cexaey = cex * aey;\n    const ac = aexcey - cexaey;\n    const bexdey = bex * dey;\n    const dexbey = dex * bey;\n    const bd = bexdey - dexbey;\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n    const det = clift * (dez * ab + aez * bd + bez * da) - dlift * (aez * bc - bez * ac + cez * ab) + (alift * (bez * cd - cez * bd + dez * bc) - blift * (cez * da + dez * ac + aez * cd));\n    const aezplus = Math.abs(aez);\n    const bezplus = Math.abs(bez);\n    const cezplus = Math.abs(cez);\n    const dezplus = Math.abs(dez);\n    const aexbeyplus = Math.abs(aexbey) + Math.abs(bexaey);\n    const bexceyplus = Math.abs(bexcey) + Math.abs(cexbey);\n    const cexdeyplus = Math.abs(cexdey) + Math.abs(dexcey);\n    const dexaeyplus = Math.abs(dexaey) + Math.abs(aexdey);\n    const aexceyplus = Math.abs(aexcey) + Math.abs(cexaey);\n    const bexdeyplus = Math.abs(bexdey) + Math.abs(dexbey);\n    const permanent = (cexdeyplus * bezplus + bexdeyplus * cezplus + bexceyplus * dezplus) * alift + (dexaeyplus * cezplus + aexceyplus * dezplus + cexdeyplus * aezplus) * blift + (aexbeyplus * dezplus + bexdeyplus * aezplus + dexaeyplus * bezplus) * clift + (bexceyplus * aezplus + aexceyplus * bezplus + aexbeyplus * cezplus) * dlift;\n    const errbound = isperrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return -insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent);\n}\nfunction inspherefast(pax, pay, paz, pbx, pby, pbz, pcx, pcy, pcz, pdx, pdy, pdz, pex, pey, pez) {\n    const aex = pax - pex;\n    const bex = pbx - pex;\n    const cex = pcx - pex;\n    const dex = pdx - pex;\n    const aey = pay - pey;\n    const bey = pby - pey;\n    const cey = pcy - pey;\n    const dey = pdy - pey;\n    const aez = paz - pez;\n    const bez = pbz - pez;\n    const cez = pcz - pez;\n    const dez = pdz - pez;\n    const ab = aex * bey - bex * aey;\n    const bc = bex * cey - cex * bey;\n    const cd = cex * dey - dex * cey;\n    const da = dex * aey - aex * dey;\n    const ac = aex * cey - cex * aey;\n    const bd = bex * dey - dex * bey;\n    const abc = aez * bc - bez * ac + cez * ab;\n    const bcd = bez * cd - cez * bd + dez * bc;\n    const cda = cez * da + dez * ac + aez * cd;\n    const dab = dez * ab + aez * bd + bez * da;\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n    return clift * dab - dlift * abc + (alift * bcd - blift * cda);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/insphere.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/orient2d.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/orient2d.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orient2d: () => (/* binding */ orient2d),\n/* harmony export */   orient2dfast: () => (/* binding */ orient2dfast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\nconst ccwerrboundA = (3 + 16 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ccwerrboundB = (2 + 12 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ccwerrboundC = (9 + 64 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst B = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst C1 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst C2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(12);\nconst D = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nfunction orient2dadapt(ax, ay, bx, by, cx, cy, detsum) {\n    let acxtail, acytail, bcxtail, bcytail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n    const acx = ax - cx;\n    const bcx = bx - cx;\n    const acy = ay - cy;\n    const bcy = by - cy;\n    s1 = acx * bcy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcx;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    B[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    B[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    B[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    B[3] = u3;\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(4, B);\n    let errbound = ccwerrboundB * detsum;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    bvirt = ax - acx;\n    acxtail = ax - (acx + bvirt) + (bvirt - cx);\n    bvirt = bx - bcx;\n    bcxtail = bx - (bcx + bvirt) + (bvirt - cx);\n    bvirt = ay - acy;\n    acytail = ay - (acy + bvirt) + (bvirt - cy);\n    bvirt = by - bcy;\n    bcytail = by - (bcy + bvirt) + (bvirt - cy);\n    if (acxtail === 0 && acytail === 0 && bcxtail === 0 && bcytail === 0) {\n        return det;\n    }\n    errbound = ccwerrboundC * detsum + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += acx * bcytail + bcy * acxtail - (acy * bcxtail + bcx * acytail);\n    if (det >= errbound || -det >= errbound) return det;\n    s1 = acxtail * bcy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcx;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C1len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, B, 4, u, C1);\n    s1 = acx * bcytail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcxtail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C2len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(C1len, C1, 4, u, C2);\n    s1 = acxtail * bcytail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcxtail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const Dlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(C2len, C2, 4, u, D);\n    return D[Dlen - 1];\n}\nfunction orient2d(ax, ay, bx, by, cx, cy) {\n    const detleft = (ay - cy) * (bx - cx);\n    const detright = (ax - cx) * (by - cy);\n    const det = detleft - detright;\n    const detsum = Math.abs(detleft + detright);\n    if (Math.abs(det) >= ccwerrboundA * detsum) return det;\n    return -orient2dadapt(ax, ay, bx, by, cx, cy, detsum);\n}\nfunction orient2dfast(ax, ay, bx, by, cx, cy) {\n    return (ay - cy) * (bx - cx) - (ax - cx) * (by - cy);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/orient2d.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/orient3d.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/orient3d.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orient3d: () => (/* binding */ orient3d),\n/* harmony export */   orient3dfast: () => (/* binding */ orient3dfast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\nconst o3derrboundA = (7 + 56 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst o3derrboundB = (3 + 28 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst o3derrboundC = (26 + 288 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst at_b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst at_c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bt_c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bt_a = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ct_a = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ct_b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bct = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cat = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _12 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(12);\nlet fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nlet fin2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nfunction finadd(finlen, alen, a) {\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(finlen, fin, alen, a, fin2);\n    const tmp = fin;\n    fin = fin2;\n    fin2 = tmp;\n    return finlen;\n}\nfunction tailinit(xtail, ytail, ax, ay, bx, by, a, b) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3, negate;\n    if (xtail === 0) {\n        if (ytail === 0) {\n            a[0] = 0;\n            b[0] = 0;\n            return 1;\n        } else {\n            negate = -ytail;\n            s1 = negate * ax;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            s1 = ytail * bx;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        }\n    } else {\n        if (ytail === 0) {\n            s1 = xtail * ay;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            negate = -xtail;\n            s1 = negate * by;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        } else {\n            s1 = xtail * ay;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = ytail * ax;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            a[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            a[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            a[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            a[3] = u3;\n            s1 = ytail * bx;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = xtail * by;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            b[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            b[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            b[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            b[3] = u3;\n            return 4;\n        }\n    }\n}\nfunction tailadd(finlen, a, b, k, z) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, u3;\n    s1 = a * b;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * a;\n    ahi = c - (c - a);\n    alo = a - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * k;\n    bhi = c - (c - k);\n    blo = k - bhi;\n    _i = s0 * k;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s0;\n    ahi = c - (c - s0);\n    alo = s0 - ahi;\n    u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n    _j = s1 * k;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s1;\n    ahi = c - (c - s1);\n    alo = s1 - ahi;\n    _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n    _k = _i + _0;\n    bvirt = _k - _i;\n    u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n    u3 = _j + _k;\n    u[2] = _k - (u3 - _j);\n    u[3] = u3;\n    finlen = finadd(finlen, 4, u);\n    if (z !== 0) {\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * z;\n        bhi = c - (c - z);\n        blo = z - bhi;\n        _i = s0 * z;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s0;\n        ahi = c - (c - s0);\n        alo = s0 - ahi;\n        u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n        _j = s1 * z;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s1;\n        ahi = c - (c - s1);\n        alo = s1 - ahi;\n        _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n        _k = _i + _0;\n        bvirt = _k - _i;\n        u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n        u3 = _j + _k;\n        u[2] = _k - (u3 - _j);\n        u[3] = u3;\n        finlen = finadd(finlen, 4, u);\n    }\n    return finlen;\n}\nfunction orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail;\n    let adytail, bdytail, cdytail;\n    let adztail, bdztail, cdztail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3;\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n    s1 = bdx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adz, _8), _8, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdz, _8b), _8b, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdz, _8), _8, fin);\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = o3derrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    bvirt = az - adz;\n    adztail = az - (adz + bvirt) + (bvirt - dz);\n    bvirt = bz - bdz;\n    bdztail = bz - (bdz + bvirt) + (bvirt - dz);\n    bvirt = cz - cdz;\n    cdztail = cz - (cdz + bvirt) + (bvirt - dz);\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0 && adztail === 0 && bdztail === 0 && cdztail === 0) {\n        return det;\n    }\n    errbound = o3derrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += adz * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + adztail * (bdx * cdy - bdy * cdx) + bdz * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + bdztail * (cdx * ady - cdy * adx) + cdz * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + cdztail * (adx * bdy - ady * bdx);\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    const at_len = tailinit(adxtail, adytail, bdx, bdy, cdx, cdy, at_b, at_c);\n    const bt_len = tailinit(bdxtail, bdytail, cdx, cdy, adx, ady, bt_c, bt_a);\n    const ct_len = tailinit(cdxtail, cdytail, adx, ady, bdx, bdy, ct_a, ct_b);\n    const bctlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(bt_len, bt_c, ct_len, ct_b, bct);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adz, _16), _16);\n    const catlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(ct_len, ct_a, at_len, at_c, cat);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdz, _16), _16);\n    const abtlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(at_len, at_b, bt_len, bt_a, abt);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdz, _16), _16);\n    if (adztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adztail, _16), _16);\n    }\n    if (bdztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdztail, _16), _16);\n    }\n    if (cdztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdztail, _16), _16);\n    }\n    if (adxtail !== 0) {\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, adxtail, bdytail, cdz, cdztail);\n        }\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, -adxtail, cdytail, bdz, bdztail);\n        }\n    }\n    if (bdxtail !== 0) {\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, bdxtail, cdytail, adz, adztail);\n        }\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, -bdxtail, adytail, cdz, cdztail);\n        }\n    }\n    if (cdxtail !== 0) {\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, cdxtail, adytail, bdz, bdztail);\n        }\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, -cdxtail, bdytail, adz, adztail);\n        }\n    }\n    return fin[finlen - 1];\n}\nfunction orient3d(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n    const det = adz * (bdxcdy - cdxbdy) + bdz * (cdxady - adxcdy) + cdz * (adxbdy - bdxady);\n    const permanent = (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * Math.abs(adz) + (Math.abs(cdxady) + Math.abs(adxcdy)) * Math.abs(bdz) + (Math.abs(adxbdy) + Math.abs(bdxady)) * Math.abs(cdz);\n    const errbound = o3derrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent);\n}\nfunction orient3dfast(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n    return adx * (bdy * cdz - bdz * cdy) + bdx * (cdy * adz - cdz * ady) + cdx * (ady * bdz - adz * bdy);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/orient3d.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/util.js":
/*!****************************************************!*\
  !*** ./node_modules/robust-predicates/esm/util.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   epsilon: () => (/* binding */ epsilon),\n/* harmony export */   estimate: () => (/* binding */ estimate),\n/* harmony export */   negate: () => (/* binding */ negate),\n/* harmony export */   resulterrbound: () => (/* binding */ resulterrbound),\n/* harmony export */   scale: () => (/* binding */ scale),\n/* harmony export */   splitter: () => (/* binding */ splitter),\n/* harmony export */   sum: () => (/* binding */ sum),\n/* harmony export */   sum_three: () => (/* binding */ sum_three),\n/* harmony export */   vec: () => (/* binding */ vec)\n/* harmony export */ });\nconst epsilon = 1.1102230246251565e-16;\nconst splitter = 134217729;\nconst resulterrbound = (3 + 8 * epsilon) * epsilon;\n// fast_expansion_sum_zeroelim routine from oritinal code\nfunction sum(elen, e, flen, f, h) {\n    let Q, Qnew, hh, bvirt;\n    let enow = e[0];\n    let fnow = f[0];\n    let eindex = 0;\n    let findex = 0;\n    if (fnow > enow === fnow > -enow) {\n        Q = enow;\n        enow = e[++eindex];\n    } else {\n        Q = fnow;\n        fnow = f[++findex];\n    }\n    let hindex = 0;\n    if (eindex < elen && findex < flen) {\n        if (fnow > enow === fnow > -enow) {\n            Qnew = enow + Q;\n            hh = Q - (Qnew - enow);\n            enow = e[++eindex];\n        } else {\n            Qnew = fnow + Q;\n            hh = Q - (Qnew - fnow);\n            fnow = f[++findex];\n        }\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        while(eindex < elen && findex < flen){\n            if (fnow > enow === fnow > -enow) {\n                Qnew = Q + enow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (enow - bvirt);\n                enow = e[++eindex];\n            } else {\n                Qnew = Q + fnow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n                fnow = f[++findex];\n            }\n            Q = Qnew;\n            if (hh !== 0) {\n                h[hindex++] = hh;\n            }\n        }\n    }\n    while(eindex < elen){\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    while(findex < flen){\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\nfunction sum_three(alen, a, blen, b, clen, c, tmp, out) {\n    return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n// scale_expansion_zeroelim routine from oritinal code\nfunction scale(elen, e, b, h) {\n    let Q, sum, hh, product1, product0;\n    let bvirt, c, ahi, alo, bhi, blo;\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    let enow = e[0];\n    Q = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n    let hindex = 0;\n    if (hh !== 0) {\n        h[hindex++] = hh;\n    }\n    for(let i = 1; i < elen; i++){\n        enow = e[i];\n        product1 = enow * b;\n        c = splitter * enow;\n        ahi = c - (c - enow);\n        alo = enow - ahi;\n        product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n        sum = Q + product0;\n        bvirt = sum - Q;\n        hh = Q - (sum - bvirt) + (product0 - bvirt);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        Q = product1 + sum;\n        hh = sum - (Q - product1);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\nfunction negate(elen, e) {\n    for(let i = 0; i < elen; i++)e[i] = -e[i];\n    return elen;\n}\nfunction estimate(elen, e) {\n    let Q = e[0];\n    for(let i = 1; i < elen; i++)Q += e[i];\n    return Q;\n}\nfunction vec(n) {\n    return new Float64Array(n);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/index.js":
/*!*************************************************!*\
  !*** ./node_modules/robust-predicates/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   incircle: () => (/* reexport safe */ _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__.incircle),\n/* harmony export */   incirclefast: () => (/* reexport safe */ _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__.incirclefast),\n/* harmony export */   insphere: () => (/* reexport safe */ _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__.insphere),\n/* harmony export */   inspherefast: () => (/* reexport safe */ _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__.inspherefast),\n/* harmony export */   orient2d: () => (/* reexport safe */ _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__.orient2d),\n/* harmony export */   orient2dfast: () => (/* reexport safe */ _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__.orient2dfast),\n/* harmony export */   orient3d: () => (/* reexport safe */ _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__.orient3d),\n/* harmony export */   orient3dfast: () => (/* reexport safe */ _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__.orient3dfast)\n/* harmony export */ });\n/* harmony import */ var _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./esm/orient2d.js */ \"(ssr)/./node_modules/robust-predicates/esm/orient2d.js\");\n/* harmony import */ var _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./esm/orient3d.js */ \"(ssr)/./node_modules/robust-predicates/esm/orient3d.js\");\n/* harmony import */ var _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./esm/incircle.js */ \"(ssr)/./node_modules/robust-predicates/esm/incircle.js\");\n/* harmony import */ var _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./esm/insphere.js */ \"(ssr)/./node_modules/robust-predicates/esm/insphere.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcm9idXN0LXByZWRpY2F0ZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQ3lEO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvcm9idXN0LXByZWRpY2F0ZXMvaW5kZXguanM/ZGUxYiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7b3JpZW50MmQsIG9yaWVudDJkZmFzdH0gZnJvbSAnLi9lc20vb3JpZW50MmQuanMnO1xuZXhwb3J0IHtvcmllbnQzZCwgb3JpZW50M2RmYXN0fSBmcm9tICcuL2VzbS9vcmllbnQzZC5qcyc7XG5leHBvcnQge2luY2lyY2xlLCBpbmNpcmNsZWZhc3R9IGZyb20gJy4vZXNtL2luY2lyY2xlLmpzJztcbmV4cG9ydCB7aW5zcGhlcmUsIGluc3BoZXJlZmFzdH0gZnJvbSAnLi9lc20vaW5zcGhlcmUuanMnO1xuIl0sIm5hbWVzIjpbIm9yaWVudDJkIiwib3JpZW50MmRmYXN0Iiwib3JpZW50M2QiLCJvcmllbnQzZGZhc3QiLCJpbmNpcmNsZSIsImluY2lyY2xlZmFzdCIsImluc3BoZXJlIiwiaW5zcGhlcmVmYXN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/index.js\n");

/***/ })

};
;