"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-gauge-component";
exports.ids = ["vendor-chunks/react-gauge-component"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/constants.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/constants.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.CONSTANTS = void 0;\nexports.CONSTANTS = {\n    arcTooltipClassname: \"gauge-component-arc-tooltip\",\n    tickLineClassname: \"tick-line\",\n    tickValueClassname: \"tick-value\",\n    valueLabelClassname: \"value-text\",\n    debugTicksRadius: false,\n    debugSingleGauge: false,\n    rangeBetweenCenteredTickValueLabel: [\n        0.35,\n        0.65\n    ]\n};\nexports[\"default\"] = exports.CONSTANTS;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ2F1Z2UtY29tcG9uZW50L2Rpc3QvbGliL0dhdWdlQ29tcG9uZW50L2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUM3REQsaUJBQWlCLEdBQUcsS0FBSztBQUN6QkEsaUJBQWlCLEdBQUc7SUFDaEJHLHFCQUFxQjtJQUNyQkMsbUJBQW1CO0lBQ25CQyxvQkFBb0I7SUFDcEJDLHFCQUFxQjtJQUNyQkMsa0JBQWtCO0lBQ2xCQyxrQkFBa0I7SUFDbEJDLG9DQUFvQztRQUFDO1FBQU07S0FBSztBQUNwRDtBQUNBVCxrQkFBZSxHQUFHQSxRQUFRRSxTQUFTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ2F1Z2UtY29tcG9uZW50L2Rpc3QvbGliL0dhdWdlQ29tcG9uZW50L2NvbnN0YW50cy5qcz81NWU0Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5DT05TVEFOVFMgPSB2b2lkIDA7XG5leHBvcnRzLkNPTlNUQU5UUyA9IHtcbiAgICBhcmNUb29sdGlwQ2xhc3NuYW1lOiBcImdhdWdlLWNvbXBvbmVudC1hcmMtdG9vbHRpcFwiLFxuICAgIHRpY2tMaW5lQ2xhc3NuYW1lOiBcInRpY2stbGluZVwiLFxuICAgIHRpY2tWYWx1ZUNsYXNzbmFtZTogXCJ0aWNrLXZhbHVlXCIsXG4gICAgdmFsdWVMYWJlbENsYXNzbmFtZTogXCJ2YWx1ZS10ZXh0XCIsXG4gICAgZGVidWdUaWNrc1JhZGl1czogZmFsc2UsXG4gICAgZGVidWdTaW5nbGVHYXVnZTogZmFsc2UsXG4gICAgcmFuZ2VCZXR3ZWVuQ2VudGVyZWRUaWNrVmFsdWVMYWJlbDogWzAuMzUsIDAuNjVdXG59O1xuZXhwb3J0cy5kZWZhdWx0ID0gZXhwb3J0cy5DT05TVEFOVFM7XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJDT05TVEFOVFMiLCJhcmNUb29sdGlwQ2xhc3NuYW1lIiwidGlja0xpbmVDbGFzc25hbWUiLCJ0aWNrVmFsdWVDbGFzc25hbWUiLCJ2YWx1ZUxhYmVsQ2xhc3NuYW1lIiwiZGVidWdUaWNrc1JhZGl1cyIsImRlYnVnU2luZ2xlR2F1Z2UiLCJyYW5nZUJldHdlZW5DZW50ZXJlZFRpY2tWYWx1ZUxhYmVsIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.validateArcs = exports.clearOuterArcs = exports.clearArcs = exports.redrawArcs = exports.getCoordByValue = exports.createGradientElement = exports.getColors = exports.applyGradientColors = exports.getArcDataByPercentage = exports.getArcDataByValue = exports.applyColors = exports.setupTooltip = exports.setupArcs = exports.drawArc = exports.setArcData = exports.hideTooltip = void 0;\nvar utils = __importStar(__webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js\"));\nvar d3_1 = __webpack_require__(/*! d3 */ \"(ssr)/./node_modules/d3/src/index.js\");\nvar arcHooks = __importStar(__webpack_require__(/*! ./arc */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\"));\nvar constants_1 = __importDefault(__webpack_require__(/*! ../constants */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/constants.js\"));\nvar Tooltip_1 = __webpack_require__(/*! ../types/Tooltip */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tooltip.js\");\nvar GaugeComponentProps_1 = __webpack_require__(/*! ../types/GaugeComponentProps */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nvar lodash_1 = __webpack_require__(/*! lodash */ \"(ssr)/./node_modules/lodash/lodash.js\");\nvar onArcMouseMove = function(event, d, gauge) {\n    //event.target.style.stroke = \"#ffffff5e\";\n    if (d.data.tooltip != undefined) {\n        var shouldChangeText = d.data.tooltip.text != gauge.tooltip.current.text();\n        if (shouldChangeText) {\n            gauge.tooltip.current.html(d.data.tooltip.text).style(\"position\", \"absolute\").style(\"display\", \"block\").style(\"opacity\", 1);\n            applyTooltipStyles(d.data.tooltip, d.data.color, gauge);\n        }\n        gauge.tooltip.current.style(\"left\", event.pageX + 15 + \"px\").style(\"top\", event.pageY - 10 + \"px\");\n    }\n    if (d.data.onMouseMove != undefined) d.data.onMouseMove(event);\n};\nvar applyTooltipStyles = function(tooltip, arcColor, gauge) {\n    //Apply default styles\n    Object.entries(Tooltip_1.defaultTooltipStyle).forEach(function(_a) {\n        var key = _a[0], value = _a[1];\n        return gauge.tooltip.current.style(utils.camelCaseToKebabCase(key), value);\n    });\n    gauge.tooltip.current.style(\"background-color\", arcColor);\n    //Apply custom styles\n    if (tooltip.style != undefined) Object.entries(tooltip.style).forEach(function(_a) {\n        var key = _a[0], value = _a[1];\n        return gauge.tooltip.current.style(utils.camelCaseToKebabCase(key), value);\n    });\n};\nvar onArcMouseLeave = function(event, d, gauge, mousemoveCbThrottled) {\n    mousemoveCbThrottled.cancel();\n    (0, exports.hideTooltip)(gauge);\n    if (d.data.onMouseLeave != undefined) d.data.onMouseLeave(event);\n};\nvar hideTooltip = function(gauge) {\n    gauge.tooltip.current.html(\" \").style(\"display\", \"none\");\n};\nexports.hideTooltip = hideTooltip;\nvar onArcMouseOut = function(event, d, gauge) {\n    event.target.style.stroke = \"none\";\n};\nvar onArcMouseClick = function(event, d) {\n    if (d.data.onMouseClick != undefined) d.data.onMouseClick(event);\n};\nvar setArcData = function(gauge) {\n    var _a, _b;\n    var arc = gauge.props.arc;\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    // Determine number of arcs to display\n    var nbArcsToDisplay = (arc === null || arc === void 0 ? void 0 : arc.nbSubArcs) || ((_a = arc === null || arc === void 0 ? void 0 : arc.subArcs) === null || _a === void 0 ? void 0 : _a.length) || 1;\n    var colorArray = (0, exports.getColors)(nbArcsToDisplay, gauge);\n    if ((arc === null || arc === void 0 ? void 0 : arc.subArcs) && !(arc === null || arc === void 0 ? void 0 : arc.nbSubArcs)) {\n        var lastSubArcLimit_1 = 0;\n        var lastSubArcLimitPercentageAcc_1 = 0;\n        var subArcsLength_1 = [];\n        var subArcsLimits_1 = [];\n        var subArcsTooltip_1 = [];\n        (_b = arc === null || arc === void 0 ? void 0 : arc.subArcs) === null || _b === void 0 ? void 0 : _b.forEach(function(subArc, i) {\n            var _a;\n            var subArcLength = 0;\n            //map limit for non defined subArcs limits\n            var subArcRange = 0;\n            var limit = subArc.limit;\n            if (subArc.length != undefined) {\n                subArcLength = subArc.length;\n                limit = utils.getCurrentGaugeValueByPercentage(subArcLength + lastSubArcLimitPercentageAcc_1, gauge);\n            } else if (subArc.limit == undefined) {\n                subArcRange = lastSubArcLimit_1;\n                var remainingPercentageEquallyDivided = undefined;\n                var remainingSubArcs = (_a = arc === null || arc === void 0 ? void 0 : arc.subArcs) === null || _a === void 0 ? void 0 : _a.slice(i);\n                var remainingPercentage = (1 - utils.calculatePercentage(minValue, maxValue, lastSubArcLimit_1)) * 100;\n                if (!remainingPercentageEquallyDivided) {\n                    remainingPercentageEquallyDivided = remainingPercentage / Math.max((remainingSubArcs === null || remainingSubArcs === void 0 ? void 0 : remainingSubArcs.length) || 1, 1) / 100;\n                }\n                limit = lastSubArcLimit_1 + remainingPercentageEquallyDivided * 100;\n                subArcLength = remainingPercentageEquallyDivided;\n            } else {\n                subArcRange = limit - lastSubArcLimit_1;\n                // Calculate arc length based on previous arc percentage\n                if (i !== 0) {\n                    subArcLength = utils.calculatePercentage(minValue, maxValue, limit) - lastSubArcLimitPercentageAcc_1;\n                } else {\n                    subArcLength = utils.calculatePercentage(minValue, maxValue, subArcRange);\n                }\n            }\n            subArcsLength_1.push(subArcLength);\n            subArcsLimits_1.push(limit);\n            lastSubArcLimitPercentageAcc_1 = subArcsLength_1.reduce(function(count, curr) {\n                return count + curr;\n            }, 0);\n            lastSubArcLimit_1 = limit;\n            if (subArc.tooltip != undefined) subArcsTooltip_1.push(subArc.tooltip);\n        });\n        var subArcs_1 = arc.subArcs;\n        gauge.arcData.current = subArcsLength_1.map(function(length, i) {\n            return {\n                value: length,\n                limit: subArcsLimits_1[i],\n                color: colorArray[i],\n                showTick: subArcs_1[i].showTick || false,\n                tooltip: subArcs_1[i].tooltip || undefined,\n                onMouseMove: subArcs_1[i].onMouseMove,\n                onMouseLeave: subArcs_1[i].onMouseLeave,\n                onMouseClick: subArcs_1[i].onClick\n            };\n        });\n    } else {\n        var arcValue_1 = maxValue / nbArcsToDisplay;\n        gauge.arcData.current = Array.from({\n            length: nbArcsToDisplay\n        }, function(_, i) {\n            return {\n                value: arcValue_1,\n                limit: (i + 1) * arcValue_1,\n                color: colorArray[i],\n                tooltip: undefined\n            };\n        });\n    }\n};\nexports.setArcData = setArcData;\nvar getGrafanaMainArcData = function(gauge, percent) {\n    var _a;\n    if (percent === void 0) {\n        percent = undefined;\n    }\n    var currentPercentage = percent != undefined ? percent : utils.calculatePercentage(gauge.props.minValue, gauge.props.maxValue, gauge.props.value);\n    var curArcData = (0, exports.getArcDataByPercentage)(currentPercentage, gauge);\n    var firstSubArc = {\n        value: currentPercentage,\n        //White indicate that no arc was found and work as an alert for debug\n        color: (curArcData === null || curArcData === void 0 ? void 0 : curArcData.color) || \"white\"\n    };\n    //This is the grey arc that will be displayed when the gauge is not full\n    var secondSubArc = {\n        value: 1 - currentPercentage,\n        color: (_a = gauge.props.arc) === null || _a === void 0 ? void 0 : _a.emptyColor\n    };\n    return [\n        firstSubArc,\n        secondSubArc\n    ];\n};\nvar drawGrafanaOuterArc = function(gauge, resize) {\n    if (resize === void 0) {\n        resize = false;\n    }\n    var outerRadius = gauge.dimensions.current.outerRadius;\n    //Grafana's outer arc will be populates as the standard arc data would\n    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana && resize) {\n        gauge.doughnut.current.selectAll(\".outerSubArc\").remove();\n        var outerArc = (0, d3_1.arc)().outerRadius(outerRadius + 7).innerRadius(outerRadius + 2).cornerRadius(0).padAngle(0);\n        var arcPaths = gauge.doughnut.current.selectAll(\"anyString\").data(gauge.pieChart.current(gauge.arcData.current)).enter().append(\"g\").attr(\"class\", \"outerSubArc\");\n        var outerArcSubarcs = arcPaths.append(\"path\").attr(\"d\", outerArc);\n        (0, exports.applyColors)(outerArcSubarcs, gauge);\n        var mousemoveCbThrottled_1 = (0, lodash_1.throttle)(function(event, d) {\n            return onArcMouseMove(event, d, gauge);\n        }, 20);\n        arcPaths.on(\"mouseleave\", function(event, d) {\n            return onArcMouseLeave(event, d, gauge, mousemoveCbThrottled_1);\n        }).on(\"mouseout\", function(event, d) {\n            return onArcMouseOut(event, d, gauge);\n        }).on(\"mousemove\", mousemoveCbThrottled_1).on(\"click\", function(event, d) {\n            return onArcMouseClick(event, d);\n        });\n    }\n};\nvar drawArc = function(gauge, percent) {\n    var _a, _b;\n    if (percent === void 0) {\n        percent = undefined;\n    }\n    var _c = gauge.props.arc, padding = _c.padding, cornerRadius = _c.cornerRadius;\n    var _d = gauge.dimensions.current, innerRadius = _d.innerRadius, outerRadius = _d.outerRadius;\n    // chartHooks.clearChart(gauge);\n    var data = {};\n    //When gradient enabled, it'll have only 1 arc\n    if ((_b = (_a = gauge.props) === null || _a === void 0 ? void 0 : _a.arc) === null || _b === void 0 ? void 0 : _b.gradient) {\n        data = [\n            {\n                value: 1\n            }\n        ];\n    } else {\n        data = gauge.arcData.current;\n    }\n    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana) {\n        data = getGrafanaMainArcData(gauge, percent);\n    }\n    var arcPadding = gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana ? 0 : padding;\n    var arcCornerRadius = gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana ? 0 : cornerRadius;\n    var arcObj = (0, d3_1.arc)().outerRadius(outerRadius).innerRadius(innerRadius).cornerRadius(arcCornerRadius).padAngle(arcPadding);\n    var arcPaths = gauge.doughnut.current.selectAll(\"anyString\").data(gauge.pieChart.current(data)).enter().append(\"g\").attr(\"class\", \"subArc\");\n    var subArcs = arcPaths.append(\"path\").attr(\"d\", arcObj);\n    (0, exports.applyColors)(subArcs, gauge);\n    var mousemoveCbThrottled = (0, lodash_1.throttle)(function(event, d) {\n        return onArcMouseMove(event, d, gauge);\n    }, 20);\n    arcPaths.on(\"mouseleave\", function(event, d) {\n        return onArcMouseLeave(event, d, gauge, mousemoveCbThrottled);\n    }).on(\"mouseout\", function(event, d) {\n        return onArcMouseOut(event, d, gauge);\n    }).on(\"mousemove\", mousemoveCbThrottled).on(\"click\", function(event, d) {\n        return onArcMouseClick(event, d);\n    });\n};\nexports.drawArc = drawArc;\nvar setupArcs = function(gauge, resize) {\n    if (resize === void 0) {\n        resize = false;\n    }\n    //Setup the arc\n    (0, exports.setupTooltip)(gauge);\n    drawGrafanaOuterArc(gauge, resize);\n    (0, exports.drawArc)(gauge);\n};\nexports.setupArcs = setupArcs;\nvar setupTooltip = function(gauge) {\n    //Add tooltip\n    var isTooltipInTheDom = document.getElementsByClassName(constants_1.default.arcTooltipClassname).length != 0;\n    if (!isTooltipInTheDom) (0, d3_1.select)(\"body\").append(\"div\").attr(\"class\", constants_1.default.arcTooltipClassname);\n    gauge.tooltip.current = (0, d3_1.select)(\".\".concat(constants_1.default.arcTooltipClassname));\n    gauge.tooltip.current.on(\"mouseleave\", function() {\n        return arcHooks.hideTooltip(gauge);\n    }).on(\"mouseout\", function() {\n        return arcHooks.hideTooltip(gauge);\n    });\n};\nexports.setupTooltip = setupTooltip;\nvar applyColors = function(subArcsPath, gauge) {\n    var _a, _b;\n    if ((_b = (_a = gauge.props) === null || _a === void 0 ? void 0 : _a.arc) === null || _b === void 0 ? void 0 : _b.gradient) {\n        var uniqueId_1 = \"subArc-linear-gradient-\".concat(Math.random());\n        var gradEl = (0, exports.createGradientElement)(gauge.doughnut.current, uniqueId_1);\n        (0, exports.applyGradientColors)(gradEl, gauge);\n        subArcsPath.style(\"fill\", function(d) {\n            return \"url(#\".concat(uniqueId_1, \")\");\n        });\n    } else {\n        subArcsPath.style(\"fill\", function(d) {\n            return d.data.color;\n        });\n    }\n};\nexports.applyColors = applyColors;\nvar getArcDataByValue = function(value, gauge) {\n    return gauge.arcData.current.find(function(subArcData) {\n        return value <= subArcData.limit;\n    });\n};\nexports.getArcDataByValue = getArcDataByValue;\nvar getArcDataByPercentage = function(percentage, gauge) {\n    return (0, exports.getArcDataByValue)(utils.getCurrentGaugeValueByPercentage(percentage, gauge), gauge);\n};\nexports.getArcDataByPercentage = getArcDataByPercentage;\nvar applyGradientColors = function(gradEl, gauge) {\n    gauge.arcData.current.forEach(function(subArcData) {\n        var _a, _b, _c, _d;\n        var normalizedOffset = utils.normalize(subArcData === null || subArcData === void 0 ? void 0 : subArcData.limit, (_b = (_a = gauge === null || gauge === void 0 ? void 0 : gauge.props) === null || _a === void 0 ? void 0 : _a.minValue) !== null && _b !== void 0 ? _b : 0, (_d = (_c = gauge === null || gauge === void 0 ? void 0 : gauge.props) === null || _c === void 0 ? void 0 : _c.maxValue) !== null && _d !== void 0 ? _d : 100);\n        gradEl.append(\"stop\").attr(\"offset\", \"\".concat(normalizedOffset, \"%\")).style(\"stop-color\", subArcData.color) //end in red\n        .style(\"stop-opacity\", 1);\n    });\n};\nexports.applyGradientColors = applyGradientColors;\n//Depending on the number of levels in the chart\n//This function returns the same number of colors\nvar getColors = function(nbArcsToDisplay, gauge) {\n    var _a;\n    var arc = gauge.props.arc;\n    var colorsValue = [];\n    if (!arc.colorArray) {\n        var subArcColors = (_a = arc.subArcs) === null || _a === void 0 ? void 0 : _a.map(function(subArc) {\n            return subArc.color;\n        });\n        colorsValue = (subArcColors === null || subArcColors === void 0 ? void 0 : subArcColors.some(function(color) {\n            return color != undefined;\n        })) ? subArcColors : constants_1.default.defaultColors;\n    } else {\n        colorsValue = arc.colorArray;\n    }\n    //defaults colorsValue to white in order to avoid compilation error\n    if (!colorsValue) colorsValue = [\n        \"#fff\"\n    ];\n    //Check if the number of colors equals the number of levels\n    //Otherwise make an interpolation\n    var arcsEqualsColorsLength = nbArcsToDisplay === (colorsValue === null || colorsValue === void 0 ? void 0 : colorsValue.length);\n    if (arcsEqualsColorsLength) return colorsValue;\n    var colorScale = (0, d3_1.scaleLinear)().domain([\n        1,\n        nbArcsToDisplay\n    ])//@ts-ignore\n    .range([\n        colorsValue[0],\n        colorsValue[colorsValue.length - 1]\n    ]) //Use the first and the last color as range\n    //@ts-ignore\n    .interpolate(d3_1.interpolateHsl);\n    var colorArray = [];\n    for(var i = 1; i <= nbArcsToDisplay; i++){\n        colorArray.push(colorScale(i));\n    }\n    return colorArray;\n};\nexports.getColors = getColors;\nvar createGradientElement = function(div, uniqueId) {\n    //make defs and add the linear gradient\n    var lg = div.append(\"defs\").append(\"linearGradient\").attr(\"id\", uniqueId) //id of the gradient\n    .attr(\"x1\", \"0%\").attr(\"x2\", \"100%\").attr(\"y1\", \"0%\").attr(\"y2\", \"0%\");\n    return lg;\n};\nexports.createGradientElement = createGradientElement;\nvar getCoordByValue = function(value, gauge, position, centerToArcLengthSubtract, radiusFactor) {\n    var _a;\n    if (position === void 0) {\n        position = \"inner\";\n    }\n    if (centerToArcLengthSubtract === void 0) {\n        centerToArcLengthSubtract = 0;\n    }\n    if (radiusFactor === void 0) {\n        radiusFactor = 1;\n    }\n    var positionCenterToArcLength = {\n        \"outer\": function() {\n            return gauge.dimensions.current.outerRadius - centerToArcLengthSubtract + 2;\n        },\n        \"inner\": function() {\n            return gauge.dimensions.current.innerRadius * radiusFactor - centerToArcLengthSubtract + 9;\n        },\n        \"between\": function() {\n            var lengthBetweenOuterAndInner = gauge.dimensions.current.outerRadius - gauge.dimensions.current.innerRadius;\n            var middlePosition = gauge.dimensions.current.innerRadius + lengthBetweenOuterAndInner - 5;\n            return middlePosition;\n        }\n    };\n    var centerToArcLength = positionCenterToArcLength[position]();\n    // This normalizes the labels when distanceFromArc = 0 to be just touching the arcs \n    if (gauge.props.type === GaugeComponentProps_1.GaugeType.Grafana) {\n        centerToArcLength += 5;\n    } else if (gauge.props.type === GaugeComponentProps_1.GaugeType.Semicircle) {\n        centerToArcLength += -2;\n    }\n    var percent = utils.calculatePercentage(gauge.props.minValue, gauge.props.maxValue, value);\n    var gaugeTypesAngles = (_a = {}, _a[GaugeComponentProps_1.GaugeType.Grafana] = {\n        startAngle: utils.degToRad(-23),\n        endAngle: utils.degToRad(203)\n    }, _a[GaugeComponentProps_1.GaugeType.Semicircle] = {\n        startAngle: utils.degToRad(0.9),\n        endAngle: utils.degToRad(179.1)\n    }, _a[GaugeComponentProps_1.GaugeType.Radial] = {\n        startAngle: utils.degToRad(-39),\n        endAngle: utils.degToRad(219)\n    }, _a);\n    var _b = gaugeTypesAngles[gauge.props.type], startAngle = _b.startAngle, endAngle = _b.endAngle;\n    var angle = startAngle + percent * (endAngle - startAngle);\n    var coordsRadius = 1 * (gauge.dimensions.current.width / 500);\n    var coord = [\n        0,\n        -coordsRadius / 2\n    ];\n    var coordMinusCenter = [\n        coord[0] - centerToArcLength * Math.cos(angle),\n        coord[1] - centerToArcLength * Math.sin(angle)\n    ];\n    var centerCoords = [\n        gauge.dimensions.current.outerRadius,\n        gauge.dimensions.current.outerRadius\n    ];\n    var x = centerCoords[0] + coordMinusCenter[0];\n    var y = centerCoords[1] + coordMinusCenter[1];\n    return {\n        x: x,\n        y: y\n    };\n};\nexports.getCoordByValue = getCoordByValue;\nvar redrawArcs = function(gauge) {\n    (0, exports.clearArcs)(gauge);\n    (0, exports.setArcData)(gauge);\n    (0, exports.setupArcs)(gauge);\n};\nexports.redrawArcs = redrawArcs;\nvar clearArcs = function(gauge) {\n    gauge.doughnut.current.selectAll(\".subArc\").remove();\n};\nexports.clearArcs = clearArcs;\nvar clearOuterArcs = function(gauge) {\n    gauge.doughnut.current.selectAll(\".outerSubArc\").remove();\n};\nexports.clearOuterArcs = clearOuterArcs;\nvar validateArcs = function(gauge) {\n    verifySubArcsLimits(gauge);\n};\nexports.validateArcs = validateArcs;\n/**\n * Reorders the subArcs within the gauge's arc property based on the limit property.\n * SubArcs with undefined limits are sorted last.\n*/ var reOrderSubArcs = function(gauge) {\n    var _a;\n    var subArcs = (_a = gauge.props.arc) === null || _a === void 0 ? void 0 : _a.subArcs;\n    subArcs.sort(function(a, b) {\n        if (typeof a.limit === \"undefined\" && typeof b.limit === \"undefined\") {\n            return 0;\n        }\n        if (typeof a.limit === \"undefined\") {\n            return 1;\n        }\n        if (typeof b.limit === \"undefined\") {\n            return -1;\n        }\n        return a.limit - b.limit;\n    });\n};\nvar verifySubArcsLimits = function(gauge) {\n    var _a;\n    // disabled when length implemented.\n    // reOrderSubArcs(gauge);\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    var arc = gauge.props.arc;\n    var subArcs = arc.subArcs;\n    var prevLimit = undefined;\n    for(var _i = 0, _b = ((_a = gauge.props.arc) === null || _a === void 0 ? void 0 : _a.subArcs) || []; _i < _b.length; _i++){\n        var subArc = _b[_i];\n        var limit = subArc.limit;\n        if (typeof limit !== \"undefined\") {\n            // Check if the limit is within the valid range\n            if (limit < minValue || limit > maxValue) throw new Error(\"The limit of a subArc must be between the minValue and maxValue. The limit of the subArc is \".concat(limit));\n            // Check if the limit is greater than the previous limit\n            if (typeof prevLimit !== \"undefined\") {\n                if (limit <= prevLimit) throw new Error(\"The limit of a subArc must be greater than the limit of the previous subArc. The limit of the subArc is \".concat(limit, '. If you\\'re trying to specify length in percent of the arc, use property \"length\". refer to: https://github.com/antoniolago/react-gauge-component'));\n            }\n            prevLimit = limit;\n        }\n    }\n    // If the user has defined subArcs, make sure the last subArc has a limit equal to the maxValue\n    if (subArcs.length > 0) {\n        var lastSubArc = subArcs[subArcs.length - 1];\n        if (lastSubArc.limit < maxValue) lastSubArc.limit = maxValue;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/chart.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/chart.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.clearChart = exports.centerGraph = exports.calculateRadius = exports.updateDimensions = exports.renderChart = exports.calculateAngles = exports.initChart = void 0;\nvar GaugeComponentProps_1 = __webpack_require__(/*! ../types/GaugeComponentProps */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nvar arcHooks = __importStar(__webpack_require__(/*! ./arc */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\"));\nvar labelsHooks = __importStar(__webpack_require__(/*! ./labels */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/labels.js\"));\nvar pointerHooks = __importStar(__webpack_require__(/*! ./pointer */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/pointer.js\"));\nvar initChart = function(gauge, isFirstRender) {\n    var _a, _b, _c, _d;\n    var angles = gauge.dimensions.current.angles;\n    if ((_b = (_a = gauge.resizeObserver) === null || _a === void 0 ? void 0 : _a.current) === null || _b === void 0 ? void 0 : _b.disconnect) {\n        (_d = (_c = gauge.resizeObserver) === null || _c === void 0 ? void 0 : _c.current) === null || _d === void 0 ? void 0 : _d.disconnect();\n    }\n    var updatedValue = JSON.stringify(gauge.prevProps.current.value) !== JSON.stringify(gauge.props.value);\n    if (updatedValue && !isFirstRender) {\n        (0, exports.renderChart)(gauge, false);\n        return;\n    }\n    gauge.container.current.select(\"svg\").remove();\n    gauge.svg.current = gauge.container.current.append(\"svg\");\n    gauge.g.current = gauge.svg.current.append(\"g\"); //Used for margins\n    gauge.doughnut.current = gauge.g.current.append(\"g\").attr(\"class\", \"doughnut\");\n    //gauge.outerDougnut.current = gauge.g.current.append(\"g\").attr(\"class\", \"doughnut\");\n    (0, exports.calculateAngles)(gauge);\n    gauge.pieChart.current.value(function(d) {\n        return d.value;\n    })//.padAngle(15)\n    .startAngle(angles.startAngle).endAngle(angles.endAngle).sort(null);\n    //Set up pointer\n    pointerHooks.addPointerElement(gauge);\n    (0, exports.renderChart)(gauge, true);\n};\nexports.initChart = initChart;\nvar calculateAngles = function(gauge) {\n    var angles = gauge.dimensions.current.angles;\n    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Semicircle) {\n        angles.startAngle = -Math.PI / 2 + 0.02;\n        angles.endAngle = Math.PI / 2 - 0.02;\n    } else if (gauge.props.type == GaugeComponentProps_1.GaugeType.Radial) {\n        angles.startAngle = -Math.PI / 1.37;\n        angles.endAngle = Math.PI / 1.37;\n    } else if (gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana) {\n        angles.startAngle = -Math.PI / 1.6;\n        angles.endAngle = Math.PI / 1.6;\n    }\n};\nexports.calculateAngles = calculateAngles;\n//Renders the chart, should be called every time the window is resized\nvar renderChart = function(gauge, resize) {\n    var _a;\n    var _b, _c, _d, _e, _f;\n    if (resize === void 0) {\n        resize = false;\n    }\n    var dimensions = gauge.dimensions;\n    var arc = gauge.props.arc;\n    var labels = gauge.props.labels;\n    //if resize recalculate dimensions, clear chart and redraw\n    //if not resize, treat each prop separately\n    if (resize) {\n        (0, exports.updateDimensions)(gauge);\n        //Set dimensions of svg element and translations\n        gauge.g.current.attr(\"transform\", \"translate(\" + dimensions.current.margin.left + \", \" + 35 + \")\");\n        //Set the radius to lesser of width or height and remove the margins\n        //Calculate the new radius\n        (0, exports.calculateRadius)(gauge);\n        gauge.doughnut.current.attr(\"transform\", \"translate(\" + dimensions.current.outerRadius + \", \" + dimensions.current.outerRadius + \")\");\n        //Hide tooltip failsafe (sometimes subarcs events are not fired)\n        gauge.doughnut.current.on(\"mouseleave\", function() {\n            return arcHooks.hideTooltip(gauge);\n        }).on(\"mouseout\", function() {\n            return arcHooks.hideTooltip(gauge);\n        });\n        var arcWidth = arc.width;\n        dimensions.current.innerRadius = dimensions.current.outerRadius * (1 - arcWidth);\n        (0, exports.clearChart)(gauge);\n        arcHooks.setArcData(gauge);\n        arcHooks.setupArcs(gauge, resize);\n        labelsHooks.setupLabels(gauge);\n        if (!((_c = (_b = gauge.props) === null || _b === void 0 ? void 0 : _b.pointer) === null || _c === void 0 ? void 0 : _c.hide)) pointerHooks.drawPointer(gauge, resize);\n        var gaugeTypeHeightCorrection = (_a = {}, _a[GaugeComponentProps_1.GaugeType.Semicircle] = 50, _a[GaugeComponentProps_1.GaugeType.Radial] = 55, _a[GaugeComponentProps_1.GaugeType.Grafana] = 55, _a);\n        var boundHeight = gauge.doughnut.current.node().getBoundingClientRect().height;\n        var boundWidth = gauge.container.current.node().getBoundingClientRect().width;\n        var gaugeType = gauge.props.type;\n        gauge.svg.current.attr(\"width\", boundWidth).attr(\"height\", boundHeight + gaugeTypeHeightCorrection[gaugeType]);\n    } else {\n        var arcsPropsChanged = JSON.stringify(gauge.prevProps.current.arc) !== JSON.stringify(gauge.props.arc);\n        var pointerPropsChanged = JSON.stringify(gauge.prevProps.current.pointer) !== JSON.stringify(gauge.props.pointer);\n        var valueChanged = JSON.stringify(gauge.prevProps.current.value) !== JSON.stringify(gauge.props.value);\n        var ticksChanged = JSON.stringify((_d = gauge.prevProps.current.labels) === null || _d === void 0 ? void 0 : _d.tickLabels) !== JSON.stringify(labels.tickLabels);\n        var shouldRedrawArcs = arcsPropsChanged;\n        if (shouldRedrawArcs) {\n            arcHooks.clearArcs(gauge);\n            arcHooks.setArcData(gauge);\n            arcHooks.setupArcs(gauge, resize);\n        }\n        //If pointer is hidden there's no need to redraw it when only value changes\n        var shouldRedrawPointer = pointerPropsChanged || valueChanged && !((_f = (_e = gauge.props) === null || _e === void 0 ? void 0 : _e.pointer) === null || _f === void 0 ? void 0 : _f.hide);\n        if (shouldRedrawPointer) {\n            pointerHooks.drawPointer(gauge);\n        }\n        if (arcsPropsChanged || ticksChanged) {\n            labelsHooks.clearTicks(gauge);\n            labelsHooks.setupTicks(gauge);\n        }\n        if (valueChanged) {\n            labelsHooks.clearValueLabel(gauge);\n            labelsHooks.setupValueLabel(gauge);\n        }\n    }\n};\nexports.renderChart = renderChart;\nvar updateDimensions = function(gauge) {\n    var marginInPercent = gauge.props.marginInPercent;\n    var dimensions = gauge.dimensions;\n    var divDimensions = gauge.container.current.node().getBoundingClientRect(), divWidth = divDimensions.width, divHeight = divDimensions.height;\n    if (dimensions.current.fixedHeight == 0) dimensions.current.fixedHeight = divHeight + 200;\n    //Set the new width and horizontal margins\n    var isMarginBox = typeof marginInPercent == \"number\";\n    var marginLeft = isMarginBox ? marginInPercent : marginInPercent.left;\n    var marginRight = isMarginBox ? marginInPercent : marginInPercent.right;\n    var marginTop = isMarginBox ? marginInPercent : marginInPercent.top;\n    var marginBottom = isMarginBox ? marginInPercent : marginInPercent.bottom;\n    dimensions.current.margin.left = divWidth * marginLeft;\n    dimensions.current.margin.right = divWidth * marginRight;\n    dimensions.current.width = divWidth - dimensions.current.margin.left - dimensions.current.margin.right;\n    dimensions.current.margin.top = dimensions.current.fixedHeight * marginTop;\n    dimensions.current.margin.bottom = dimensions.current.fixedHeight * marginBottom;\n    dimensions.current.height = dimensions.current.width / 2 - dimensions.current.margin.top - dimensions.current.margin.bottom;\n//gauge.height.current = divHeight - dimensions.current.margin.top - dimensions.current.margin.bottom;\n};\nexports.updateDimensions = updateDimensions;\nvar calculateRadius = function(gauge) {\n    var dimensions = gauge.dimensions;\n    //The radius needs to be constrained by the containing div\n    //Since it is a half circle we are dealing with the height of the div\n    //Only needs to be half of the width, because the width needs to be 2 * radius\n    //For the whole arc to fit\n    //First check if it is the width or the height that is the \"limiting\" dimension\n    if (dimensions.current.width < 2 * dimensions.current.height) {\n        //Then the width limits the size of the chart\n        //Set the radius to the width - the horizontal margins\n        dimensions.current.outerRadius = (dimensions.current.width - dimensions.current.margin.left - dimensions.current.margin.right) / 2;\n    } else {\n        dimensions.current.outerRadius = dimensions.current.height - dimensions.current.margin.top - dimensions.current.margin.bottom + 35;\n    }\n    (0, exports.centerGraph)(gauge);\n};\nexports.calculateRadius = calculateRadius;\n//Calculates new margins to make the graph centered\nvar centerGraph = function(gauge) {\n    var dimensions = gauge.dimensions;\n    dimensions.current.margin.left = dimensions.current.width / 2 - dimensions.current.outerRadius + dimensions.current.margin.right;\n    gauge.g.current.attr(\"transform\", \"translate(\" + dimensions.current.margin.left + \", \" + dimensions.current.margin.top + \")\");\n};\nexports.centerGraph = centerGraph;\nvar clearChart = function(gauge) {\n    //Remove the old stuff\n    labelsHooks.clearTicks(gauge);\n    labelsHooks.clearValueLabel(gauge);\n    pointerHooks.clearPointerElement(gauge);\n    arcHooks.clearArcs(gauge);\n};\nexports.clearChart = clearChart;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/chart.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/labels.js":
/*!************************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/labels.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __assign = (void 0) && (void 0).__assign || function() {\n    __assign = Object.assign || function(t) {\n        for(var s, i = 1, n = arguments.length; i < n; i++){\n            s = arguments[i];\n            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.calculateAnchorAndAngleByValue = exports.clearTicks = exports.clearValueLabel = exports.addValueText = exports.addText = exports.getLabelCoordsByValue = exports.addTick = exports.addTickValue = exports.addTickLine = exports.mapTick = exports.addArcTicks = exports.setupTicks = exports.setupValueLabel = exports.setupLabels = void 0;\nvar utils = __importStar(__webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js\"));\nvar constants_1 = __importDefault(__webpack_require__(/*! ../constants */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/constants.js\"));\nvar Tick_1 = __webpack_require__(/*! ../types/Tick */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tick.js\");\nvar d3 = __importStar(__webpack_require__(/*! d3 */ \"(ssr)/./node_modules/d3/src/index.js\"));\nvar GaugeComponentProps_1 = __webpack_require__(/*! ../types/GaugeComponentProps */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nvar arc_1 = __webpack_require__(/*! ./arc */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\");\nvar setupLabels = function(gauge) {\n    (0, exports.setupValueLabel)(gauge);\n    (0, exports.setupTicks)(gauge);\n};\nexports.setupLabels = setupLabels;\nvar setupValueLabel = function(gauge) {\n    var _a;\n    var labels = gauge.props.labels;\n    if (!((_a = labels === null || labels === void 0 ? void 0 : labels.valueLabel) === null || _a === void 0 ? void 0 : _a.hide)) (0, exports.addValueText)(gauge);\n};\nexports.setupValueLabel = setupValueLabel;\nvar setupTicks = function(gauge) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n    var labels = gauge.props.labels;\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    if (constants_1.default.debugTicksRadius) {\n        for(var index = 0; index < maxValue; index++){\n            var indexTick = (0, exports.mapTick)(index, gauge);\n            (0, exports.addTick)(indexTick, gauge);\n        }\n    } else if (!((_a = labels.tickLabels) === null || _a === void 0 ? void 0 : _a.hideMinMax)) {\n        var alreadyHaveMinValueTick = (_c = (_b = labels.tickLabels) === null || _b === void 0 ? void 0 : _b.ticks) === null || _c === void 0 ? void 0 : _c.some(function(tick) {\n            return tick.value == minValue;\n        });\n        if (!alreadyHaveMinValueTick) {\n            //Add min value tick\n            var minValueTick = (0, exports.mapTick)(minValue, gauge);\n            (0, exports.addTick)(minValueTick, gauge);\n        }\n        var alreadyHaveMaxValueTick = (_e = (_d = labels.tickLabels) === null || _d === void 0 ? void 0 : _d.ticks) === null || _e === void 0 ? void 0 : _e.some(function(tick) {\n            return tick.value == maxValue;\n        });\n        if (!alreadyHaveMaxValueTick) {\n            // //Add max value tick\n            var maxValueTick = (0, exports.mapTick)(maxValue, gauge);\n            (0, exports.addTick)(maxValueTick, gauge);\n        }\n    }\n    if (((_g = (_f = labels.tickLabels) === null || _f === void 0 ? void 0 : _f.ticks) === null || _g === void 0 ? void 0 : _g.length) > 0) {\n        (_j = (_h = labels.tickLabels) === null || _h === void 0 ? void 0 : _h.ticks) === null || _j === void 0 ? void 0 : _j.forEach(function(tick) {\n            (0, exports.addTick)(tick, gauge);\n        });\n    }\n    (0, exports.addArcTicks)(gauge);\n};\nexports.setupTicks = setupTicks;\nvar addArcTicks = function(gauge) {\n    var _a;\n    (_a = gauge.arcData.current) === null || _a === void 0 ? void 0 : _a.map(function(subArc) {\n        if (subArc.showTick) return subArc.limit;\n    }).forEach(function(tickValue) {\n        if (tickValue) (0, exports.addTick)((0, exports.mapTick)(tickValue, gauge), gauge);\n    });\n};\nexports.addArcTicks = addArcTicks;\nvar mapTick = function(value, gauge) {\n    var tickLabels = gauge.props.labels.tickLabels;\n    return {\n        value: value,\n        valueConfig: tickLabels === null || tickLabels === void 0 ? void 0 : tickLabels.defaultTickValueConfig,\n        lineConfig: tickLabels === null || tickLabels === void 0 ? void 0 : tickLabels.defaultTickLineConfig\n    };\n};\nexports.mapTick = mapTick;\nvar addTickLine = function(tick, gauge) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;\n    var labels = gauge.props.labels;\n    var _u = (0, exports.calculateAnchorAndAngleByValue)(tick === null || tick === void 0 ? void 0 : tick.value, gauge), tickAnchor = _u.tickAnchor, angle = _u.angle;\n    var tickDistanceFromArc = ((_a = tick.lineConfig) === null || _a === void 0 ? void 0 : _a.distanceFromArc) || ((_c = (_b = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _b === void 0 ? void 0 : _b.defaultTickLineConfig) === null || _c === void 0 ? void 0 : _c.distanceFromArc) || 0;\n    if (((_e = (_d = gauge.props.labels) === null || _d === void 0 ? void 0 : _d.tickLabels) === null || _e === void 0 ? void 0 : _e.type) == \"outer\") tickDistanceFromArc = -tickDistanceFromArc;\n    // else tickDistanceFromArc = tickDistanceFromArc - 10;\n    var coords = (0, exports.getLabelCoordsByValue)(tick === null || tick === void 0 ? void 0 : tick.value, gauge, tickDistanceFromArc);\n    var tickColor = ((_f = tick.lineConfig) === null || _f === void 0 ? void 0 : _f.color) || ((_h = (_g = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _g === void 0 ? void 0 : _g.defaultTickLineConfig) === null || _h === void 0 ? void 0 : _h.color) || ((_j = Tick_1.defaultTickLabels.defaultTickLineConfig) === null || _j === void 0 ? void 0 : _j.color);\n    var tickWidth = ((_k = tick.lineConfig) === null || _k === void 0 ? void 0 : _k.width) || ((_m = (_l = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _l === void 0 ? void 0 : _l.defaultTickLineConfig) === null || _m === void 0 ? void 0 : _m.width) || ((_o = Tick_1.defaultTickLabels.defaultTickLineConfig) === null || _o === void 0 ? void 0 : _o.width);\n    var tickLength = ((_p = tick.lineConfig) === null || _p === void 0 ? void 0 : _p.length) || ((_r = (_q = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _q === void 0 ? void 0 : _q.defaultTickLineConfig) === null || _r === void 0 ? void 0 : _r.length) || ((_s = Tick_1.defaultTickLabels.defaultTickLineConfig) === null || _s === void 0 ? void 0 : _s.length);\n    // Calculate the end coordinates based on the adjusted position\n    var endX;\n    var endY;\n    // When inner should draw from outside to inside\n    // When outer should draw from inside to outside\n    if (((_t = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _t === void 0 ? void 0 : _t.type) == \"inner\") {\n        endX = coords.x + tickLength * Math.cos(angle * Math.PI / 180);\n        endY = coords.y + tickLength * Math.sin(angle * Math.PI / 180);\n    } else {\n        endX = coords.x - tickLength * Math.cos(angle * Math.PI / 180);\n        endY = coords.y - tickLength * Math.sin(angle * Math.PI / 180);\n    }\n    // (gauge.dimensions.current.outerRadius - gauge.dimensions.current.innerRadius)\n    // Create a D3 line generator\n    var lineGenerator = d3.line();\n    var lineCoordinates;\n    // Define the line coordinates\n    lineCoordinates = [\n        [\n            coords.x,\n            coords.y\n        ],\n        [\n            endX,\n            endY\n        ]\n    ];\n    // Append a path element for the line\n    gauge.g.current.append(\"path\").datum(lineCoordinates).attr(\"class\", constants_1.default.tickLineClassname).attr(\"d\", lineGenerator)// .attr(\"transform\", `translate(${0}, ${0})`)\n    .attr(\"stroke\", tickColor).attr(\"stroke-width\", tickWidth).attr(\"fill\", \"none\");\n// .attr(\"stroke-linecap\", \"round\")\n// .attr(\"stroke-linejoin\", \"round\")\n// .attr(\"transform\", `rotate(${angle})`);\n};\nexports.addTickLine = addTickLine;\nvar addTickValue = function(tick, gauge) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;\n    var labels = gauge.props.labels;\n    var arc = gauge.props.arc;\n    var arcWidth = arc.width;\n    var tickValue = tick === null || tick === void 0 ? void 0 : tick.value;\n    var tickAnchor = (0, exports.calculateAnchorAndAngleByValue)(tickValue, gauge).tickAnchor;\n    var centerToArcLengthSubtract = 27 - arcWidth * 10;\n    var isInner = ((_a = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _a === void 0 ? void 0 : _a.type) == \"inner\";\n    if (!isInner) centerToArcLengthSubtract = arcWidth * 10 - 10;\n    else centerToArcLengthSubtract -= 10;\n    var tickDistanceFromArc = ((_b = tick.lineConfig) === null || _b === void 0 ? void 0 : _b.distanceFromArc) || ((_d = (_c = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _c === void 0 ? void 0 : _c.defaultTickLineConfig) === null || _d === void 0 ? void 0 : _d.distanceFromArc) || 0;\n    var tickLength = ((_e = tick.lineConfig) === null || _e === void 0 ? void 0 : _e.length) || ((_g = (_f = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _f === void 0 ? void 0 : _f.defaultTickLineConfig) === null || _g === void 0 ? void 0 : _g.length) || 0;\n    var _shouldHideTickLine = shouldHideTickLine(tick, gauge);\n    if (!_shouldHideTickLine) {\n        if (isInner) {\n            centerToArcLengthSubtract += tickDistanceFromArc;\n            centerToArcLengthSubtract += tickLength;\n        } else {\n            centerToArcLengthSubtract -= tickDistanceFromArc;\n            centerToArcLengthSubtract -= tickLength;\n        }\n    }\n    var coords = (0, exports.getLabelCoordsByValue)(tickValue, gauge, centerToArcLengthSubtract);\n    var tickValueStyle = ((_h = tick.valueConfig) === null || _h === void 0 ? void 0 : _h.style) || ((_k = (_j = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _j === void 0 ? void 0 : _j.defaultTickValueConfig) === null || _k === void 0 ? void 0 : _k.style) || {};\n    tickValueStyle = __assign({}, tickValueStyle);\n    var text = \"\";\n    var maxDecimalDigits = ((_l = tick.valueConfig) === null || _l === void 0 ? void 0 : _l.maxDecimalDigits) || ((_o = (_m = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _m === void 0 ? void 0 : _m.defaultTickValueConfig) === null || _o === void 0 ? void 0 : _o.maxDecimalDigits);\n    if ((_p = tick.valueConfig) === null || _p === void 0 ? void 0 : _p.formatTextValue) {\n        text = tick.valueConfig.formatTextValue(utils.floatingNumber(tickValue, maxDecimalDigits));\n    } else if ((_r = (_q = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _q === void 0 ? void 0 : _q.defaultTickValueConfig) === null || _r === void 0 ? void 0 : _r.formatTextValue) {\n        text = labels.tickLabels.defaultTickValueConfig.formatTextValue(utils.floatingNumber(tickValue, maxDecimalDigits));\n    } else if (gauge.props.minValue === 0 && gauge.props.maxValue === 100) {\n        text = utils.floatingNumber(tickValue, maxDecimalDigits).toString();\n        text += \"%\";\n    } else {\n        text = utils.floatingNumber(tickValue, maxDecimalDigits).toString();\n    }\n    if (((_s = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _s === void 0 ? void 0 : _s.type) == \"inner\") {\n        if (tickAnchor === \"end\") coords.x += 10;\n        if (tickAnchor === \"start\") coords.x -= 10;\n    // if (tickAnchor === \"middle\") coords.y -= 0;\n    } else {\n        // if(tickAnchor === \"end\") coords.x -= 10;\n        // if(tickAnchor === \"start\") coords.x += 10;\n        if (tickAnchor === \"middle\") coords.y += 2;\n    }\n    if (tickAnchor === \"middle\") {\n        coords.y += 0;\n    } else {\n        coords.y += 3;\n    }\n    tickValueStyle.textAnchor = tickAnchor;\n    (0, exports.addText)(text, coords.x, coords.y, gauge, tickValueStyle, constants_1.default.tickValueClassname);\n};\nexports.addTickValue = addTickValue;\nvar addTick = function(tick, gauge) {\n    var labels = gauge.props.labels;\n    //Make validation for sequence of values respecting DEFAULT -> DEFAULT FROM USER -> SPECIFIC TICK VALUE\n    var _shouldHideTickLine = shouldHideTickLine(tick, gauge);\n    var _shouldHideTickValue = shouldHideTickValue(tick, gauge);\n    if (!_shouldHideTickLine) (0, exports.addTickLine)(tick, gauge);\n    if (!constants_1.default.debugTicksRadius && !_shouldHideTickValue) {\n        (0, exports.addTickValue)(tick, gauge);\n    }\n};\nexports.addTick = addTick;\nvar getLabelCoordsByValue = function(value, gauge, centerToArcLengthSubtract) {\n    var _a;\n    if (centerToArcLengthSubtract === void 0) {\n        centerToArcLengthSubtract = 0;\n    }\n    var labels = gauge.props.labels;\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    var type = (_a = labels.tickLabels) === null || _a === void 0 ? void 0 : _a.type;\n    var _b = (0, arc_1.getCoordByValue)(value, gauge, type, centerToArcLengthSubtract, 0.93), x = _b.x, y = _b.y;\n    var percent = utils.calculatePercentage(minValue, maxValue, value);\n    //This corrects labels in the cener being too close from the arc\n    // let isValueBetweenCenter = percent > CONSTANTS.rangeBetweenCenteredTickValueLabel[0] && \n    //                               percent < CONSTANTS.rangeBetweenCenteredTickValueLabel[1];\n    // if (isValueBetweenCenter){\n    //   let isInner = type == \"inner\";\n    //   y+= isInner ? 8 : -1;\n    // }\n    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Radial) {\n        y += 3;\n    }\n    return {\n        x: x,\n        y: y\n    };\n};\nexports.getLabelCoordsByValue = getLabelCoordsByValue;\nvar addText = function(html, x, y, gauge, style, className, rotate) {\n    if (rotate === void 0) {\n        rotate = 0;\n    }\n    var div = gauge.g.current.append(\"g\").attr(\"class\", className).attr(\"transform\", \"translate(\".concat(x, \", \").concat(y, \")\")).append(\"text\").text(html); // use html() instead of text()\n    applyTextStyles(div, style);\n    div.attr(\"transform\", \"rotate(\".concat(rotate, \")\"));\n};\nexports.addText = addText;\nvar applyTextStyles = function(div, style) {\n    //Apply default styles\n    Object.entries(style).forEach(function(_a) {\n        var key = _a[0], value = _a[1];\n        return div.style(utils.camelCaseToKebabCase(key), value);\n    });\n    //Apply custom styles\n    if (style != undefined) Object.entries(style).forEach(function(_a) {\n        var key = _a[0], value = _a[1];\n        return div.style(utils.camelCaseToKebabCase(key), value);\n    });\n};\n//Adds text undeneath the graft to display which percentage is the current one\nvar addValueText = function(gauge) {\n    var _a, _b, _c;\n    var labels = gauge.props.labels;\n    var value = gauge.props.value;\n    var valueLabel = labels === null || labels === void 0 ? void 0 : labels.valueLabel;\n    var textPadding = 20;\n    var text = \"\";\n    var maxDecimalDigits = (_a = labels === null || labels === void 0 ? void 0 : labels.valueLabel) === null || _a === void 0 ? void 0 : _a.maxDecimalDigits;\n    var floatValue = utils.floatingNumber(value, maxDecimalDigits);\n    if (valueLabel.formatTextValue) {\n        text = valueLabel.formatTextValue(floatValue);\n    } else if (gauge.props.minValue === 0 && gauge.props.maxValue === 100) {\n        text = floatValue.toString();\n        text += \"%\";\n    } else {\n        text = floatValue.toString();\n    }\n    var maxLengthBeforeComputation = 4;\n    var textLength = (text === null || text === void 0 ? void 0 : text.length) || 0;\n    var fontRatio = textLength > maxLengthBeforeComputation ? maxLengthBeforeComputation / textLength * 1.5 : 1; // Compute the font size ratio\n    var valueFontSize = (_b = valueLabel === null || valueLabel === void 0 ? void 0 : valueLabel.style) === null || _b === void 0 ? void 0 : _b.fontSize;\n    var valueTextStyle = __assign({}, valueLabel.style);\n    var x = gauge.dimensions.current.outerRadius;\n    var y = 0;\n    valueTextStyle.textAnchor = \"middle\";\n    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Semicircle) {\n        y = gauge.dimensions.current.outerRadius / 1.5 + textPadding;\n    } else if (gauge.props.type == GaugeComponentProps_1.GaugeType.Radial) {\n        y = gauge.dimensions.current.outerRadius * 1.45 + textPadding;\n    } else if (gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana) {\n        y = gauge.dimensions.current.outerRadius * 1.0 + textPadding;\n    }\n    //if(gauge.props.pointer.type == PointerType.Arrow){\n    //  y = gauge.dimensions.current.outerRadius * 0.79 + textPadding;\n    //}\n    var widthFactor = gauge.props.type == GaugeComponentProps_1.GaugeType.Radial ? 0.003 : 0.003;\n    fontRatio = gauge.dimensions.current.width * widthFactor * fontRatio;\n    var fontSizeNumber = parseInt(valueFontSize, 10) * fontRatio;\n    valueTextStyle.fontSize = fontSizeNumber + \"px\";\n    if (valueLabel.matchColorWithArc) valueTextStyle.fill = ((_c = (0, arc_1.getArcDataByValue)(value, gauge)) === null || _c === void 0 ? void 0 : _c.color) || \"white\";\n    (0, exports.addText)(text, x, y, gauge, valueTextStyle, constants_1.default.valueLabelClassname);\n};\nexports.addValueText = addValueText;\nvar clearValueLabel = function(gauge) {\n    return gauge.g.current.selectAll(\".\".concat(constants_1.default.valueLabelClassname)).remove();\n};\nexports.clearValueLabel = clearValueLabel;\nvar clearTicks = function(gauge) {\n    gauge.g.current.selectAll(\".\".concat(constants_1.default.tickLineClassname)).remove();\n    gauge.g.current.selectAll(\".\".concat(constants_1.default.tickValueClassname)).remove();\n};\nexports.clearTicks = clearTicks;\nvar calculateAnchorAndAngleByValue = function(value, gauge) {\n    var _a;\n    var _b;\n    var labels = gauge.props.labels;\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    var valuePercentage = utils.calculatePercentage(minValue, maxValue, value);\n    var gaugeTypesAngles = (_a = {}, _a[GaugeComponentProps_1.GaugeType.Grafana] = {\n        startAngle: -20,\n        endAngle: 220\n    }, _a[GaugeComponentProps_1.GaugeType.Semicircle] = {\n        startAngle: 0,\n        endAngle: 180\n    }, _a[GaugeComponentProps_1.GaugeType.Radial] = {\n        startAngle: -42,\n        endAngle: 266\n    }, _a);\n    var _c = gaugeTypesAngles[gauge.props.type], startAngle = _c.startAngle, endAngle = _c.endAngle;\n    var angle = startAngle + valuePercentage * 100 * endAngle / 100;\n    var isValueLessThanHalf = valuePercentage < 0.5;\n    //Values between 40% and 60% are aligned in the middle\n    var isValueBetweenTolerance = valuePercentage > constants_1.default.rangeBetweenCenteredTickValueLabel[0] && valuePercentage < constants_1.default.rangeBetweenCenteredTickValueLabel[1];\n    var tickAnchor = \"\";\n    var isInner = ((_b = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _b === void 0 ? void 0 : _b.type) == \"inner\";\n    if (isValueBetweenTolerance) {\n        tickAnchor = \"middle\";\n    } else if (isValueLessThanHalf) {\n        tickAnchor = isInner ? \"start\" : \"end\";\n    } else {\n        tickAnchor = isInner ? \"end\" : \"start\";\n    }\n    // if(valuePercentage > 0.50) angle = angle - 180;\n    return {\n        tickAnchor: tickAnchor,\n        angle: angle\n    };\n};\nexports.calculateAnchorAndAngleByValue = calculateAnchorAndAngleByValue;\nvar shouldHideTickLine = function(tick, gauge) {\n    var _a, _b, _c, _d;\n    var labels = gauge.props.labels;\n    var defaultHideValue = (_a = Tick_1.defaultTickLabels.defaultTickLineConfig) === null || _a === void 0 ? void 0 : _a.hide;\n    var shouldHide = defaultHideValue;\n    var defaultHideLineFromUser = (_c = (_b = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _b === void 0 ? void 0 : _b.defaultTickLineConfig) === null || _c === void 0 ? void 0 : _c.hide;\n    if (defaultHideLineFromUser != undefined) {\n        shouldHide = defaultHideLineFromUser;\n    }\n    var specificHideValueFromUser = (_d = tick.lineConfig) === null || _d === void 0 ? void 0 : _d.hide;\n    if (specificHideValueFromUser != undefined) {\n        shouldHide = specificHideValueFromUser;\n    }\n    return shouldHide;\n};\nvar shouldHideTickValue = function(tick, gauge) {\n    var _a, _b, _c, _d;\n    var labels = gauge.props.labels;\n    var defaultHideValue = (_a = Tick_1.defaultTickLabels.defaultTickValueConfig) === null || _a === void 0 ? void 0 : _a.hide;\n    var shouldHide = defaultHideValue;\n    var defaultHideValueFromUser = (_c = (_b = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _b === void 0 ? void 0 : _b.defaultTickValueConfig) === null || _c === void 0 ? void 0 : _c.hide;\n    if (defaultHideValueFromUser != undefined) {\n        shouldHide = defaultHideValueFromUser;\n    }\n    var specificHideValueFromUser = (_d = tick.valueConfig) === null || _d === void 0 ? void 0 : _d.hide;\n    if (specificHideValueFromUser != undefined) {\n        shouldHide = specificHideValueFromUser;\n    }\n    return shouldHide;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/labels.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/pointer.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/pointer.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.clearPointerElement = exports.addPointerElement = exports.translatePointer = exports.drawPointer = void 0;\nvar d3_1 = __webpack_require__(/*! d3 */ \"(ssr)/./node_modules/d3/src/index.js\");\nvar Pointer_1 = __webpack_require__(/*! ../types/Pointer */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js\");\nvar arc_1 = __webpack_require__(/*! ./arc */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\");\nvar utils = __importStar(__webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js\"));\nvar arcHooks = __importStar(__webpack_require__(/*! ./arc */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\"));\nvar GaugeComponentProps_1 = __webpack_require__(/*! ../types/GaugeComponentProps */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nvar drawPointer = function(gauge, resize) {\n    var _a;\n    if (resize === void 0) {\n        resize = false;\n    }\n    gauge.pointer.current.context = setupContext(gauge);\n    var _b = gauge.pointer.current.context, prevPercent = _b.prevPercent, currentPercent = _b.currentPercent, prevProgress = _b.prevProgress;\n    var pointer = gauge.props.pointer;\n    var isFirstTime = ((_a = gauge.prevProps) === null || _a === void 0 ? void 0 : _a.current.value) == undefined;\n    if ((isFirstTime || resize) && gauge.props.type != GaugeComponentProps_1.GaugeType.Grafana) initPointer(gauge);\n    var shouldAnimate = (!resize || isFirstTime) && pointer.animate;\n    if (shouldAnimate) {\n        gauge.doughnut.current.transition().delay(pointer.animationDelay).ease(pointer.elastic ? d3_1.easeElastic : d3_1.easeExpOut).duration(pointer.animationDuration).tween(\"progress\", function() {\n            var currentInterpolatedPercent = (0, d3_1.interpolateNumber)(prevPercent, currentPercent);\n            return function(percentOfPercent) {\n                var progress = currentInterpolatedPercent(percentOfPercent);\n                if (isProgressValid(progress, prevProgress, gauge)) {\n                    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana) {\n                        arcHooks.clearArcs(gauge);\n                        arcHooks.drawArc(gauge, progress);\n                    //arcHooks.setupArcs(gauge);\n                    } else {\n                        updatePointer(progress, gauge);\n                    }\n                }\n                gauge.pointer.current.context.prevProgress = progress;\n            };\n        });\n    } else {\n        updatePointer(currentPercent, gauge);\n    }\n};\nexports.drawPointer = drawPointer;\nvar setupContext = function(gauge) {\n    var _a;\n    var value = gauge.props.value;\n    var pointer = gauge.props.pointer;\n    var pointerLength = pointer.length;\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    var pointerPath = gauge.pointer.current.context.pointerPath;\n    var pointerRadius = getPointerRadius(gauge);\n    var length = pointer.type == Pointer_1.PointerType.Needle ? pointerLength : 0.2;\n    var typesWithPath = [\n        Pointer_1.PointerType.Needle,\n        Pointer_1.PointerType.Arrow\n    ];\n    var pointerContext = {\n        centerPoint: [\n            0,\n            -pointerRadius / 2\n        ],\n        pointerRadius: getPointerRadius(gauge),\n        pathLength: gauge.dimensions.current.outerRadius * length,\n        currentPercent: utils.calculatePercentage(minValue, maxValue, value),\n        prevPercent: utils.calculatePercentage(minValue, maxValue, ((_a = gauge.prevProps) === null || _a === void 0 ? void 0 : _a.current.value) || minValue),\n        prevProgress: 0,\n        pathStr: \"\",\n        shouldDrawPath: typesWithPath.includes(pointer.type),\n        prevColor: \"\"\n    };\n    return pointerContext;\n};\nvar initPointer = function(gauge) {\n    var value = gauge.props.value;\n    var pointer = gauge.props.pointer;\n    var _a = gauge.pointer.current.context, shouldDrawPath = _a.shouldDrawPath, centerPoint = _a.centerPoint, pointerRadius = _a.pointerRadius, pathStr = _a.pathStr, currentPercent = _a.currentPercent, prevPercent = _a.prevPercent;\n    if (shouldDrawPath) {\n        gauge.pointer.current.context.pathStr = calculatePointerPath(gauge, prevPercent || currentPercent);\n        gauge.pointer.current.path = gauge.pointer.current.element.append(\"path\").attr(\"d\", gauge.pointer.current.context.pathStr).attr(\"fill\", pointer.color);\n    }\n    //Add a circle at the bottom of pointer\n    if (pointer.type == Pointer_1.PointerType.Needle) {\n        gauge.pointer.current.element.append(\"circle\").attr(\"cx\", centerPoint[0]).attr(\"cy\", centerPoint[1]).attr(\"r\", pointerRadius).attr(\"fill\", pointer.color);\n    } else if (pointer.type == Pointer_1.PointerType.Blob) {\n        gauge.pointer.current.element.append(\"circle\").attr(\"cx\", centerPoint[0]).attr(\"cy\", centerPoint[1]).attr(\"r\", pointerRadius).attr(\"fill\", pointer.baseColor).attr(\"stroke\", pointer.color).attr(\"stroke-width\", pointer.strokeWidth * pointerRadius / 10);\n    }\n    //Translate the pointer starting point of the arc\n    setPointerPosition(pointerRadius, value, gauge);\n};\nvar updatePointer = function(percentage, gauge) {\n    var _a;\n    var pointer = gauge.props.pointer;\n    var _b = gauge.pointer.current.context, pointerRadius = _b.pointerRadius, shouldDrawPath = _b.shouldDrawPath, prevColor = _b.prevColor;\n    setPointerPosition(pointerRadius, percentage, gauge);\n    if (shouldDrawPath && gauge.props.type != GaugeComponentProps_1.GaugeType.Grafana) gauge.pointer.current.path.attr(\"d\", calculatePointerPath(gauge, percentage));\n    if (pointer.type == Pointer_1.PointerType.Blob) {\n        var currentColor = (_a = arcHooks.getArcDataByPercentage(percentage, gauge)) === null || _a === void 0 ? void 0 : _a.color;\n        var shouldChangeColor = currentColor != prevColor;\n        if (shouldChangeColor) gauge.pointer.current.element.select(\"circle\").attr(\"stroke\", currentColor);\n        var strokeWidth = pointer.strokeWidth * pointerRadius / 10;\n        gauge.pointer.current.element.select(\"circle\").attr(\"stroke-width\", strokeWidth);\n        gauge.pointer.current.context.prevColor = currentColor;\n    }\n};\nvar setPointerPosition = function(pointerRadius, progress, gauge) {\n    var _a;\n    var pointer = gauge.props.pointer;\n    var pointerType = pointer.type;\n    var dimensions = gauge.dimensions;\n    var value = utils.getCurrentGaugeValueByPercentage(progress, gauge);\n    var pointers = (_a = {}, _a[Pointer_1.PointerType.Needle] = function() {\n        // Set needle position to center\n        (0, exports.translatePointer)(dimensions.current.outerRadius, dimensions.current.outerRadius, gauge);\n    }, _a[Pointer_1.PointerType.Arrow] = function() {\n        var _a = (0, arc_1.getCoordByValue)(value, gauge, \"inner\", 0, 0.70), x = _a.x, y = _a.y;\n        x -= 1;\n        y += pointerRadius - 3;\n        (0, exports.translatePointer)(x, y, gauge);\n    }, _a[Pointer_1.PointerType.Blob] = function() {\n        var _a = (0, arc_1.getCoordByValue)(value, gauge, \"between\", 0, 0.75), x = _a.x, y = _a.y;\n        x -= 1;\n        y += pointerRadius;\n        (0, exports.translatePointer)(x, y, gauge);\n    }, _a);\n    return pointers[pointerType]();\n};\nvar isProgressValid = function(currentPercent, prevPercent, gauge) {\n    //Avoid unnecessary re-rendering (when progress is too small) but allow the pointer to reach the final value\n    var overFlow = currentPercent > 1 || currentPercent < 0;\n    var tooSmallValue = Math.abs(currentPercent - prevPercent) < 0.0001;\n    var sameValueAsBefore = currentPercent == prevPercent;\n    return !tooSmallValue && !sameValueAsBefore && !overFlow;\n};\nvar calculatePointerPath = function(gauge, percent) {\n    var _a = gauge.pointer.current.context, centerPoint = _a.centerPoint, pointerRadius = _a.pointerRadius, pathLength = _a.pathLength;\n    var startAngle = utils.degToRad(gauge.props.type == GaugeComponentProps_1.GaugeType.Semicircle ? 0 : -42);\n    var endAngle = utils.degToRad(gauge.props.type == GaugeComponentProps_1.GaugeType.Semicircle ? 180 : 223);\n    var angle = startAngle + percent * (endAngle - startAngle);\n    var topPoint = [\n        centerPoint[0] - pathLength * Math.cos(angle),\n        centerPoint[1] - pathLength * Math.sin(angle)\n    ];\n    var thetaMinusHalfPi = angle - Math.PI / 2;\n    var leftPoint = [\n        centerPoint[0] - pointerRadius * Math.cos(thetaMinusHalfPi),\n        centerPoint[1] - pointerRadius * Math.sin(thetaMinusHalfPi)\n    ];\n    var thetaPlusHalfPi = angle + Math.PI / 2;\n    var rightPoint = [\n        centerPoint[0] - pointerRadius * Math.cos(thetaPlusHalfPi),\n        centerPoint[1] - pointerRadius * Math.sin(thetaPlusHalfPi)\n    ];\n    var pathStr = \"M \".concat(leftPoint[0], \" \").concat(leftPoint[1], \" L \").concat(topPoint[0], \" \").concat(topPoint[1], \" L \").concat(rightPoint[0], \" \").concat(rightPoint[1]);\n    return pathStr;\n};\nvar getPointerRadius = function(gauge) {\n    var pointer = gauge.props.pointer;\n    var pointerWidth = pointer.width;\n    return pointerWidth * (gauge.dimensions.current.width / 500);\n};\nvar translatePointer = function(x, y, gauge) {\n    return gauge.pointer.current.element.attr(\"transform\", \"translate(\" + x + \", \" + y + \")\");\n};\nexports.translatePointer = translatePointer;\nvar addPointerElement = function(gauge) {\n    return gauge.pointer.current.element = gauge.g.current.append(\"g\").attr(\"class\", \"pointer\");\n};\nexports.addPointerElement = addPointerElement;\nvar clearPointerElement = function(gauge) {\n    return gauge.pointer.current.element.selectAll(\"*\").remove();\n};\nexports.clearPointerElement = clearPointerElement;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/pointer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nvar __assign = (void 0) && (void 0).__assign || function() {\n    __assign = Object.assign || function(t) {\n        for(var s, i = 1, n = arguments.length; i < n; i++){\n            s = arguments[i];\n            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.camelCaseToKebabCase = exports.getCurrentGaugeValueByPercentage = exports.getCurrentGaugePercentageByValue = exports.degToRad = exports.normalize = exports.floatingNumber = exports.percentToRad = exports.mergeObjects = exports.isEmptyObject = exports.calculatePercentage = void 0;\nvar calculatePercentage = function(minValue, maxValue, value) {\n    if (value < minValue) {\n        return 0;\n    } else if (value > maxValue) {\n        return 1;\n    } else {\n        var percentage = (value - minValue) / (maxValue - minValue);\n        return percentage;\n    }\n};\nexports.calculatePercentage = calculatePercentage;\nvar isEmptyObject = function(obj) {\n    return Object.keys(obj).length === 0 && obj.constructor === Object;\n};\nexports.isEmptyObject = isEmptyObject;\nvar mergeObjects = function(obj1, obj2) {\n    var mergedObj = __assign({}, obj1);\n    Object.keys(obj2).forEach(function(key) {\n        var val1 = obj1[key];\n        var val2 = obj2[key];\n        if (Array.isArray(val1) && Array.isArray(val2)) {\n            mergedObj[key] = val2;\n        } else if (typeof val1 === \"object\" && typeof val2 === \"object\") {\n            mergedObj[key] = (0, exports.mergeObjects)(val1, val2);\n        } else if (val2 !== undefined) {\n            mergedObj[key] = val2;\n        }\n    });\n    return mergedObj;\n};\nexports.mergeObjects = mergeObjects;\n//Returns the angle (in rad) for the given 'percent' value where percent = 1 means 100% and is 180 degree angle\nvar percentToRad = function(percent, angle) {\n    return percent * (Math.PI / angle);\n};\nexports.percentToRad = percentToRad;\nvar floatingNumber = function(value, maxDigits) {\n    if (maxDigits === void 0) {\n        maxDigits = 2;\n    }\n    return Math.round(value * Math.pow(10, maxDigits)) / Math.pow(10, maxDigits);\n};\nexports.floatingNumber = floatingNumber;\n// Function to normalize a value between a new min and max\nfunction normalize(value, min, max) {\n    return (value - min) / (max - min) * 100;\n}\nexports.normalize = normalize;\nvar degToRad = function(degrees) {\n    return degrees * (Math.PI / 180);\n};\nexports.degToRad = degToRad;\nvar getCurrentGaugePercentageByValue = function(value, gauge) {\n    return (0, exports.calculatePercentage)(gauge.minValue, gauge.maxValue, value);\n};\nexports.getCurrentGaugePercentageByValue = getCurrentGaugePercentageByValue;\nvar getCurrentGaugeValueByPercentage = function(percentage, gauge) {\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    var value = minValue + percentage * (maxValue - minValue);\n    return value;\n};\nexports.getCurrentGaugeValueByPercentage = getCurrentGaugeValueByPercentage;\nvar camelCaseToKebabCase = function(str) {\n    return str.replace(/[A-Z]/g, function(letter) {\n        return \"-\".concat(letter.toLowerCase());\n    });\n};\nexports.camelCaseToKebabCase = camelCaseToKebabCase;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ2F1Z2UtY29tcG9uZW50L2Rpc3QvbGliL0dhdWdlQ29tcG9uZW50L2hvb2tzL3V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsSUFBSUEsV0FBVyxDQUFDLE1BQUcsS0FBSyxPQUFHLEVBQUVBLFFBQVEsSUFBSztJQUN0Q0EsV0FBV0MsT0FBT0MsTUFBTSxJQUFJLFNBQVNDLENBQUM7UUFDbEMsSUFBSyxJQUFJQyxHQUFHQyxJQUFJLEdBQUdDLElBQUlDLFVBQVVDLE1BQU0sRUFBRUgsSUFBSUMsR0FBR0QsSUFBSztZQUNqREQsSUFBSUcsU0FBUyxDQUFDRixFQUFFO1lBQ2hCLElBQUssSUFBSUksS0FBS0wsRUFBRyxJQUFJSCxPQUFPUyxTQUFTLENBQUNDLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDUixHQUFHSyxJQUN6RE4sQ0FBQyxDQUFDTSxFQUFFLEdBQUdMLENBQUMsQ0FBQ0ssRUFBRTtRQUNuQjtRQUNBLE9BQU9OO0lBQ1g7SUFDQSxPQUFPSCxTQUFTYSxLQUFLLENBQUMsSUFBSSxFQUFFTjtBQUNoQztBQUNBTiw4Q0FBNkM7SUFBRWUsT0FBTztBQUFLLENBQUMsRUFBQztBQUM3REQsNEJBQTRCLEdBQUdBLHdDQUF3QyxHQUFHQSx3Q0FBd0MsR0FBR0EsZ0JBQWdCLEdBQUdBLGlCQUFpQixHQUFHQSxzQkFBc0IsR0FBR0Esb0JBQW9CLEdBQUdBLG9CQUFvQixHQUFHQSxxQkFBcUIsR0FBR0EsMkJBQTJCLEdBQUcsS0FBSztBQUM5UixJQUFJVyxzQkFBc0IsU0FBVUMsUUFBUSxFQUFFQyxRQUFRLEVBQUVaLEtBQUs7SUFDekQsSUFBSUEsUUFBUVcsVUFBVTtRQUNsQixPQUFPO0lBQ1gsT0FDSyxJQUFJWCxRQUFRWSxVQUFVO1FBQ3ZCLE9BQU87SUFDWCxPQUNLO1FBQ0QsSUFBSUMsYUFBYSxDQUFDYixRQUFRVyxRQUFPLElBQU1DLENBQUFBLFdBQVdELFFBQU87UUFDekQsT0FBUUU7SUFDWjtBQUNKO0FBQ0FkLDJCQUEyQixHQUFHVztBQUM5QixJQUFJRCxnQkFBZ0IsU0FBVUssR0FBRztJQUM3QixPQUFPN0IsT0FBTzhCLElBQUksQ0FBQ0QsS0FBS3RCLE1BQU0sS0FBSyxLQUFLc0IsSUFBSUUsV0FBVyxLQUFLL0I7QUFDaEU7QUFDQWMscUJBQXFCLEdBQUdVO0FBQ3hCLElBQUlELGVBQWUsU0FBVVMsSUFBSSxFQUFFQyxJQUFJO0lBQ25DLElBQUlDLFlBQVluQyxTQUFTLENBQUMsR0FBR2lDO0lBQzdCaEMsT0FBTzhCLElBQUksQ0FBQ0csTUFBTUUsT0FBTyxDQUFDLFNBQVVDLEdBQUc7UUFDbkMsSUFBSUMsT0FBT0wsSUFBSSxDQUFDSSxJQUFJO1FBQ3BCLElBQUlFLE9BQU9MLElBQUksQ0FBQ0csSUFBSTtRQUNwQixJQUFJRyxNQUFNQyxPQUFPLENBQUNILFNBQVNFLE1BQU1DLE9BQU8sQ0FBQ0YsT0FBTztZQUM1Q0osU0FBUyxDQUFDRSxJQUFJLEdBQUdFO1FBQ3JCLE9BQ0ssSUFBSSxPQUFPRCxTQUFTLFlBQVksT0FBT0MsU0FBUyxVQUFVO1lBQzNESixTQUFTLENBQUNFLElBQUksR0FBRyxDQUFDLEdBQUd0QixRQUFRUyxZQUFZLEVBQUVjLE1BQU1DO1FBQ3JELE9BQ0ssSUFBSUEsU0FBU0csV0FBVztZQUN6QlAsU0FBUyxDQUFDRSxJQUFJLEdBQUdFO1FBQ3JCO0lBQ0o7SUFDQSxPQUFPSjtBQUNYO0FBQ0FwQixvQkFBb0IsR0FBR1M7QUFDdkIsK0dBQStHO0FBQy9HLElBQUlELGVBQWUsU0FBVW9CLE9BQU8sRUFBRUMsS0FBSztJQUN2QyxPQUFPRCxVQUFXRSxDQUFBQSxLQUFLQyxFQUFFLEdBQUdGLEtBQUk7QUFDcEM7QUFDQTdCLG9CQUFvQixHQUFHUTtBQUN2QixJQUFJRCxpQkFBaUIsU0FBVU4sS0FBSyxFQUFFK0IsU0FBUztJQUMzQyxJQUFJQSxjQUFjLEtBQUssR0FBRztRQUFFQSxZQUFZO0lBQUc7SUFDM0MsT0FBT0YsS0FBS0csS0FBSyxDQUFDaEMsUUFBUTZCLEtBQUtJLEdBQUcsQ0FBQyxJQUFJRixjQUFjRixLQUFLSSxHQUFHLENBQUMsSUFBSUY7QUFDdEU7QUFDQWhDLHNCQUFzQixHQUFHTztBQUN6QiwwREFBMEQ7QUFDMUQsU0FBU0QsVUFBVUwsS0FBSyxFQUFFa0MsR0FBRyxFQUFFQyxHQUFHO0lBQzlCLE9BQU8sQ0FBRW5DLFFBQVFrQyxHQUFFLElBQU1DLENBQUFBLE1BQU1ELEdBQUUsSUFBTTtBQUMzQztBQUNBbkMsaUJBQWlCLEdBQUdNO0FBQ3BCLElBQUlELFdBQVcsU0FBVWdDLE9BQU87SUFDNUIsT0FBT0EsVUFBV1AsQ0FBQUEsS0FBS0MsRUFBRSxHQUFHLEdBQUU7QUFDbEM7QUFDQS9CLGdCQUFnQixHQUFHSztBQUNuQixJQUFJRCxtQ0FBbUMsU0FBVUgsS0FBSyxFQUFFcUMsS0FBSztJQUFJLE9BQU8sQ0FBQyxHQUFHdEMsUUFBUVcsbUJBQW1CLEVBQUUyQixNQUFNMUIsUUFBUSxFQUFFMEIsTUFBTXpCLFFBQVEsRUFBRVo7QUFBUTtBQUNqSkQsd0NBQXdDLEdBQUdJO0FBQzNDLElBQUlELG1DQUFtQyxTQUFVVyxVQUFVLEVBQUV3QixLQUFLO0lBQzlELElBQUkxQixXQUFXMEIsTUFBTUMsS0FBSyxDQUFDM0IsUUFBUTtJQUNuQyxJQUFJQyxXQUFXeUIsTUFBTUMsS0FBSyxDQUFDMUIsUUFBUTtJQUNuQyxJQUFJWixRQUFRVyxXQUFXLGFBQWdCQyxDQUFBQSxXQUFXRCxRQUFPO0lBQ3pELE9BQU9YO0FBQ1g7QUFDQUQsd0NBQXdDLEdBQUdHO0FBQzNDLElBQUlELHVCQUF1QixTQUFVc0MsR0FBRztJQUFJLE9BQU9BLElBQUlDLE9BQU8sQ0FBQyxVQUFVLFNBQVVDLE1BQU07UUFBSSxPQUFPLElBQUlDLE1BQU0sQ0FBQ0QsT0FBT0UsV0FBVztJQUFLO0FBQUk7QUFDMUk1Qyw0QkFBNEIsR0FBR0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9yZWFjdC1nYXVnZS1jb21wb25lbnQvZGlzdC9saWIvR2F1Z2VDb21wb25lbnQvaG9va3MvdXRpbHMuanM/MWZhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2Fzc2lnbiA9ICh0aGlzICYmIHRoaXMuX19hc3NpZ24pIHx8IGZ1bmN0aW9uICgpIHtcbiAgICBfX2Fzc2lnbiA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24odCkge1xuICAgICAgICBmb3IgKHZhciBzLCBpID0gMSwgbiA9IGFyZ3VtZW50cy5sZW5ndGg7IGkgPCBuOyBpKyspIHtcbiAgICAgICAgICAgIHMgPSBhcmd1bWVudHNbaV07XG4gICAgICAgICAgICBmb3IgKHZhciBwIGluIHMpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgcCkpXG4gICAgICAgICAgICAgICAgdFtwXSA9IHNbcF07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHQ7XG4gICAgfTtcbiAgICByZXR1cm4gX19hc3NpZ24uYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmNhbWVsQ2FzZVRvS2ViYWJDYXNlID0gZXhwb3J0cy5nZXRDdXJyZW50R2F1Z2VWYWx1ZUJ5UGVyY2VudGFnZSA9IGV4cG9ydHMuZ2V0Q3VycmVudEdhdWdlUGVyY2VudGFnZUJ5VmFsdWUgPSBleHBvcnRzLmRlZ1RvUmFkID0gZXhwb3J0cy5ub3JtYWxpemUgPSBleHBvcnRzLmZsb2F0aW5nTnVtYmVyID0gZXhwb3J0cy5wZXJjZW50VG9SYWQgPSBleHBvcnRzLm1lcmdlT2JqZWN0cyA9IGV4cG9ydHMuaXNFbXB0eU9iamVjdCA9IGV4cG9ydHMuY2FsY3VsYXRlUGVyY2VudGFnZSA9IHZvaWQgMDtcbnZhciBjYWxjdWxhdGVQZXJjZW50YWdlID0gZnVuY3Rpb24gKG1pblZhbHVlLCBtYXhWYWx1ZSwgdmFsdWUpIHtcbiAgICBpZiAodmFsdWUgPCBtaW5WYWx1ZSkge1xuICAgICAgICByZXR1cm4gMDtcbiAgICB9XG4gICAgZWxzZSBpZiAodmFsdWUgPiBtYXhWYWx1ZSkge1xuICAgICAgICByZXR1cm4gMTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHZhciBwZXJjZW50YWdlID0gKHZhbHVlIC0gbWluVmFsdWUpIC8gKG1heFZhbHVlIC0gbWluVmFsdWUpO1xuICAgICAgICByZXR1cm4gKHBlcmNlbnRhZ2UpO1xuICAgIH1cbn07XG5leHBvcnRzLmNhbGN1bGF0ZVBlcmNlbnRhZ2UgPSBjYWxjdWxhdGVQZXJjZW50YWdlO1xudmFyIGlzRW1wdHlPYmplY3QgPSBmdW5jdGlvbiAob2JqKSB7XG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKG9iaikubGVuZ3RoID09PSAwICYmIG9iai5jb25zdHJ1Y3RvciA9PT0gT2JqZWN0O1xufTtcbmV4cG9ydHMuaXNFbXB0eU9iamVjdCA9IGlzRW1wdHlPYmplY3Q7XG52YXIgbWVyZ2VPYmplY3RzID0gZnVuY3Rpb24gKG9iajEsIG9iajIpIHtcbiAgICB2YXIgbWVyZ2VkT2JqID0gX19hc3NpZ24oe30sIG9iajEpO1xuICAgIE9iamVjdC5rZXlzKG9iajIpLmZvckVhY2goZnVuY3Rpb24gKGtleSkge1xuICAgICAgICB2YXIgdmFsMSA9IG9iajFba2V5XTtcbiAgICAgICAgdmFyIHZhbDIgPSBvYmoyW2tleV07XG4gICAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbDEpICYmIEFycmF5LmlzQXJyYXkodmFsMikpIHtcbiAgICAgICAgICAgIG1lcmdlZE9ialtrZXldID0gdmFsMjtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICh0eXBlb2YgdmFsMSA9PT0gJ29iamVjdCcgJiYgdHlwZW9mIHZhbDIgPT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICBtZXJnZWRPYmpba2V5XSA9ICgwLCBleHBvcnRzLm1lcmdlT2JqZWN0cykodmFsMSwgdmFsMik7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodmFsMiAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBtZXJnZWRPYmpba2V5XSA9IHZhbDI7XG4gICAgICAgIH1cbiAgICB9KTtcbiAgICByZXR1cm4gbWVyZ2VkT2JqO1xufTtcbmV4cG9ydHMubWVyZ2VPYmplY3RzID0gbWVyZ2VPYmplY3RzO1xuLy9SZXR1cm5zIHRoZSBhbmdsZSAoaW4gcmFkKSBmb3IgdGhlIGdpdmVuICdwZXJjZW50JyB2YWx1ZSB3aGVyZSBwZXJjZW50ID0gMSBtZWFucyAxMDAlIGFuZCBpcyAxODAgZGVncmVlIGFuZ2xlXG52YXIgcGVyY2VudFRvUmFkID0gZnVuY3Rpb24gKHBlcmNlbnQsIGFuZ2xlKSB7XG4gICAgcmV0dXJuIHBlcmNlbnQgKiAoTWF0aC5QSSAvIGFuZ2xlKTtcbn07XG5leHBvcnRzLnBlcmNlbnRUb1JhZCA9IHBlcmNlbnRUb1JhZDtcbnZhciBmbG9hdGluZ051bWJlciA9IGZ1bmN0aW9uICh2YWx1ZSwgbWF4RGlnaXRzKSB7XG4gICAgaWYgKG1heERpZ2l0cyA9PT0gdm9pZCAwKSB7IG1heERpZ2l0cyA9IDI7IH1cbiAgICByZXR1cm4gTWF0aC5yb3VuZCh2YWx1ZSAqIE1hdGgucG93KDEwLCBtYXhEaWdpdHMpKSAvIE1hdGgucG93KDEwLCBtYXhEaWdpdHMpO1xufTtcbmV4cG9ydHMuZmxvYXRpbmdOdW1iZXIgPSBmbG9hdGluZ051bWJlcjtcbi8vIEZ1bmN0aW9uIHRvIG5vcm1hbGl6ZSBhIHZhbHVlIGJldHdlZW4gYSBuZXcgbWluIGFuZCBtYXhcbmZ1bmN0aW9uIG5vcm1hbGl6ZSh2YWx1ZSwgbWluLCBtYXgpIHtcbiAgICByZXR1cm4gKCh2YWx1ZSAtIG1pbikgLyAobWF4IC0gbWluKSkgKiAxMDA7XG59XG5leHBvcnRzLm5vcm1hbGl6ZSA9IG5vcm1hbGl6ZTtcbnZhciBkZWdUb1JhZCA9IGZ1bmN0aW9uIChkZWdyZWVzKSB7XG4gICAgcmV0dXJuIGRlZ3JlZXMgKiAoTWF0aC5QSSAvIDE4MCk7XG59O1xuZXhwb3J0cy5kZWdUb1JhZCA9IGRlZ1RvUmFkO1xudmFyIGdldEN1cnJlbnRHYXVnZVBlcmNlbnRhZ2VCeVZhbHVlID0gZnVuY3Rpb24gKHZhbHVlLCBnYXVnZSkgeyByZXR1cm4gKDAsIGV4cG9ydHMuY2FsY3VsYXRlUGVyY2VudGFnZSkoZ2F1Z2UubWluVmFsdWUsIGdhdWdlLm1heFZhbHVlLCB2YWx1ZSk7IH07XG5leHBvcnRzLmdldEN1cnJlbnRHYXVnZVBlcmNlbnRhZ2VCeVZhbHVlID0gZ2V0Q3VycmVudEdhdWdlUGVyY2VudGFnZUJ5VmFsdWU7XG52YXIgZ2V0Q3VycmVudEdhdWdlVmFsdWVCeVBlcmNlbnRhZ2UgPSBmdW5jdGlvbiAocGVyY2VudGFnZSwgZ2F1Z2UpIHtcbiAgICB2YXIgbWluVmFsdWUgPSBnYXVnZS5wcm9wcy5taW5WYWx1ZTtcbiAgICB2YXIgbWF4VmFsdWUgPSBnYXVnZS5wcm9wcy5tYXhWYWx1ZTtcbiAgICB2YXIgdmFsdWUgPSBtaW5WYWx1ZSArIChwZXJjZW50YWdlKSAqIChtYXhWYWx1ZSAtIG1pblZhbHVlKTtcbiAgICByZXR1cm4gdmFsdWU7XG59O1xuZXhwb3J0cy5nZXRDdXJyZW50R2F1Z2VWYWx1ZUJ5UGVyY2VudGFnZSA9IGdldEN1cnJlbnRHYXVnZVZhbHVlQnlQZXJjZW50YWdlO1xudmFyIGNhbWVsQ2FzZVRvS2ViYWJDYXNlID0gZnVuY3Rpb24gKHN0cikgeyByZXR1cm4gc3RyLnJlcGxhY2UoL1tBLVpdL2csIGZ1bmN0aW9uIChsZXR0ZXIpIHsgcmV0dXJuIFwiLVwiLmNvbmNhdChsZXR0ZXIudG9Mb3dlckNhc2UoKSk7IH0pOyB9O1xuZXhwb3J0cy5jYW1lbENhc2VUb0tlYmFiQ2FzZSA9IGNhbWVsQ2FzZVRvS2ViYWJDYXNlO1xuIl0sIm5hbWVzIjpbIl9fYXNzaWduIiwiT2JqZWN0IiwiYXNzaWduIiwidCIsInMiLCJpIiwibiIsImFyZ3VtZW50cyIsImxlbmd0aCIsInAiLCJwcm90b3R5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJhcHBseSIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiY2FtZWxDYXNlVG9LZWJhYkNhc2UiLCJnZXRDdXJyZW50R2F1Z2VWYWx1ZUJ5UGVyY2VudGFnZSIsImdldEN1cnJlbnRHYXVnZVBlcmNlbnRhZ2VCeVZhbHVlIiwiZGVnVG9SYWQiLCJub3JtYWxpemUiLCJmbG9hdGluZ051bWJlciIsInBlcmNlbnRUb1JhZCIsIm1lcmdlT2JqZWN0cyIsImlzRW1wdHlPYmplY3QiLCJjYWxjdWxhdGVQZXJjZW50YWdlIiwibWluVmFsdWUiLCJtYXhWYWx1ZSIsInBlcmNlbnRhZ2UiLCJvYmoiLCJrZXlzIiwiY29uc3RydWN0b3IiLCJvYmoxIiwib2JqMiIsIm1lcmdlZE9iaiIsImZvckVhY2giLCJrZXkiLCJ2YWwxIiwidmFsMiIsIkFycmF5IiwiaXNBcnJheSIsInVuZGVmaW5lZCIsInBlcmNlbnQiLCJhbmdsZSIsIk1hdGgiLCJQSSIsIm1heERpZ2l0cyIsInJvdW5kIiwicG93IiwibWluIiwibWF4IiwiZGVncmVlcyIsImdhdWdlIiwicHJvcHMiLCJzdHIiLCJyZXBsYWNlIiwibGV0dGVyIiwiY29uY2F0IiwidG9Mb3dlckNhc2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/index.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __assign = (void 0) && (void 0).__assign || function() {\n    __assign = Object.assign || function(t) {\n        for(var s, i = 1, n = arguments.length; i < n; i++){\n            s = arguments[i];\n            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GaugeComponent = void 0;\nvar react_1 = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar d3_1 = __webpack_require__(/*! d3 */ \"(ssr)/./node_modules/d3/src/index.js\");\nvar GaugeComponentProps_1 = __webpack_require__(/*! ./types/GaugeComponentProps */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nvar chartHooks = __importStar(__webpack_require__(/*! ./hooks/chart */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/chart.js\"));\nvar arcHooks = __importStar(__webpack_require__(/*! ./hooks/arc */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\"));\nvar utils_1 = __webpack_require__(/*! ./hooks/utils */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js\");\nvar Dimensions_1 = __webpack_require__(/*! ./types/Dimensions */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Dimensions.js\");\nvar Pointer_1 = __webpack_require__(/*! ./types/Pointer */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js\");\nvar Arc_1 = __webpack_require__(/*! ./types/Arc */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Arc.js\");\n/*\nGaugeComponent creates a gauge chart using D3\nThe chart is responsive and will have the same width as the \"container\"\nThe radius of the gauge depends on the width and height of the container\nIt will use whichever is smallest of width or height\nThe svg element surrounding the gauge will always be square\n\"container\" is the div where the chart should be placed\n*/ var GaugeComponent = function(props) {\n    var svg = (0, react_1.useRef)({});\n    var tooltip = (0, react_1.useRef)({});\n    var g = (0, react_1.useRef)({});\n    var doughnut = (0, react_1.useRef)({});\n    var isFirstRun = (0, react_1.useRef)(true);\n    var currentProgress = (0, react_1.useRef)(0);\n    var pointer = (0, react_1.useRef)(__assign({}, Pointer_1.defaultPointerRef));\n    var container = (0, react_1.useRef)({});\n    var arcData = (0, react_1.useRef)([]);\n    var pieChart = (0, react_1.useRef)((0, d3_1.pie)());\n    var dimensions = (0, react_1.useRef)(__assign({}, Dimensions_1.defaultDimensions));\n    var mergedProps = (0, react_1.useRef)(props);\n    var prevProps = (0, react_1.useRef)({});\n    var resizeObserver = (0, react_1.useRef)({});\n    var selectedRef = (0, react_1.useRef)(null);\n    var gauge = {\n        props: mergedProps.current,\n        resizeObserver: resizeObserver,\n        prevProps: prevProps,\n        svg: svg,\n        g: g,\n        dimensions: dimensions,\n        doughnut: doughnut,\n        isFirstRun: isFirstRun,\n        currentProgress: currentProgress,\n        pointer: pointer,\n        container: container,\n        arcData: arcData,\n        pieChart: pieChart,\n        tooltip: tooltip\n    };\n    //Merged properties will get the default props and overwrite by the user's defined props\n    //To keep the original default props in the object\n    var updateMergedProps = function() {\n        var _a, _b;\n        var defaultValues = __assign({}, GaugeComponentProps_1.defaultGaugeProps);\n        gauge.props = mergedProps.current = (0, utils_1.mergeObjects)(defaultValues, props);\n        if (((_a = gauge.props.arc) === null || _a === void 0 ? void 0 : _a.width) == ((_b = GaugeComponentProps_1.defaultGaugeProps.arc) === null || _b === void 0 ? void 0 : _b.width)) {\n            var mergedArc = mergedProps.current.arc;\n            mergedArc.width = (0, Arc_1.getArcWidthByType)(gauge.props.type);\n        }\n        if (gauge.props.marginInPercent == GaugeComponentProps_1.defaultGaugeProps.marginInPercent) mergedProps.current.marginInPercent = (0, GaugeComponentProps_1.getGaugeMarginByType)(gauge.props.type);\n        arcHooks.validateArcs(gauge);\n    };\n    var shouldInitChart = function() {\n        var arcsPropsChanged = JSON.stringify(prevProps.current.arc) !== JSON.stringify(mergedProps.current.arc);\n        var pointerPropsChanged = JSON.stringify(prevProps.current.pointer) !== JSON.stringify(mergedProps.current.pointer);\n        var valueChanged = JSON.stringify(prevProps.current.value) !== JSON.stringify(mergedProps.current.value);\n        var minValueChanged = JSON.stringify(prevProps.current.minValue) !== JSON.stringify(mergedProps.current.minValue);\n        var maxValueChanged = JSON.stringify(prevProps.current.maxValue) !== JSON.stringify(mergedProps.current.maxValue);\n        return arcsPropsChanged || pointerPropsChanged || valueChanged || minValueChanged || maxValueChanged;\n    };\n    (0, react_1.useLayoutEffect)(function() {\n        updateMergedProps();\n        isFirstRun.current = (0, utils_1.isEmptyObject)(container.current);\n        if (isFirstRun.current) container.current = (0, d3_1.select)(selectedRef.current);\n        if (shouldInitChart()) chartHooks.initChart(gauge, isFirstRun.current);\n        gauge.prevProps.current = mergedProps.current;\n    }, [\n        props\n    ]);\n    // useEffect(() => {\n    //   const observer = new MutationObserver(function () {\n    //     setTimeout(() => window.dispatchEvent(new Event('resize')), 10);\n    //     if (!selectedRef.current?.offsetParent) return;\n    //     chartHooks.renderChart(gauge, true);\n    //     observer.disconnect()\n    //   });\n    //   observer.observe(selectedRef.current?.parentNode, {attributes: true, subtree: false});\n    //   return () => observer.disconnect();\n    // }, [selectedRef.current?.parentNode?.offsetWidth, selectedRef.current?.parentNode?.offsetHeight]);\n    (0, react_1.useEffect)(function() {\n        var handleResize = function() {\n            return chartHooks.renderChart(gauge, true);\n        };\n        //Set up resize event listener to re-render the chart everytime the window is resized\n        window.addEventListener(\"resize\", handleResize);\n        return function() {\n            return window.removeEventListener(\"resize\", handleResize);\n        };\n    }, [\n        props\n    ]);\n    // useEffect(() => {\n    //   console.log(selectedRef.current?.offsetWidth)\n    //   // workaround to trigger recomputing of gauge size on first load (e.g. F5)\n    //   setTimeout(() => window.dispatchEvent(new Event('resize')), 10);\n    // }, [selectedRef.current?.parentNode]);\n    (0, react_1.useEffect)(function() {\n        var element = selectedRef.current;\n        if (!element) return;\n        // Create observer instance\n        var observer = new ResizeObserver(function() {\n            chartHooks.renderChart(gauge, true);\n        });\n        // Store observer reference\n        gauge.resizeObserver.current = observer;\n        // Observe parent node\n        if (element.parentNode) {\n            observer.observe(element.parentNode);\n        }\n        // Cleanup\n        return function() {\n            var _a;\n            if (gauge.resizeObserver) {\n                (_a = gauge.resizeObserver.current) === null || _a === void 0 ? void 0 : _a.disconnect();\n                delete gauge.resizeObserver.current;\n            }\n        };\n    }, []);\n    var id = props.id, style = props.style, className = props.className, type = props.type;\n    return react_1.default.createElement(\"div\", {\n        id: id,\n        className: \"\".concat(gauge.props.type, \"-gauge\").concat(className ? \" \" + className : \"\"),\n        style: style,\n        ref: function(svg) {\n            return selectedRef.current = svg;\n        }\n    });\n};\nexports.GaugeComponent = GaugeComponent;\nexports[\"default\"] = GaugeComponent;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Arc.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Arc.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultArc = exports.getArcWidthByType = exports.defaultSubArcs = void 0;\nvar GaugeComponentProps_1 = __webpack_require__(/*! ./GaugeComponentProps */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nexports.defaultSubArcs = [\n    {\n        limit: 33,\n        color: \"#5BE12C\"\n    },\n    {\n        limit: 66,\n        color: \"#F5CD19\"\n    },\n    {\n        color: \"#EA4228\"\n    }\n];\nvar getArcWidthByType = function(type) {\n    var _a;\n    var gaugeTypesWidth = (_a = {}, _a[GaugeComponentProps_1.GaugeType.Grafana] = 0.25, _a[GaugeComponentProps_1.GaugeType.Semicircle] = 0.15, _a[GaugeComponentProps_1.GaugeType.Radial] = 0.2, _a);\n    if (!type) type = GaugeComponentProps_1.defaultGaugeProps.type;\n    return gaugeTypesWidth[type];\n};\nexports.getArcWidthByType = getArcWidthByType;\nexports.defaultArc = {\n    padding: 0.05,\n    width: 0.25,\n    cornerRadius: 7,\n    nbSubArcs: undefined,\n    emptyColor: \"#5C5C5C\",\n    colorArray: undefined,\n    subArcs: exports.defaultSubArcs,\n    gradient: false\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Arc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Dimensions.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Dimensions.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultDimensions = exports.defaultAngles = exports.defaultMargins = void 0;\nexports.defaultMargins = {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n};\nexports.defaultAngles = {\n    startAngle: 0,\n    endAngle: 0,\n    startAngleDeg: 0,\n    endAngleDeg: 0\n};\nexports.defaultDimensions = {\n    width: 0,\n    height: 0,\n    margin: exports.defaultMargins,\n    outerRadius: 0,\n    innerRadius: 0,\n    angles: exports.defaultAngles,\n    fixedHeight: 0\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ2F1Z2UtY29tcG9uZW50L2Rpc3QvbGliL0dhdWdlQ29tcG9uZW50L3R5cGVzL0RpbWVuc2lvbnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFDN0RELHlCQUF5QixHQUFHQSxxQkFBcUIsR0FBR0Esc0JBQXNCLEdBQUcsS0FBSztBQUNsRkEsc0JBQXNCLEdBQUc7SUFDckJLLEtBQUs7SUFDTEMsT0FBTztJQUNQQyxRQUFRO0lBQ1JDLE1BQU07QUFDVjtBQUNBUixxQkFBcUIsR0FBRztJQUNwQlMsWUFBWTtJQUNaQyxVQUFVO0lBQ1ZDLGVBQWU7SUFDZkMsYUFBYTtBQUNqQjtBQUNBWix5QkFBeUIsR0FBRztJQUN4QmEsT0FBTztJQUNQQyxRQUFRO0lBQ1JDLFFBQVFmLFFBQVFJLGNBQWM7SUFDOUJZLGFBQWE7SUFDYkMsYUFBYTtJQUNiQyxRQUFRbEIsUUFBUUcsYUFBYTtJQUM3QmdCLGFBQWE7QUFDakIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9yZWFjdC1nYXVnZS1jb21wb25lbnQvZGlzdC9saWIvR2F1Z2VDb21wb25lbnQvdHlwZXMvRGltZW5zaW9ucy5qcz9mNzNlIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5kZWZhdWx0RGltZW5zaW9ucyA9IGV4cG9ydHMuZGVmYXVsdEFuZ2xlcyA9IGV4cG9ydHMuZGVmYXVsdE1hcmdpbnMgPSB2b2lkIDA7XG5leHBvcnRzLmRlZmF1bHRNYXJnaW5zID0ge1xuICAgIHRvcDogMCxcbiAgICByaWdodDogMCxcbiAgICBib3R0b206IDAsXG4gICAgbGVmdDogMFxufTtcbmV4cG9ydHMuZGVmYXVsdEFuZ2xlcyA9IHtcbiAgICBzdGFydEFuZ2xlOiAwLFxuICAgIGVuZEFuZ2xlOiAwLFxuICAgIHN0YXJ0QW5nbGVEZWc6IDAsXG4gICAgZW5kQW5nbGVEZWc6IDBcbn07XG5leHBvcnRzLmRlZmF1bHREaW1lbnNpb25zID0ge1xuICAgIHdpZHRoOiAwLFxuICAgIGhlaWdodDogMCxcbiAgICBtYXJnaW46IGV4cG9ydHMuZGVmYXVsdE1hcmdpbnMsXG4gICAgb3V0ZXJSYWRpdXM6IDAsXG4gICAgaW5uZXJSYWRpdXM6IDAsXG4gICAgYW5nbGVzOiBleHBvcnRzLmRlZmF1bHRBbmdsZXMsXG4gICAgZml4ZWRIZWlnaHQ6IDBcbn07XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJkZWZhdWx0RGltZW5zaW9ucyIsImRlZmF1bHRBbmdsZXMiLCJkZWZhdWx0TWFyZ2lucyIsInRvcCIsInJpZ2h0IiwiYm90dG9tIiwibGVmdCIsInN0YXJ0QW5nbGUiLCJlbmRBbmdsZSIsInN0YXJ0QW5nbGVEZWciLCJlbmRBbmdsZURlZyIsIndpZHRoIiwiaGVpZ2h0IiwibWFyZ2luIiwib3V0ZXJSYWRpdXMiLCJpbm5lclJhZGl1cyIsImFuZ2xlcyIsImZpeGVkSGVpZ2h0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Dimensions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.getGaugeMarginByType = exports.defaultGaugeProps = exports.GaugeType = void 0;\nvar Arc_1 = __webpack_require__(/*! ./Arc */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Arc.js\");\nvar Labels_1 = __webpack_require__(/*! ./Labels */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Labels.js\");\nvar Pointer_1 = __webpack_require__(/*! ./Pointer */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js\");\nvar GaugeType;\n(function(GaugeType) {\n    GaugeType[\"Semicircle\"] = \"semicircle\";\n    GaugeType[\"Radial\"] = \"radial\";\n    GaugeType[\"Grafana\"] = \"grafana\";\n})(GaugeType || (exports.GaugeType = GaugeType = {}));\nexports.defaultGaugeProps = {\n    id: \"\",\n    className: \"gauge-component-class\",\n    style: {\n        width: \"100%\"\n    },\n    marginInPercent: 0.07,\n    value: 33,\n    minValue: 0,\n    maxValue: 100,\n    arc: Arc_1.defaultArc,\n    labels: Labels_1.defaultLabels,\n    pointer: Pointer_1.defaultPointer,\n    type: GaugeType.Grafana\n};\nvar getGaugeMarginByType = function(type) {\n    var _a;\n    var gaugeTypesMargin = (_a = {}, _a[GaugeType.Grafana] = {\n        top: 0.12,\n        bottom: 0.00,\n        left: 0.07,\n        right: 0.07\n    }, _a[GaugeType.Semicircle] = {\n        top: 0.08,\n        bottom: 0.00,\n        left: 0.08,\n        right: 0.08\n    }, _a[GaugeType.Radial] = {\n        top: 0.07,\n        bottom: 0.00,\n        left: 0.07,\n        right: 0.07\n    }, _a);\n    return gaugeTypesMargin[type];\n};\nexports.getGaugeMarginByType = getGaugeMarginByType;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Labels.js":
/*!************************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Labels.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultLabels = exports.defaultValueLabel = void 0;\nvar Tick_1 = __webpack_require__(/*! ./Tick */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tick.js\");\nexports.defaultValueLabel = {\n    formatTextValue: undefined,\n    matchColorWithArc: false,\n    maxDecimalDigits: 2,\n    style: {\n        fontSize: \"35px\",\n        fill: \"#fff\",\n        textShadow: \"black 1px 0.5px 0px, black 0px 0px 0.03em, black 0px 0px 0.01em\"\n    },\n    hide: false\n};\nexports.defaultLabels = {\n    valueLabel: exports.defaultValueLabel,\n    tickLabels: Tick_1.defaultTickLabels\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ2F1Z2UtY29tcG9uZW50L2Rpc3QvbGliL0dhdWdlQ29tcG9uZW50L3R5cGVzL0xhYmVscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUM3REQscUJBQXFCLEdBQUdBLHlCQUF5QixHQUFHLEtBQUs7QUFDekQsSUFBSUksU0FBU0MsbUJBQU9BLENBQUMsZ0dBQVE7QUFDN0JMLHlCQUF5QixHQUFHO0lBQ3hCTSxpQkFBaUJDO0lBQ2pCQyxtQkFBbUI7SUFDbkJDLGtCQUFrQjtJQUNsQkMsT0FBTztRQUNIQyxVQUFVO1FBQ1ZDLE1BQU07UUFDTkMsWUFBWTtJQUNoQjtJQUNBQyxNQUFNO0FBQ1Y7QUFDQWQscUJBQXFCLEdBQUc7SUFDcEJlLFlBQVlmLFFBQVFHLGlCQUFpQjtJQUNyQ2EsWUFBWVosT0FBT2EsaUJBQWlCO0FBQ3hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ2F1Z2UtY29tcG9uZW50L2Rpc3QvbGliL0dhdWdlQ29tcG9uZW50L3R5cGVzL0xhYmVscy5qcz83ZDgzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5kZWZhdWx0TGFiZWxzID0gZXhwb3J0cy5kZWZhdWx0VmFsdWVMYWJlbCA9IHZvaWQgMDtcbnZhciBUaWNrXzEgPSByZXF1aXJlKFwiLi9UaWNrXCIpO1xuZXhwb3J0cy5kZWZhdWx0VmFsdWVMYWJlbCA9IHtcbiAgICBmb3JtYXRUZXh0VmFsdWU6IHVuZGVmaW5lZCxcbiAgICBtYXRjaENvbG9yV2l0aEFyYzogZmFsc2UsXG4gICAgbWF4RGVjaW1hbERpZ2l0czogMixcbiAgICBzdHlsZToge1xuICAgICAgICBmb250U2l6ZTogXCIzNXB4XCIsXG4gICAgICAgIGZpbGw6ICcjZmZmJyxcbiAgICAgICAgdGV4dFNoYWRvdzogXCJibGFjayAxcHggMC41cHggMHB4LCBibGFjayAwcHggMHB4IDAuMDNlbSwgYmxhY2sgMHB4IDBweCAwLjAxZW1cIlxuICAgIH0sXG4gICAgaGlkZTogZmFsc2Vcbn07XG5leHBvcnRzLmRlZmF1bHRMYWJlbHMgPSB7XG4gICAgdmFsdWVMYWJlbDogZXhwb3J0cy5kZWZhdWx0VmFsdWVMYWJlbCxcbiAgICB0aWNrTGFiZWxzOiBUaWNrXzEuZGVmYXVsdFRpY2tMYWJlbHNcbn07XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJkZWZhdWx0TGFiZWxzIiwiZGVmYXVsdFZhbHVlTGFiZWwiLCJUaWNrXzEiLCJyZXF1aXJlIiwiZm9ybWF0VGV4dFZhbHVlIiwidW5kZWZpbmVkIiwibWF0Y2hDb2xvcldpdGhBcmMiLCJtYXhEZWNpbWFsRGlnaXRzIiwic3R5bGUiLCJmb250U2l6ZSIsImZpbGwiLCJ0ZXh0U2hhZG93IiwiaGlkZSIsInZhbHVlTGFiZWwiLCJ0aWNrTGFiZWxzIiwiZGVmYXVsdFRpY2tMYWJlbHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Labels.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultPointer = exports.defaultPointerRef = exports.defaultPointerContext = exports.PointerType = void 0;\nvar PointerType;\n(function(PointerType) {\n    PointerType[\"Needle\"] = \"needle\";\n    PointerType[\"Blob\"] = \"blob\";\n    PointerType[\"Arrow\"] = \"arrow\";\n})(PointerType || (exports.PointerType = PointerType = {}));\nexports.defaultPointerContext = {\n    centerPoint: [\n        0,\n        0\n    ],\n    pointerRadius: 0,\n    pathLength: 0,\n    currentPercent: 0,\n    prevPercent: 0,\n    prevProgress: 0,\n    pathStr: \"\",\n    shouldDrawPath: false,\n    prevColor: \"\"\n};\nexports.defaultPointerRef = {\n    element: undefined,\n    path: undefined,\n    context: exports.defaultPointerContext\n};\nexports.defaultPointer = {\n    type: PointerType.Needle,\n    color: \"#5A5A5A\",\n    baseColor: \"white\",\n    length: 0.70,\n    width: 20,\n    animate: true,\n    elastic: false,\n    hide: false,\n    animationDuration: 3000,\n    animationDelay: 100,\n    strokeWidth: 8\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tick.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tick.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultTickLabels = void 0;\nvar defaultTickLineConfig = {\n    color: \"rgb(173 172 171)\",\n    length: 7,\n    width: 1,\n    distanceFromArc: 3,\n    hide: false\n};\nvar defaultTickValueConfig = {\n    formatTextValue: undefined,\n    maxDecimalDigits: 2,\n    style: {\n        fontSize: \"10px\",\n        fill: \"rgb(173 172 171)\"\n    },\n    hide: false\n};\nvar defaultTickList = [];\nexports.defaultTickLabels = {\n    type: \"outer\",\n    hideMinMax: false,\n    ticks: defaultTickList,\n    defaultTickValueConfig: defaultTickValueConfig,\n    defaultTickLineConfig: defaultTickLineConfig\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tick.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tooltip.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tooltip.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultTooltipStyle = void 0;\nexports.defaultTooltipStyle = {\n    borderColor: \"#5A5A5A\",\n    borderStyle: \"solid\",\n    borderWidth: \"1px\",\n    borderRadius: \"5px\",\n    color: \"white\",\n    padding: \"5px\",\n    fontSize: \"15px\",\n    textShadow: \"1px 1px 2px black, 0 0 1em black, 0 0 0.2em black\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ2F1Z2UtY29tcG9uZW50L2Rpc3QvbGliL0dhdWdlQ29tcG9uZW50L3R5cGVzL1Rvb2x0aXAuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFDN0RELDJCQUEyQixHQUFHLEtBQUs7QUFDbkNBLDJCQUEyQixHQUFHO0lBQzFCRyxhQUFhO0lBQ2JDLGFBQWE7SUFDYkMsYUFBYTtJQUNiQyxjQUFjO0lBQ2RDLE9BQU87SUFDUEMsU0FBUztJQUNUQyxVQUFVO0lBQ1ZDLFlBQVk7QUFFaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9yZWFjdC1nYXVnZS1jb21wb25lbnQvZGlzdC9saWIvR2F1Z2VDb21wb25lbnQvdHlwZXMvVG9vbHRpcC5qcz8zN2ZhIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5kZWZhdWx0VG9vbHRpcFN0eWxlID0gdm9pZCAwO1xuZXhwb3J0cy5kZWZhdWx0VG9vbHRpcFN0eWxlID0ge1xuICAgIGJvcmRlckNvbG9yOiAnIzVBNUE1QScsXG4gICAgYm9yZGVyU3R5bGU6ICdzb2xpZCcsXG4gICAgYm9yZGVyV2lkdGg6ICcxcHgnLFxuICAgIGJvcmRlclJhZGl1czogJzVweCcsXG4gICAgY29sb3I6ICd3aGl0ZScsXG4gICAgcGFkZGluZzogJzVweCcsXG4gICAgZm9udFNpemU6ICcxNXB4JyxcbiAgICB0ZXh0U2hhZG93OiAnMXB4IDFweCAycHggYmxhY2ssIDAgMCAxZW0gYmxhY2ssIDAgMCAwLjJlbSBibGFjaydcbiAgICAvLyBmb250U2l6ZTogJzE1cHgnXG59O1xuIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZGVmYXVsdFRvb2x0aXBTdHlsZSIsImJvcmRlckNvbG9yIiwiYm9yZGVyU3R5bGUiLCJib3JkZXJXaWR0aCIsImJvcmRlclJhZGl1cyIsImNvbG9yIiwicGFkZGluZyIsImZvbnRTaXplIiwidGV4dFNoYWRvdyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tooltip.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-gauge-component/dist/lib/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-gauge-component/dist/lib/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GaugeComponent = void 0;\nvar GaugeComponent_1 = __importDefault(__webpack_require__(/*! ./GaugeComponent */ \"(ssr)/./node_modules/react-gauge-component/dist/lib/GaugeComponent/index.js\"));\nexports.GaugeComponent = GaugeComponent_1.default;\nexports[\"default\"] = GaugeComponent_1.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ2F1Z2UtY29tcG9uZW50L2Rpc3QvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsSUFBSUEsa0JBQWtCLENBQUMsTUFBRyxLQUFLLE9BQUcsRUFBRUEsZUFBZSxJQUFLLFNBQVVDLEdBQUc7SUFDakUsT0FBTyxPQUFRQSxJQUFJQyxVQUFVLEdBQUlELE1BQU07UUFBRSxXQUFXQTtJQUFJO0FBQzVEO0FBQ0FFLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBQzdERCxzQkFBc0IsR0FBRyxLQUFLO0FBQzlCLElBQUlHLG1CQUFtQlIsZ0JBQWdCUyxtQkFBT0EsQ0FBQyxxR0FBa0I7QUFDakVKLHNCQUFzQixHQUFHRyxpQkFBaUJFLE9BQU87QUFDakRMLGtCQUFlLEdBQUdHLGlCQUFpQkUsT0FBTyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWdhdWdlLWNvbXBvbmVudC9kaXN0L2xpYi9pbmRleC5qcz84ZjJiIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5HYXVnZUNvbXBvbmVudCA9IHZvaWQgMDtcbnZhciBHYXVnZUNvbXBvbmVudF8xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCIuL0dhdWdlQ29tcG9uZW50XCIpKTtcbmV4cG9ydHMuR2F1Z2VDb21wb25lbnQgPSBHYXVnZUNvbXBvbmVudF8xLmRlZmF1bHQ7XG5leHBvcnRzLmRlZmF1bHQgPSBHYXVnZUNvbXBvbmVudF8xLmRlZmF1bHQ7XG4iXSwibmFtZXMiOlsiX19pbXBvcnREZWZhdWx0IiwibW9kIiwiX19lc01vZHVsZSIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiR2F1Z2VDb21wb25lbnQiLCJHYXVnZUNvbXBvbmVudF8xIiwicmVxdWlyZSIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-gauge-component/dist/lib/index.js\n");

/***/ })

};
;