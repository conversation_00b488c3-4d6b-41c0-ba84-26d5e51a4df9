"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-contour";
exports.ids = ["vendor-chunks/d3-contour"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-contour/src/area.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-contour/src/area.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(ring) {\n    var i = 0, n = ring.length, area = ring[n - 1][1] * ring[0][0] - ring[n - 1][0] * ring[0][1];\n    while(++i < n)area += ring[i - 1][1] * ring[i][0] - ring[i - 1][0] * ring[i][1];\n    return area;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLElBQUk7SUFDMUIsSUFBSUMsSUFBSSxHQUFHQyxJQUFJRixLQUFLRyxNQUFNLEVBQUVDLE9BQU9KLElBQUksQ0FBQ0UsSUFBSSxFQUFFLENBQUMsRUFBRSxHQUFHRixJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBR0EsSUFBSSxDQUFDRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLEdBQUdGLElBQUksQ0FBQyxFQUFFLENBQUMsRUFBRTtJQUM1RixNQUFPLEVBQUVDLElBQUlDLEVBQUdFLFFBQVFKLElBQUksQ0FBQ0MsSUFBSSxFQUFFLENBQUMsRUFBRSxHQUFHRCxJQUFJLENBQUNDLEVBQUUsQ0FBQyxFQUFFLEdBQUdELElBQUksQ0FBQ0MsSUFBSSxFQUFFLENBQUMsRUFBRSxHQUFHRCxJQUFJLENBQUNDLEVBQUUsQ0FBQyxFQUFFO0lBQ2pGLE9BQU9HO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1jb250b3VyL3NyYy9hcmVhLmpzPzRjOTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24ocmluZykge1xuICB2YXIgaSA9IDAsIG4gPSByaW5nLmxlbmd0aCwgYXJlYSA9IHJpbmdbbiAtIDFdWzFdICogcmluZ1swXVswXSAtIHJpbmdbbiAtIDFdWzBdICogcmluZ1swXVsxXTtcbiAgd2hpbGUgKCsraSA8IG4pIGFyZWEgKz0gcmluZ1tpIC0gMV1bMV0gKiByaW5nW2ldWzBdIC0gcmluZ1tpIC0gMV1bMF0gKiByaW5nW2ldWzFdO1xuICByZXR1cm4gYXJlYTtcbn1cbiJdLCJuYW1lcyI6WyJyaW5nIiwiaSIsIm4iLCJsZW5ndGgiLCJhcmVhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/area.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/array.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-contour/src/array.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   slice: () => (/* binding */ slice)\n/* harmony export */ });\nvar array = Array.prototype;\nvar slice = array.slice;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXJyYXkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLElBQUlBLFFBQVFDLE1BQU1DLFNBQVM7QUFFcEIsSUFBSUMsUUFBUUgsTUFBTUcsS0FBSyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXJyYXkuanM/N2JmYiJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgYXJyYXkgPSBBcnJheS5wcm90b3R5cGU7XG5cbmV4cG9ydCB2YXIgc2xpY2UgPSBhcnJheS5zbGljZTtcbiJdLCJuYW1lcyI6WyJhcnJheSIsIkFycmF5IiwicHJvdG90eXBlIiwic2xpY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/array.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/ascending.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-contour/src/ascending.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n    return a - b;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXNjZW5kaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBU0EsQ0FBQyxFQUFFQyxDQUFDO0lBQzFCLE9BQU9ELElBQUlDO0FBQ2IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1jb250b3VyL3NyYy9hc2NlbmRpbmcuanM/ZDg5MyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihhLCBiKSB7XG4gIHJldHVybiBhIC0gYjtcbn1cbiJdLCJuYW1lcyI6WyJhIiwiYiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/ascending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/constant.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/constant.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((x)=>()=>x);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvY29uc3RhbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlQSxDQUFBQSxJQUFLLElBQU1BLENBQUFBLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1jb250b3VyL3NyYy9jb25zdGFudC5qcz9mZDljIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHggPT4gKCkgPT4geDtcbiJdLCJuYW1lcyI6WyJ4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/contains.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/contains.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(ring, hole) {\n    var i = -1, n = hole.length, c;\n    while(++i < n)if (c = ringContains(ring, hole[i])) return c;\n    return 0;\n}\nfunction ringContains(ring, point) {\n    var x = point[0], y = point[1], contains = -1;\n    for(var i = 0, n = ring.length, j = n - 1; i < n; j = i++){\n        var pi = ring[i], xi = pi[0], yi = pi[1], pj = ring[j], xj = pj[0], yj = pj[1];\n        if (segmentContains(pi, pj, point)) return 0;\n        if (yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi) contains = -contains;\n    }\n    return contains;\n}\nfunction segmentContains(a, b, c) {\n    var i;\n    return collinear(a, b, c) && within(a[i = +(a[0] === b[0])], c[i], b[i]);\n}\nfunction collinear(a, b, c) {\n    return (b[0] - a[0]) * (c[1] - a[1]) === (c[0] - a[0]) * (b[1] - a[1]);\n}\nfunction within(p, q, r) {\n    return p <= q && q <= r || r <= q && q <= p;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/contains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/contours.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/contours.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/threshold/sturges.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/extent.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-contour/src/array.js\");\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-contour/src/ascending.js\");\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-contour/src/area.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-contour/src/constant.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/./node_modules/d3-contour/src/contains.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/./node_modules/d3-contour/src/noop.js\");\n\n\n\n\n\n\n\nvar cases = [\n    [],\n    [\n        [\n            [\n                1.0,\n                1.5\n            ],\n            [\n                0.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.5,\n                1.0\n            ],\n            [\n                1.0,\n                1.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.5,\n                1.0\n            ],\n            [\n                0.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                0.5\n            ],\n            [\n                1.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                1.5\n            ],\n            [\n                0.5,\n                1.0\n            ]\n        ],\n        [\n            [\n                1.0,\n                0.5\n            ],\n            [\n                1.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                0.5\n            ],\n            [\n                1.0,\n                1.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                0.5\n            ],\n            [\n                0.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                0.5,\n                1.0\n            ],\n            [\n                1.0,\n                0.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                1.5\n            ],\n            [\n                1.0,\n                0.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                0.5,\n                1.0\n            ],\n            [\n                1.0,\n                0.5\n            ]\n        ],\n        [\n            [\n                1.5,\n                1.0\n            ],\n            [\n                1.0,\n                1.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.5,\n                1.0\n            ],\n            [\n                1.0,\n                0.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                0.5,\n                1.0\n            ],\n            [\n                1.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                1.5\n            ],\n            [\n                1.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                0.5,\n                1.0\n            ],\n            [\n                1.0,\n                1.5\n            ]\n        ]\n    ],\n    []\n];\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var dx = 1, dy = 1, threshold = d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"], smooth = smoothLinear;\n    function contours(values) {\n        var tz = threshold(values);\n        // Convert number of thresholds into uniform thresholds.\n        if (!Array.isArray(tz)) {\n            const e = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, finite);\n            tz = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(...(0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(e[0], e[1], tz), tz);\n            while(tz[tz.length - 1] >= e[1])tz.pop();\n            while(tz[1] < e[0])tz.shift();\n        } else {\n            tz = tz.slice().sort(_ascending_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]);\n        }\n        return tz.map((value)=>contour(values, value));\n    }\n    // Accumulate, smooth contour rings, assign holes to exterior rings.\n    // Based on https://github.com/mbostock/shapefile/blob/v0.6.2/shp/polygon.js\n    function contour(values, value) {\n        const v = value == null ? NaN : +value;\n        if (isNaN(v)) throw new Error(`invalid value: ${value}`);\n        var polygons = [], holes = [];\n        isorings(values, v, function(ring) {\n            smooth(ring, values, v);\n            if ((0,_area_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ring) > 0) polygons.push([\n                ring\n            ]);\n            else holes.push(ring);\n        });\n        holes.forEach(function(hole) {\n            for(var i = 0, n = polygons.length, polygon; i < n; ++i){\n                if ((0,_contains_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((polygon = polygons[i])[0], hole) !== -1) {\n                    polygon.push(hole);\n                    return;\n                }\n            }\n        });\n        return {\n            type: \"MultiPolygon\",\n            value: value,\n            coordinates: polygons\n        };\n    }\n    // Marching squares with isolines stitched into rings.\n    // Based on https://github.com/topojson/topojson-client/blob/v3.0.0/src/stitch.js\n    function isorings(values, value, callback) {\n        var fragmentByStart = new Array, fragmentByEnd = new Array, x, y, t0, t1, t2, t3;\n        // Special case for the first row (y = -1, t2 = t3 = 0).\n        x = y = -1;\n        t1 = above(values[0], value);\n        cases[t1 << 1].forEach(stitch);\n        while(++x < dx - 1){\n            t0 = t1, t1 = above(values[x + 1], value);\n            cases[t0 | t1 << 1].forEach(stitch);\n        }\n        cases[t1 << 0].forEach(stitch);\n        // General case for the intermediate rows.\n        while(++y < dy - 1){\n            x = -1;\n            t1 = above(values[y * dx + dx], value);\n            t2 = above(values[y * dx], value);\n            cases[t1 << 1 | t2 << 2].forEach(stitch);\n            while(++x < dx - 1){\n                t0 = t1, t1 = above(values[y * dx + dx + x + 1], value);\n                t3 = t2, t2 = above(values[y * dx + x + 1], value);\n                cases[t0 | t1 << 1 | t2 << 2 | t3 << 3].forEach(stitch);\n            }\n            cases[t1 | t2 << 3].forEach(stitch);\n        }\n        // Special case for the last row (y = dy - 1, t0 = t1 = 0).\n        x = -1;\n        t2 = values[y * dx] >= value;\n        cases[t2 << 2].forEach(stitch);\n        while(++x < dx - 1){\n            t3 = t2, t2 = above(values[y * dx + x + 1], value);\n            cases[t2 << 2 | t3 << 3].forEach(stitch);\n        }\n        cases[t2 << 3].forEach(stitch);\n        function stitch(line) {\n            var start = [\n                line[0][0] + x,\n                line[0][1] + y\n            ], end = [\n                line[1][0] + x,\n                line[1][1] + y\n            ], startIndex = index(start), endIndex = index(end), f, g;\n            if (f = fragmentByEnd[startIndex]) {\n                if (g = fragmentByStart[endIndex]) {\n                    delete fragmentByEnd[f.end];\n                    delete fragmentByStart[g.start];\n                    if (f === g) {\n                        f.ring.push(end);\n                        callback(f.ring);\n                    } else {\n                        fragmentByStart[f.start] = fragmentByEnd[g.end] = {\n                            start: f.start,\n                            end: g.end,\n                            ring: f.ring.concat(g.ring)\n                        };\n                    }\n                } else {\n                    delete fragmentByEnd[f.end];\n                    f.ring.push(end);\n                    fragmentByEnd[f.end = endIndex] = f;\n                }\n            } else if (f = fragmentByStart[endIndex]) {\n                if (g = fragmentByEnd[startIndex]) {\n                    delete fragmentByStart[f.start];\n                    delete fragmentByEnd[g.end];\n                    if (f === g) {\n                        f.ring.push(end);\n                        callback(f.ring);\n                    } else {\n                        fragmentByStart[g.start] = fragmentByEnd[f.end] = {\n                            start: g.start,\n                            end: f.end,\n                            ring: g.ring.concat(f.ring)\n                        };\n                    }\n                } else {\n                    delete fragmentByStart[f.start];\n                    f.ring.unshift(start);\n                    fragmentByStart[f.start = startIndex] = f;\n                }\n            } else {\n                fragmentByStart[startIndex] = fragmentByEnd[endIndex] = {\n                    start: startIndex,\n                    end: endIndex,\n                    ring: [\n                        start,\n                        end\n                    ]\n                };\n            }\n        }\n    }\n    function index(point) {\n        return point[0] * 2 + point[1] * (dx + 1) * 4;\n    }\n    function smoothLinear(ring, values, value) {\n        ring.forEach(function(point) {\n            var x = point[0], y = point[1], xt = x | 0, yt = y | 0, v1 = valid(values[yt * dx + xt]);\n            if (x > 0 && x < dx && xt === x) {\n                point[0] = smooth1(x, valid(values[yt * dx + xt - 1]), v1, value);\n            }\n            if (y > 0 && y < dy && yt === y) {\n                point[1] = smooth1(y, valid(values[(yt - 1) * dx + xt]), v1, value);\n            }\n        });\n    }\n    contours.contour = contour;\n    contours.size = function(_) {\n        if (!arguments.length) return [\n            dx,\n            dy\n        ];\n        var _0 = Math.floor(_[0]), _1 = Math.floor(_[1]);\n        if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n        return dx = _0, dy = _1, contours;\n    };\n    contours.thresholds = function(_) {\n        return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0,_constant_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_array_js__WEBPACK_IMPORTED_MODULE_8__.slice.call(_)) : (0,_constant_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_), contours) : threshold;\n    };\n    contours.smooth = function(_) {\n        return arguments.length ? (smooth = _ ? smoothLinear : _noop_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"], contours) : smooth === smoothLinear;\n    };\n    return contours;\n}\n// When computing the extent, ignore infinite values (as well as invalid ones).\nfunction finite(x) {\n    return isFinite(x) ? x : NaN;\n}\n// Is the (possibly invalid) x greater than or equal to the (known valid) value?\n// Treat any invalid value as below negative infinity.\nfunction above(x, value) {\n    return x == null ? false : +x >= value;\n}\n// During smoothing, treat any invalid value as negative infinity.\nfunction valid(v) {\n    return v == null || isNaN(v = +v) ? -Infinity : v;\n}\nfunction smooth1(x, v0, v1, value) {\n    const a = value - v0;\n    const b = v1 - v0;\n    const d = isFinite(a) || isFinite(b) ? a / b : Math.sign(a) / Math.sign(b);\n    return isNaN(d) ? x : x + d - 0.5;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/contours.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/density.js":
/*!************************************************!*\
  !*** ./node_modules/d3-contour/src/density.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/blur.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/max.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-contour/src/array.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-contour/src/constant.js\");\n/* harmony import */ var _contours_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contours.js */ \"(ssr)/./node_modules/d3-contour/src/contours.js\");\n\n\n\n\nfunction defaultX(d) {\n    return d[0];\n}\nfunction defaultY(d) {\n    return d[1];\n}\nfunction defaultWeight() {\n    return 1;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var x = defaultX, y = defaultY, weight = defaultWeight, dx = 960, dy = 500, r = 20, k = 2, o = r * 3, n = dx + o * 2 >> k, m = dy + o * 2 >> k, threshold = (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(20);\n    function grid(data) {\n        var values = new Float32Array(n * m), pow2k = Math.pow(2, -k), i = -1;\n        for (const d of data){\n            var xi = (x(d, ++i, data) + o) * pow2k, yi = (y(d, i, data) + o) * pow2k, wi = +weight(d, i, data);\n            if (wi && xi >= 0 && xi < n && yi >= 0 && yi < m) {\n                var x0 = Math.floor(xi), y0 = Math.floor(yi), xt = xi - x0 - 0.5, yt = yi - y0 - 0.5;\n                values[x0 + y0 * n] += (1 - xt) * (1 - yt) * wi;\n                values[x0 + 1 + y0 * n] += xt * (1 - yt) * wi;\n                values[x0 + 1 + (y0 + 1) * n] += xt * yt * wi;\n                values[x0 + (y0 + 1) * n] += (1 - xt) * yt * wi;\n            }\n        }\n        (0,d3_array__WEBPACK_IMPORTED_MODULE_1__.blur2)({\n            data: values,\n            width: n,\n            height: m\n        }, r * pow2k);\n        return values;\n    }\n    function density(data) {\n        var values = grid(data), tz = threshold(values), pow4k = Math.pow(2, 2 * k);\n        // Convert number of thresholds into uniform thresholds.\n        if (!Array.isArray(tz)) {\n            tz = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(Number.MIN_VALUE, (0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values) / pow4k, tz);\n        }\n        return (0,_contours_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().size([\n            n,\n            m\n        ]).thresholds(tz.map((d)=>d * pow4k))(values).map((c, i)=>(c.value = +tz[i], transform(c)));\n    }\n    density.contours = function(data) {\n        var values = grid(data), contours = (0,_contours_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().size([\n            n,\n            m\n        ]), pow4k = Math.pow(2, 2 * k), contour = (value)=>{\n            value = +value;\n            var c = transform(contours.contour(values, value * pow4k));\n            c.value = value; // preserve exact threshold value\n            return c;\n        };\n        Object.defineProperty(contour, \"max\", {\n            get: ()=>(0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values) / pow4k\n        });\n        return contour;\n    };\n    function transform(geometry) {\n        geometry.coordinates.forEach(transformPolygon);\n        return geometry;\n    }\n    function transformPolygon(coordinates) {\n        coordinates.forEach(transformRing);\n    }\n    function transformRing(coordinates) {\n        coordinates.forEach(transformPoint);\n    }\n    // TODO Optimize.\n    function transformPoint(coordinates) {\n        coordinates[0] = coordinates[0] * Math.pow(2, k) - o;\n        coordinates[1] = coordinates[1] * Math.pow(2, k) - o;\n    }\n    function resize() {\n        o = r * 3;\n        n = dx + o * 2 >> k;\n        m = dy + o * 2 >> k;\n        return density;\n    }\n    density.x = function(_) {\n        return arguments.length ? (x = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : x;\n    };\n    density.y = function(_) {\n        return arguments.length ? (y = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : y;\n    };\n    density.weight = function(_) {\n        return arguments.length ? (weight = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : weight;\n    };\n    density.size = function(_) {\n        if (!arguments.length) return [\n            dx,\n            dy\n        ];\n        var _0 = +_[0], _1 = +_[1];\n        if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n        return dx = _0, dy = _1, resize();\n    };\n    density.cellSize = function(_) {\n        if (!arguments.length) return 1 << k;\n        if (!((_ = +_) >= 1)) throw new Error(\"invalid cell size\");\n        return k = Math.floor(Math.log(_) / Math.LN2), resize();\n    };\n    density.thresholds = function(_) {\n        return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_array_js__WEBPACK_IMPORTED_MODULE_5__.slice.call(_)) : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_), density) : threshold;\n    };\n    density.bandwidth = function(_) {\n        if (!arguments.length) return Math.sqrt(r * (r + 1));\n        if (!((_ = +_) >= 0)) throw new Error(\"invalid bandwidth\");\n        return r = (Math.sqrt(4 * _ * _ + 1) - 1) / 2, resize();\n    };\n    return density;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/density.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/index.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-contour/src/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contourDensity: () => (/* reexport safe */ _density_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   contours: () => (/* reexport safe */ _contours_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _contours_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./contours.js */ \"(ssr)/./node_modules/d3-contour/src/contours.js\");\n/* harmony import */ var _density_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./density.js */ \"(ssr)/./node_modules/d3-contour/src/density.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrRDtBQUNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvaW5kZXguanM/NGZlMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge2RlZmF1bHQgYXMgY29udG91cnN9IGZyb20gXCIuL2NvbnRvdXJzLmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgY29udG91ckRlbnNpdHl9IGZyb20gXCIuL2RlbnNpdHkuanNcIjtcbiJdLCJuYW1lcyI6WyJkZWZhdWx0IiwiY29udG91cnMiLCJjb250b3VyRGVuc2l0eSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/noop.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-contour/src/noop.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvbm9vcC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsc0NBQVcsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL25vb3AuanM/ZDhiNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHt9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/noop.js\n");

/***/ })

};
;