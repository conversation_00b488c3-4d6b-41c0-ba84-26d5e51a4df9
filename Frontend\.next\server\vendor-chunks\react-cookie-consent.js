"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-cookie-consent";
exports.ids = ["vendor-chunks/react-cookie-consent"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-cookie-consent/dist/react-cookie-consent.esm.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-cookie-consent/dist/react-cookie-consent.esm.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConditionalWrapper: () => (/* binding */ ConditionalWrapper),\n/* harmony export */   CookieConsent: () => (/* binding */ CookieConsent),\n/* harmony export */   Cookies: () => (/* reexport default from dynamic */ js_cookie__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   OPTIONS: () => (/* binding */ POSITION_OPTIONS),\n/* harmony export */   POSITION_OPTIONS: () => (/* binding */ POSITION_OPTIONS),\n/* harmony export */   SAME_SITE_OPTIONS: () => (/* binding */ SAME_SITE_OPTIONS),\n/* harmony export */   VISIBILITY_OPTIONS: () => (/* binding */ VISIBILITY_OPTIONS),\n/* harmony export */   VISIBLE_OPTIONS: () => (/* binding */ VISIBILITY_OPTIONS),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultCookieConsentName: () => (/* binding */ defaultCookieConsentName),\n/* harmony export */   getCookieConsentValue: () => (/* binding */ getCookieConsentValue),\n/* harmony export */   getLegacyCookieName: () => (/* binding */ getLegacyCookieName),\n/* harmony export */   resetCookieConsentValue: () => (/* binding */ resetCookieConsentValue)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/src/js.cookie.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(js_cookie__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n/**\r\n * A function to wrap elements with a \"wrapper\" on a condition\r\n * @param {object} wrappingOptions\r\n *  condition == boolean condition, when to wrap\r\n *  wrapper == style to wrap. e.g <div>{children}</div>\r\n *  children == react passes whatever is between tags as children. Don't supply this yourself.\r\n */ var ConditionalWrapper = function ConditionalWrapper(_ref) {\n    var condition = _ref.condition, wrapper = _ref.wrapper, children = _ref.children;\n    return condition ? wrapper(children) : children;\n};\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n    subClass.prototype = Object.create(superClass.prototype);\n    subClass.prototype.constructor = subClass;\n    _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nvar POSITION_OPTIONS = {\n    TOP: \"top\",\n    BOTTOM: \"bottom\",\n    NONE: \"none\"\n};\nvar SAME_SITE_OPTIONS;\n(function(SAME_SITE_OPTIONS) {\n    SAME_SITE_OPTIONS[\"STRICT\"] = \"strict\";\n    SAME_SITE_OPTIONS[\"LAX\"] = \"lax\";\n    SAME_SITE_OPTIONS[\"NONE\"] = \"none\";\n})(SAME_SITE_OPTIONS || (SAME_SITE_OPTIONS = {}));\nvar VISIBILITY_OPTIONS = {\n    HIDDEN: \"hidden\",\n    SHOW: \"show\",\n    BY_COOKIE_VALUE: \"byCookieValue\"\n};\nvar defaultCookieConsentName = \"CookieConsent\";\nvar _excluded = [\n    \"children\"\n];\nvar DefaultButtonComponent = function DefaultButtonComponent(_ref) {\n    var children = _ref.children, props = _objectWithoutPropertiesLoose(_ref, _excluded);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"button\", Object.assign({}, props), children);\n};\nvar defaultCookieConsentProps = {\n    disableStyles: false,\n    hideOnAccept: true,\n    hideOnDecline: true,\n    location: POSITION_OPTIONS.BOTTOM,\n    visible: VISIBILITY_OPTIONS.BY_COOKIE_VALUE,\n    onAccept: function onAccept(_acceptedByScrolling) {},\n    onDecline: function onDecline() {},\n    cookieName: defaultCookieConsentName,\n    cookieValue: \"true\",\n    declineCookieValue: \"false\",\n    setDeclineCookie: true,\n    buttonText: \"I understand\",\n    declineButtonText: \"I decline\",\n    debug: false,\n    expires: 365,\n    containerClasses: \"CookieConsent\",\n    contentClasses: \"\",\n    buttonClasses: \"\",\n    buttonWrapperClasses: \"\",\n    declineButtonClasses: \"\",\n    buttonId: \"rcc-confirm-button\",\n    declineButtonId: \"rcc-decline-button\",\n    extraCookieOptions: {},\n    disableButtonStyles: false,\n    enableDeclineButton: false,\n    flipButtons: false,\n    sameSite: SAME_SITE_OPTIONS.LAX,\n    ButtonComponent: DefaultButtonComponent,\n    overlay: false,\n    overlayClasses: \"\",\n    onOverlayClick: function onOverlayClick() {},\n    acceptOnOverlayClick: false,\n    ariaAcceptLabel: \"Accept cookies\",\n    ariaDeclineLabel: \"Decline cookies\",\n    acceptOnScroll: false,\n    acceptOnScrollPercentage: 25,\n    customContentAttributes: {},\n    customContainerAttributes: {},\n    customButtonProps: {},\n    customDeclineButtonProps: {},\n    customButtonWrapperAttributes: {},\n    style: {},\n    buttonStyle: {},\n    declineButtonStyle: {},\n    contentStyle: {},\n    overlayStyle: {}\n};\nvar defaultState = {\n    visible: false,\n    style: {\n        alignItems: \"baseline\",\n        background: \"#353535\",\n        color: \"white\",\n        display: \"flex\",\n        flexWrap: \"wrap\",\n        justifyContent: \"space-between\",\n        left: \"0\",\n        position: \"fixed\",\n        width: \"100%\",\n        zIndex: \"999\"\n    },\n    buttonStyle: {\n        background: \"#ffd42d\",\n        border: \"0\",\n        borderRadius: \"0px\",\n        boxShadow: \"none\",\n        color: \"black\",\n        cursor: \"pointer\",\n        flex: \"0 0 auto\",\n        padding: \"5px 10px\",\n        margin: \"15px\"\n    },\n    declineButtonStyle: {\n        background: \"#c12a2a\",\n        border: \"0\",\n        borderRadius: \"0px\",\n        boxShadow: \"none\",\n        color: \"#e5e5e5\",\n        cursor: \"pointer\",\n        flex: \"0 0 auto\",\n        padding: \"5px 10px\",\n        margin: \"15px\"\n    },\n    contentStyle: {\n        flex: \"1 0 300px\",\n        margin: \"15px\"\n    },\n    overlayStyle: {\n        position: \"fixed\",\n        left: 0,\n        top: 0,\n        width: \"100%\",\n        height: \"100%\",\n        zIndex: \"999\",\n        backgroundColor: \"rgba(0,0,0,0.3)\"\n    }\n};\n/**\r\n * Returns the value of the consent cookie\r\n * Retrieves the regular value first and if not found the legacy one according\r\n * to: https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\r\n * @param {*} name optional name of the cookie\r\n */ var getCookieConsentValue = function getCookieConsentValue(name) {\n    if (name === void 0) {\n        name = defaultCookieConsentName;\n    }\n    var cookieValue = js_cookie__WEBPACK_IMPORTED_MODULE_0___default().get(name);\n    // if the cookieValue is undefined check for the legacy cookie\n    if (cookieValue === undefined) {\n        return js_cookie__WEBPACK_IMPORTED_MODULE_0___default().get(getLegacyCookieName(name));\n    }\n    return cookieValue;\n};\n/**\r\n * Reset the consent cookie\r\n * Remove the cookie on browser in order to allow user to change their consent\r\n * @param {*} name optional name of the cookie\r\n */ var resetCookieConsentValue = function resetCookieConsentValue(name) {\n    if (name === void 0) {\n        name = defaultCookieConsentName;\n    }\n    js_cookie__WEBPACK_IMPORTED_MODULE_0___default().remove(name);\n};\n/**\r\n * Get the legacy cookie name by the regular cookie name\r\n * @param {string} name of cookie to get\r\n */ var getLegacyCookieName = function getLegacyCookieName(name) {\n    return name + \"-legacy\";\n};\nvar CookieConsent = /*#__PURE__*/ function(_Component) {\n    _inheritsLoose(CookieConsent, _Component);\n    function CookieConsent() {\n        var _this;\n        _this = _Component.apply(this, arguments) || this;\n        _this.state = defaultState;\n        /**\r\n     * checks whether scroll has exceeded set amount and fire accept if so.\r\n     */ _this.handleScroll = function() {\n            var _defaultCookieConsent = _extends({}, defaultCookieConsentProps, _this.props), acceptOnScrollPercentage = _defaultCookieConsent.acceptOnScrollPercentage;\n            // (top / height) - height * 100\n            var rootNode = document.documentElement;\n            var body = document.body;\n            var top = \"scrollTop\";\n            var height = \"scrollHeight\";\n            var percentage = (rootNode[top] || body[top]) / ((rootNode[height] || body[height]) - rootNode.clientHeight) * 100;\n            if (percentage > acceptOnScrollPercentage) {\n                _this.accept(true);\n            }\n        };\n        _this.removeScrollListener = function() {\n            var acceptOnScroll = _this.props.acceptOnScroll;\n            if (acceptOnScroll) {\n                window.removeEventListener(\"scroll\", _this.handleScroll);\n            }\n        };\n        return _this;\n    }\n    var _proto = CookieConsent.prototype;\n    _proto.componentDidMount = function componentDidMount() {\n        var debug = this.props.debug;\n        // if cookie undefined or debug\n        if (this.getCookieValue() === undefined || debug) {\n            this.setState({\n                visible: true\n            });\n            // if acceptOnScroll is set to true and (cookie is undefined or debug is set to true), add a listener.\n            if (this.props.acceptOnScroll) {\n                window.addEventListener(\"scroll\", this.handleScroll, {\n                    passive: true\n                });\n            }\n        }\n    };\n    _proto.componentWillUnmount = function componentWillUnmount() {\n        // remove listener if still set\n        this.removeScrollListener();\n    } /**\r\n   * Set a persistent accept cookie\r\n   */ ;\n    _proto.accept = function accept(acceptedByScrolling) {\n        var _acceptedByScrolling;\n        if (acceptedByScrolling === void 0) {\n            acceptedByScrolling = false;\n        }\n        var _defaultCookieConsent2 = _extends({}, defaultCookieConsentProps, this.props), cookieName = _defaultCookieConsent2.cookieName, cookieValue = _defaultCookieConsent2.cookieValue, hideOnAccept = _defaultCookieConsent2.hideOnAccept, onAccept = _defaultCookieConsent2.onAccept;\n        this.setCookie(cookieName, cookieValue);\n        onAccept((_acceptedByScrolling = acceptedByScrolling) != null ? _acceptedByScrolling : false);\n        if (hideOnAccept) {\n            this.setState({\n                visible: false\n            });\n            this.removeScrollListener();\n        }\n    } /**\r\n   * Handle a click on the overlay\r\n   */ ;\n    _proto.overlayClick = function overlayClick() {\n        var _defaultCookieConsent3 = _extends({}, defaultCookieConsentProps, this.props), acceptOnOverlayClick = _defaultCookieConsent3.acceptOnOverlayClick, onOverlayClick = _defaultCookieConsent3.onOverlayClick;\n        if (acceptOnOverlayClick) {\n            this.accept();\n        }\n        onOverlayClick();\n    } /**\r\n   * Set a persistent decline cookie\r\n   */ ;\n    _proto.decline = function decline() {\n        var _defaultCookieConsent4 = _extends({}, defaultCookieConsentProps, this.props), cookieName = _defaultCookieConsent4.cookieName, declineCookieValue = _defaultCookieConsent4.declineCookieValue, hideOnDecline = _defaultCookieConsent4.hideOnDecline, onDecline = _defaultCookieConsent4.onDecline, setDeclineCookie = _defaultCookieConsent4.setDeclineCookie;\n        if (setDeclineCookie) {\n            this.setCookie(cookieName, declineCookieValue);\n        }\n        onDecline();\n        if (hideOnDecline) {\n            this.setState({\n                visible: false\n            });\n        }\n    } /**\r\n   * Function to set the consent cookie based on the provided variables\r\n   * Sets two cookies to handle incompatible browsers, more details:\r\n   * https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\r\n   */ ;\n    _proto.setCookie = function setCookie(cookieName, cookieValue) {\n        var _this$props = this.props, extraCookieOptions = _this$props.extraCookieOptions, expires = _this$props.expires, sameSite = _this$props.sameSite;\n        var cookieSecurity = this.props.cookieSecurity;\n        if (cookieSecurity === undefined) {\n            cookieSecurity = window.location ? window.location.protocol === \"https:\" : true;\n        }\n        var cookieOptions = _extends({\n            expires: expires\n        }, extraCookieOptions, {\n            sameSite: sameSite,\n            secure: cookieSecurity\n        });\n        // Fallback for older browsers where can not set SameSite=None,\n        // SEE: https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\n        if (sameSite === SAME_SITE_OPTIONS.NONE) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0___default().set(getLegacyCookieName(cookieName), cookieValue, cookieOptions);\n        }\n        // set the regular cookie\n        js_cookie__WEBPACK_IMPORTED_MODULE_0___default().set(cookieName, cookieValue, cookieOptions);\n    } /**\r\n   * Returns the value of the consent cookie\r\n   * Retrieves the regular value first and if not found the legacy one according\r\n   * to: https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\r\n   */ ;\n    _proto.getCookieValue = function getCookieValue() {\n        var cookieName = this.props.cookieName;\n        return getCookieConsentValue(cookieName);\n    };\n    _proto.render = function render() {\n        var _this2 = this;\n        // If the bar shouldn't be visible don't render anything.\n        switch(this.props.visible){\n            case VISIBILITY_OPTIONS.HIDDEN:\n                return null;\n            case VISIBILITY_OPTIONS.BY_COOKIE_VALUE:\n                if (!this.state.visible) {\n                    return null;\n                }\n                break;\n        }\n        var _this$props2 = this.props, location = _this$props2.location, style = _this$props2.style, buttonStyle = _this$props2.buttonStyle, declineButtonStyle = _this$props2.declineButtonStyle, contentStyle = _this$props2.contentStyle, disableStyles = _this$props2.disableStyles, buttonText = _this$props2.buttonText, declineButtonText = _this$props2.declineButtonText, containerClasses = _this$props2.containerClasses, contentClasses = _this$props2.contentClasses, buttonClasses = _this$props2.buttonClasses, buttonWrapperClasses = _this$props2.buttonWrapperClasses, declineButtonClasses = _this$props2.declineButtonClasses, buttonId = _this$props2.buttonId, declineButtonId = _this$props2.declineButtonId, disableButtonStyles = _this$props2.disableButtonStyles, enableDeclineButton = _this$props2.enableDeclineButton, flipButtons = _this$props2.flipButtons, ButtonComponent = _this$props2.ButtonComponent, overlay = _this$props2.overlay, overlayClasses = _this$props2.overlayClasses, overlayStyle = _this$props2.overlayStyle, ariaAcceptLabel = _this$props2.ariaAcceptLabel, ariaDeclineLabel = _this$props2.ariaDeclineLabel, customContainerAttributes = _this$props2.customContainerAttributes, customContentAttributes = _this$props2.customContentAttributes, customButtonProps = _this$props2.customButtonProps, customDeclineButtonProps = _this$props2.customDeclineButtonProps, customButtonWrapperAttributes = _this$props2.customButtonWrapperAttributes;\n        var myStyle = {};\n        var myButtonStyle = {};\n        var myDeclineButtonStyle = {};\n        var myContentStyle = {};\n        var myOverlayStyle = {};\n        if (disableStyles) {\n            // if styles are disabled use the provided styles (or none)\n            myStyle = Object.assign({}, style);\n            myButtonStyle = Object.assign({}, buttonStyle);\n            myDeclineButtonStyle = Object.assign({}, declineButtonStyle);\n            myContentStyle = Object.assign({}, contentStyle);\n            myOverlayStyle = Object.assign({}, overlayStyle);\n        } else {\n            // if styles aren't disabled merge them with the styles that are provided (or use default styles)\n            myStyle = Object.assign({}, _extends({}, this.state.style, style));\n            myContentStyle = Object.assign({}, _extends({}, this.state.contentStyle, contentStyle));\n            myOverlayStyle = Object.assign({}, _extends({}, this.state.overlayStyle, overlayStyle));\n            // switch to disable JUST the button styles\n            if (disableButtonStyles) {\n                myButtonStyle = Object.assign({}, buttonStyle);\n                myDeclineButtonStyle = Object.assign({}, declineButtonStyle);\n            } else {\n                myButtonStyle = Object.assign({}, _extends({}, this.state.buttonStyle, buttonStyle));\n                myDeclineButtonStyle = Object.assign({}, _extends({}, this.state.declineButtonStyle, declineButtonStyle));\n            }\n        }\n        // syntactic sugar to enable user to easily select top / bottom\n        switch(location){\n            case POSITION_OPTIONS.TOP:\n                myStyle.top = \"0\";\n                break;\n            case POSITION_OPTIONS.BOTTOM:\n                myStyle.bottom = \"0\";\n                break;\n        }\n        var buttonsToRender = [];\n        // add decline button\n        enableDeclineButton && buttonsToRender.push(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(ButtonComponent, Object.assign({\n            key: \"declineButton\",\n            style: myDeclineButtonStyle,\n            className: declineButtonClasses,\n            id: declineButtonId,\n            \"aria-label\": ariaDeclineLabel,\n            onClick: function onClick() {\n                _this2.decline();\n            }\n        }, customDeclineButtonProps), declineButtonText));\n        // add accept button\n        buttonsToRender.push(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(ButtonComponent, Object.assign({\n            key: \"acceptButton\",\n            style: myButtonStyle,\n            className: buttonClasses,\n            id: buttonId,\n            \"aria-label\": ariaAcceptLabel,\n            onClick: function onClick() {\n                _this2.accept();\n            }\n        }, customButtonProps), buttonText));\n        if (flipButtons) {\n            buttonsToRender.reverse();\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(ConditionalWrapper, {\n            condition: overlay,\n            wrapper: function wrapper(children) {\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n                    style: myOverlayStyle,\n                    className: overlayClasses,\n                    onClick: function onClick() {\n                        _this2.overlayClick();\n                    }\n                }, children);\n            }\n        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", Object.assign({\n            className: \"\" + containerClasses,\n            style: myStyle\n        }, customContainerAttributes), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", Object.assign({\n            style: myContentStyle,\n            className: contentClasses\n        }, customContentAttributes), this.props.children), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", Object.assign({\n            className: \"\" + buttonWrapperClasses\n        }, customButtonWrapperAttributes), buttonsToRender.map(function(button) {\n            return button;\n        }))));\n    };\n    return CookieConsent;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\nCookieConsent.defaultProps = defaultCookieConsentProps;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CookieConsent);\n //# sourceMappingURL=react-cookie-consent.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-cookie-consent/dist/react-cookie-consent.esm.js\n");

/***/ })

};
;