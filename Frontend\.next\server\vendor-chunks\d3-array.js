"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-array";
exports.ids = ["vendor-chunks/d3-array"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-array/src/array.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/array.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   slice: () => (/* binding */ slice)\n/* harmony export */ });\nvar array = Array.prototype;\nvar slice = array.slice;\nvar map = array.map;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2FycmF5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsSUFBSUEsUUFBUUMsTUFBTUMsU0FBUztBQUVwQixJQUFJQyxRQUFRSCxNQUFNRyxLQUFLLENBQUM7QUFDeEIsSUFBSUMsTUFBTUosTUFBTUksR0FBRyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2FycmF5LmpzP2NiN2MiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGFycmF5ID0gQXJyYXkucHJvdG90eXBlO1xuXG5leHBvcnQgdmFyIHNsaWNlID0gYXJyYXkuc2xpY2U7XG5leHBvcnQgdmFyIG1hcCA9IGFycmF5Lm1hcDtcbiJdLCJuYW1lcyI6WyJhcnJheSIsIkFycmF5IiwicHJvdG90eXBlIiwic2xpY2UiLCJtYXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/array.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/ascending.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/ascending.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ascending)\n/* harmony export */ });\nfunction ascending(a, b) {\n    return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2FzY2VuZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsVUFBVUMsQ0FBQyxFQUFFQyxDQUFDO0lBQ3BDLE9BQU9ELEtBQUssUUFBUUMsS0FBSyxPQUFPQyxNQUFNRixJQUFJQyxJQUFJLENBQUMsSUFBSUQsSUFBSUMsSUFBSSxJQUFJRCxLQUFLQyxJQUFJLElBQUlDO0FBQzlFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2FzY2VuZGluZy5qcz9lOTA0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGFzY2VuZGluZyhhLCBiKSB7XG4gIHJldHVybiBhID09IG51bGwgfHwgYiA9PSBudWxsID8gTmFOIDogYSA8IGIgPyAtMSA6IGEgPiBiID8gMSA6IGEgPj0gYiA/IDAgOiBOYU47XG59XG4iXSwibmFtZXMiOlsiYXNjZW5kaW5nIiwiYSIsImIiLCJOYU4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/ascending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/bin.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/bin.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ bin)\n/* harmony export */ });\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-array/src/array.js\");\n/* harmony import */ var _bisect_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./bisect.js */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-array/src/constant.js\");\n/* harmony import */ var _extent_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./extent.js */ \"(ssr)/./node_modules/d3-array/src/extent.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/./node_modules/d3-array/src/identity.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/./node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./threshold/sturges.js */ \"(ssr)/./node_modules/d3-array/src/threshold/sturges.js\");\n\n\n\n\n\n\n\n\nfunction bin() {\n    var value = _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], domain = _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], threshold = _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n    function histogram(data) {\n        if (!Array.isArray(data)) data = Array.from(data);\n        var i, n = data.length, x, step, values = new Array(n);\n        for(i = 0; i < n; ++i){\n            values[i] = value(data[i], i, data);\n        }\n        var xz = domain(values), x0 = xz[0], x1 = xz[1], tz = threshold(values, x0, x1);\n        // Convert number of thresholds into uniform thresholds, and nice the\n        // default domain accordingly.\n        if (!Array.isArray(tz)) {\n            const max = x1, tn = +tz;\n            if (domain === _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) [x0, x1] = (0,_nice_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(x0, x1, tn);\n            tz = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(x0, x1, tn);\n            // If the domain is aligned with the first tick (which it will by\n            // default), then we can use quantization rather than bisection to bin\n            // values, which is substantially faster.\n            if (tz[0] <= x0) step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__.tickIncrement)(x0, x1, tn);\n            // If the last threshold is coincident with the domain’s upper bound, the\n            // last bin will be zero-width. If the default domain is used, and this\n            // last threshold is coincident with the maximum input value, we can\n            // extend the niced upper bound by one tick to ensure uniform bin widths;\n            // otherwise, we simply remove the last threshold. Note that we don’t\n            // coerce values or the domain to numbers, and thus must be careful to\n            // compare order (>=) rather than strict equality (===)!\n            if (tz[tz.length - 1] >= x1) {\n                if (max >= x1 && domain === _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n                    const step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__.tickIncrement)(x0, x1, tn);\n                    if (isFinite(step)) {\n                        if (step > 0) {\n                            x1 = (Math.floor(x1 / step) + 1) * step;\n                        } else if (step < 0) {\n                            x1 = (Math.ceil(x1 * -step) + 1) / -step;\n                        }\n                    }\n                } else {\n                    tz.pop();\n                }\n            }\n        }\n        // Remove any thresholds outside the domain.\n        // Be careful not to mutate an array owned by the user!\n        var m = tz.length, a = 0, b = m;\n        while(tz[a] <= x0)++a;\n        while(tz[b - 1] > x1)--b;\n        if (a || b < m) tz = tz.slice(a, b), m = b - a;\n        var bins = new Array(m + 1), bin;\n        // Initialize bins.\n        for(i = 0; i <= m; ++i){\n            bin = bins[i] = [];\n            bin.x0 = i > 0 ? tz[i - 1] : x0;\n            bin.x1 = i < m ? tz[i] : x1;\n        }\n        // Assign data to bins by value, ignoring any outside the domain.\n        if (isFinite(step)) {\n            if (step > 0) {\n                for(i = 0; i < n; ++i){\n                    if ((x = values[i]) != null && x0 <= x && x <= x1) {\n                        bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n                    }\n                }\n            } else if (step < 0) {\n                for(i = 0; i < n; ++i){\n                    if ((x = values[i]) != null && x0 <= x && x <= x1) {\n                        const j = Math.floor((x0 - x) * step);\n                        bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n                    }\n                }\n            }\n        } else {\n            for(i = 0; i < n; ++i){\n                if ((x = values[i]) != null && x0 <= x && x <= x1) {\n                    bins[(0,_bisect_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(tz, x, 0, m)].push(data[i]);\n                }\n            }\n        }\n        return bins;\n    }\n    histogram.value = function(_) {\n        return arguments.length ? (value = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_), histogram) : value;\n    };\n    histogram.domain = function(_) {\n        return arguments.length ? (domain = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])([\n            _[0],\n            _[1]\n        ]), histogram) : domain;\n    };\n    histogram.thresholds = function(_) {\n        return arguments.length ? (threshold = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(Array.isArray(_) ? _array_js__WEBPACK_IMPORTED_MODULE_7__.slice.call(_) : _), histogram) : threshold;\n    };\n    return histogram;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/bin.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/bisect.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/bisect.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bisectCenter: () => (/* binding */ bisectCenter),\n/* harmony export */   bisectLeft: () => (/* binding */ bisectLeft),\n/* harmony export */   bisectRight: () => (/* binding */ bisectRight),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _bisector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bisector.js */ \"(ssr)/./node_modules/d3-array/src/bisector.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-array/src/number.js\");\n\n\n\nconst ascendingBisect = (0,_bisector_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\nconst bisectRight = ascendingBisect.right;\nconst bisectLeft = ascendingBisect.left;\nconst bisectCenter = (0,_bisector_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_number_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).center;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (bisectRight);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Jpc2VjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXVDO0FBQ0Y7QUFDSjtBQUVqQyxNQUFNRyxrQkFBa0JGLHdEQUFRQSxDQUFDRCxxREFBU0E7QUFDbkMsTUFBTUksY0FBY0QsZ0JBQWdCRSxLQUFLLENBQUM7QUFDMUMsTUFBTUMsYUFBYUgsZ0JBQWdCSSxJQUFJLENBQUM7QUFDeEMsTUFBTUMsZUFBZVAsd0RBQVFBLENBQUNDLGtEQUFNQSxFQUFFTyxNQUFNLENBQUM7QUFDcEQsaUVBQWVMLFdBQVdBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvYmlzZWN0LmpzPzEwMDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFzY2VuZGluZyBmcm9tIFwiLi9hc2NlbmRpbmcuanNcIjtcbmltcG9ydCBiaXNlY3RvciBmcm9tIFwiLi9iaXNlY3Rvci5qc1wiO1xuaW1wb3J0IG51bWJlciBmcm9tIFwiLi9udW1iZXIuanNcIjtcblxuY29uc3QgYXNjZW5kaW5nQmlzZWN0ID0gYmlzZWN0b3IoYXNjZW5kaW5nKTtcbmV4cG9ydCBjb25zdCBiaXNlY3RSaWdodCA9IGFzY2VuZGluZ0Jpc2VjdC5yaWdodDtcbmV4cG9ydCBjb25zdCBiaXNlY3RMZWZ0ID0gYXNjZW5kaW5nQmlzZWN0LmxlZnQ7XG5leHBvcnQgY29uc3QgYmlzZWN0Q2VudGVyID0gYmlzZWN0b3IobnVtYmVyKS5jZW50ZXI7XG5leHBvcnQgZGVmYXVsdCBiaXNlY3RSaWdodDtcbiJdLCJuYW1lcyI6WyJhc2NlbmRpbmciLCJiaXNlY3RvciIsIm51bWJlciIsImFzY2VuZGluZ0Jpc2VjdCIsImJpc2VjdFJpZ2h0IiwicmlnaHQiLCJiaXNlY3RMZWZ0IiwibGVmdCIsImJpc2VjdENlbnRlciIsImNlbnRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/bisect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/bisector.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/bisector.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ bisector)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _descending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./descending.js */ \"(ssr)/./node_modules/d3-array/src/descending.js\");\n\n\nfunction bisector(f) {\n    let compare1, compare2, delta;\n    // If an accessor is specified, promote it to a comparator. In this case we\n    // can test whether the search value is (self-) comparable. We can’t do this\n    // for a comparator (except for specific, known comparators) because we can’t\n    // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n    // used to test whether a single value is comparable.\n    if (f.length !== 2) {\n        compare1 = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n        compare2 = (d, x)=>(0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(f(d), x);\n        delta = (d, x)=>f(d) - x;\n    } else {\n        compare1 = f === _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] || f === _descending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"] ? f : zero;\n        compare2 = f;\n        delta = f;\n    }\n    function left(a, x, lo = 0, hi = a.length) {\n        if (lo < hi) {\n            if (compare1(x, x) !== 0) return hi;\n            do {\n                const mid = lo + hi >>> 1;\n                if (compare2(a[mid], x) < 0) lo = mid + 1;\n                else hi = mid;\n            }while (lo < hi);\n        }\n        return lo;\n    }\n    function right(a, x, lo = 0, hi = a.length) {\n        if (lo < hi) {\n            if (compare1(x, x) !== 0) return hi;\n            do {\n                const mid = lo + hi >>> 1;\n                if (compare2(a[mid], x) <= 0) lo = mid + 1;\n                else hi = mid;\n            }while (lo < hi);\n        }\n        return lo;\n    }\n    function center(a, x, lo = 0, hi = a.length) {\n        const i = left(a, x, lo, hi - 1);\n        return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n    }\n    return {\n        left,\n        center,\n        right\n    };\n}\nfunction zero() {\n    return 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/bisector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/blur.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/blur.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blur: () => (/* binding */ blur),\n/* harmony export */   blur2: () => (/* binding */ blur2),\n/* harmony export */   blurImage: () => (/* binding */ blurImage)\n/* harmony export */ });\nfunction blur(values, r) {\n    if (!((r = +r) >= 0)) throw new RangeError(\"invalid r\");\n    let length = values.length;\n    if (!((length = Math.floor(length)) >= 0)) throw new RangeError(\"invalid length\");\n    if (!length || !r) return values;\n    const blur = blurf(r);\n    const temp = values.slice();\n    blur(values, temp, 0, length, 1);\n    blur(temp, values, 0, length, 1);\n    blur(values, temp, 0, length, 1);\n    return values;\n}\nconst blur2 = Blur2(blurf);\nconst blurImage = Blur2(blurfImage);\nfunction Blur2(blur) {\n    return function(data, rx, ry = rx) {\n        if (!((rx = +rx) >= 0)) throw new RangeError(\"invalid rx\");\n        if (!((ry = +ry) >= 0)) throw new RangeError(\"invalid ry\");\n        let { data: values, width, height } = data;\n        if (!((width = Math.floor(width)) >= 0)) throw new RangeError(\"invalid width\");\n        if (!((height = Math.floor(height !== undefined ? height : values.length / width)) >= 0)) throw new RangeError(\"invalid height\");\n        if (!width || !height || !rx && !ry) return data;\n        const blurx = rx && blur(rx);\n        const blury = ry && blur(ry);\n        const temp = values.slice();\n        if (blurx && blury) {\n            blurh(blurx, temp, values, width, height);\n            blurh(blurx, values, temp, width, height);\n            blurh(blurx, temp, values, width, height);\n            blurv(blury, values, temp, width, height);\n            blurv(blury, temp, values, width, height);\n            blurv(blury, values, temp, width, height);\n        } else if (blurx) {\n            blurh(blurx, values, temp, width, height);\n            blurh(blurx, temp, values, width, height);\n            blurh(blurx, values, temp, width, height);\n        } else if (blury) {\n            blurv(blury, values, temp, width, height);\n            blurv(blury, temp, values, width, height);\n            blurv(blury, values, temp, width, height);\n        }\n        return data;\n    };\n}\nfunction blurh(blur, T, S, w, h) {\n    for(let y = 0, n = w * h; y < n;){\n        blur(T, S, y, y += w, 1);\n    }\n}\nfunction blurv(blur, T, S, w, h) {\n    for(let x = 0, n = w * h; x < w; ++x){\n        blur(T, S, x, x + n, w);\n    }\n}\nfunction blurfImage(radius) {\n    const blur = blurf(radius);\n    return (T, S, start, stop, step)=>{\n        start <<= 2, stop <<= 2, step <<= 2;\n        blur(T, S, start + 0, stop + 0, step);\n        blur(T, S, start + 1, stop + 1, step);\n        blur(T, S, start + 2, stop + 2, step);\n        blur(T, S, start + 3, stop + 3, step);\n    };\n}\n// Given a target array T, a source array S, sets each value T[i] to the average\n// of {S[i - r], …, S[i], …, S[i + r]}, where r = ⌊radius⌋, start <= i < stop,\n// for each i, i + step, i + 2 * step, etc., and where S[j] is clamped between\n// S[start] (inclusive) and S[stop] (exclusive). If the given radius is not an\n// integer, S[i - r - 1] and S[i + r + 1] are added to the sum, each weighted\n// according to r - ⌊radius⌋.\nfunction blurf(radius) {\n    const radius0 = Math.floor(radius);\n    if (radius0 === radius) return bluri(radius);\n    const t = radius - radius0;\n    const w = 2 * radius + 1;\n    return (T, S, start, stop, step)=>{\n        if (!((stop -= step) >= start)) return; // inclusive stop\n        let sum = radius0 * S[start];\n        const s0 = step * radius0;\n        const s1 = s0 + step;\n        for(let i = start, j = start + s0; i < j; i += step){\n            sum += S[Math.min(stop, i)];\n        }\n        for(let i = start, j = stop; i <= j; i += step){\n            sum += S[Math.min(stop, i + s0)];\n            T[i] = (sum + t * (S[Math.max(start, i - s1)] + S[Math.min(stop, i + s1)])) / w;\n            sum -= S[Math.max(start, i - s0)];\n        }\n    };\n}\n// Like blurf, but optimized for integer radius.\nfunction bluri(radius) {\n    const w = 2 * radius + 1;\n    return (T, S, start, stop, step)=>{\n        if (!((stop -= step) >= start)) return; // inclusive stop\n        let sum = radius * S[start];\n        const s = step * radius;\n        for(let i = start, j = start + s; i < j; i += step){\n            sum += S[Math.min(stop, i)];\n        }\n        for(let i = start, j = stop; i <= j; i += step){\n            sum += S[Math.min(stop, i + s)];\n            T[i] = sum / w;\n            sum -= S[Math.max(start, i - s)];\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/blur.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/constant.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/constant.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ constant)\n/* harmony export */ });\nfunction constant(x) {\n    return ()=>x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxTQUFTQyxDQUFDO0lBQ2hDLE9BQU8sSUFBTUE7QUFDZiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9jb25zdGFudC5qcz85YzM3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNvbnN0YW50KHgpIHtcbiAgcmV0dXJuICgpID0+IHg7XG59XG4iXSwibmFtZXMiOlsiY29uc3RhbnQiLCJ4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/count.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/count.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ count)\n/* harmony export */ });\nfunction count(values, valueof) {\n    let count = 0;\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value != null && (value = +value) >= value) {\n                ++count;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n                ++count;\n            }\n        }\n    }\n    return count;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2NvdW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxNQUFNQyxNQUFNLEVBQUVDLE9BQU87SUFDM0MsSUFBSUYsUUFBUTtJQUNaLElBQUlFLFlBQVlDLFdBQVc7UUFDekIsS0FBSyxJQUFJQyxTQUFTSCxPQUFRO1lBQ3hCLElBQUlHLFNBQVMsUUFBUSxDQUFDQSxRQUFRLENBQUNBLEtBQUksS0FBTUEsT0FBTztnQkFDOUMsRUFBRUo7WUFDSjtRQUNGO0lBQ0YsT0FBTztRQUNMLElBQUlLLFFBQVEsQ0FBQztRQUNiLEtBQUssSUFBSUQsU0FBU0gsT0FBUTtZQUN4QixJQUFJLENBQUNHLFFBQVFGLFFBQVFFLE9BQU8sRUFBRUMsT0FBT0osT0FBTSxLQUFNLFFBQVEsQ0FBQ0csUUFBUSxDQUFDQSxLQUFJLEtBQU1BLE9BQU87Z0JBQ2xGLEVBQUVKO1lBQ0o7UUFDRjtJQUNGO0lBQ0EsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9jb3VudC5qcz9hMjlkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNvdW50KHZhbHVlcywgdmFsdWVvZikge1xuICBsZXQgY291bnQgPSAwO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgICsrY291bnQ7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGxldCBpbmRleCA9IC0xO1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKCh2YWx1ZSA9IHZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpICE9IG51bGwgJiYgKHZhbHVlID0gK3ZhbHVlKSA+PSB2YWx1ZSkge1xuICAgICAgICArK2NvdW50O1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gY291bnQ7XG59XG4iXSwibmFtZXMiOlsiY291bnQiLCJ2YWx1ZXMiLCJ2YWx1ZW9mIiwidW5kZWZpbmVkIiwidmFsdWUiLCJpbmRleCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/count.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/cross.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/cross.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ cross)\n/* harmony export */ });\nfunction length(array) {\n    return array.length | 0;\n}\nfunction empty(length) {\n    return !(length > 0);\n}\nfunction arrayify(values) {\n    return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\nfunction reducer(reduce) {\n    return (values)=>reduce(...values);\n}\nfunction cross(...values) {\n    const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n    values = values.map(arrayify);\n    const lengths = values.map(length);\n    const j = values.length - 1;\n    const index = new Array(j + 1).fill(0);\n    const product = [];\n    if (j < 0 || lengths.some(empty)) return product;\n    while(true){\n        product.push(index.map((j, i)=>values[i][j]));\n        let i = j;\n        while(++index[i] === lengths[i]){\n            if (i === 0) return reduce ? product.map(reduce) : product;\n            index[i--] = 0;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/cross.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/cumsum.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/cumsum.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ cumsum)\n/* harmony export */ });\nfunction cumsum(values, valueof) {\n    var sum = 0, index = 0;\n    return Float64Array.from(values, valueof === undefined ? (v)=>sum += +v || 0 : (v)=>sum += +valueof(v, index++, values) || 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2N1bXN1bS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsT0FBT0MsTUFBTSxFQUFFQyxPQUFPO0lBQzVDLElBQUlDLE1BQU0sR0FBR0MsUUFBUTtJQUNyQixPQUFPQyxhQUFhQyxJQUFJLENBQUNMLFFBQVFDLFlBQVlLLFlBQ3pDQyxDQUFBQSxJQUFNTCxPQUFPLENBQUNLLEtBQUssSUFDbkJBLENBQUFBLElBQU1MLE9BQU8sQ0FBQ0QsUUFBUU0sR0FBR0osU0FBU0gsV0FBVztBQUNuRCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9jdW1zdW0uanM/Y2FmMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBjdW1zdW0odmFsdWVzLCB2YWx1ZW9mKSB7XG4gIHZhciBzdW0gPSAwLCBpbmRleCA9IDA7XG4gIHJldHVybiBGbG9hdDY0QXJyYXkuZnJvbSh2YWx1ZXMsIHZhbHVlb2YgPT09IHVuZGVmaW5lZFxuICAgID8gdiA9PiAoc3VtICs9ICt2IHx8IDApXG4gICAgOiB2ID0+IChzdW0gKz0gK3ZhbHVlb2YodiwgaW5kZXgrKywgdmFsdWVzKSB8fCAwKSk7XG59Il0sIm5hbWVzIjpbImN1bXN1bSIsInZhbHVlcyIsInZhbHVlb2YiLCJzdW0iLCJpbmRleCIsIkZsb2F0NjRBcnJheSIsImZyb20iLCJ1bmRlZmluZWQiLCJ2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/cumsum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/descending.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-array/src/descending.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ descending)\n/* harmony export */ });\nfunction descending(a, b) {\n    return a == null || b == null ? NaN : b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Rlc2NlbmRpbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLFdBQVdDLENBQUMsRUFBRUMsQ0FBQztJQUNyQyxPQUFPRCxLQUFLLFFBQVFDLEtBQUssT0FBT0MsTUFDNUJELElBQUlELElBQUksQ0FBQyxJQUNUQyxJQUFJRCxJQUFJLElBQ1JDLEtBQUtELElBQUksSUFDVEU7QUFDTiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9kZXNjZW5kaW5nLmpzP2FkNWQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZGVzY2VuZGluZyhhLCBiKSB7XG4gIHJldHVybiBhID09IG51bGwgfHwgYiA9PSBudWxsID8gTmFOXG4gICAgOiBiIDwgYSA/IC0xXG4gICAgOiBiID4gYSA/IDFcbiAgICA6IGIgPj0gYSA/IDBcbiAgICA6IE5hTjtcbn1cbiJdLCJuYW1lcyI6WyJkZXNjZW5kaW5nIiwiYSIsImIiLCJOYU4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/descending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/deviation.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/deviation.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ deviation)\n/* harmony export */ });\n/* harmony import */ var _variance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./variance.js */ \"(ssr)/./node_modules/d3-array/src/variance.js\");\n\nfunction deviation(values, valueof) {\n    const v = (0,_variance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, valueof);\n    return v ? Math.sqrt(v) : v;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2RldmlhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQztBQUV0QixTQUFTQyxVQUFVQyxNQUFNLEVBQUVDLE9BQU87SUFDL0MsTUFBTUMsSUFBSUosd0RBQVFBLENBQUNFLFFBQVFDO0lBQzNCLE9BQU9DLElBQUlDLEtBQUtDLElBQUksQ0FBQ0YsS0FBS0E7QUFDNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvZGV2aWF0aW9uLmpzP2I0OTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHZhcmlhbmNlIGZyb20gXCIuL3ZhcmlhbmNlLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGRldmlhdGlvbih2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgY29uc3QgdiA9IHZhcmlhbmNlKHZhbHVlcywgdmFsdWVvZik7XG4gIHJldHVybiB2ID8gTWF0aC5zcXJ0KHYpIDogdjtcbn1cbiJdLCJuYW1lcyI6WyJ2YXJpYW5jZSIsImRldmlhdGlvbiIsInZhbHVlcyIsInZhbHVlb2YiLCJ2IiwiTWF0aCIsInNxcnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/deviation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/difference.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-array/src/difference.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ difference)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\nfunction difference(values, ...others) {\n    values = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n    for (const other of others){\n        for (const value of other){\n            values.delete(value);\n        }\n    }\n    return values;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2RpZmZlcmVuY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7QUFFckIsU0FBU0MsV0FBV0MsTUFBTSxFQUFFLEdBQUdDLE1BQU07SUFDbERELFNBQVMsSUFBSUYsZ0RBQVNBLENBQUNFO0lBQ3ZCLEtBQUssTUFBTUUsU0FBU0QsT0FBUTtRQUMxQixLQUFLLE1BQU1FLFNBQVNELE1BQU87WUFDekJGLE9BQU9JLE1BQU0sQ0FBQ0Q7UUFDaEI7SUFDRjtJQUNBLE9BQU9IO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvZGlmZmVyZW5jZS5qcz83OGVmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7SW50ZXJuU2V0fSBmcm9tIFwiaW50ZXJubWFwXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGRpZmZlcmVuY2UodmFsdWVzLCAuLi5vdGhlcnMpIHtcbiAgdmFsdWVzID0gbmV3IEludGVyblNldCh2YWx1ZXMpO1xuICBmb3IgKGNvbnN0IG90aGVyIG9mIG90aGVycykge1xuICAgIGZvciAoY29uc3QgdmFsdWUgb2Ygb3RoZXIpIHtcbiAgICAgIHZhbHVlcy5kZWxldGUodmFsdWUpO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdmFsdWVzO1xufVxuIl0sIm5hbWVzIjpbIkludGVyblNldCIsImRpZmZlcmVuY2UiLCJ2YWx1ZXMiLCJvdGhlcnMiLCJvdGhlciIsInZhbHVlIiwiZGVsZXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/difference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/disjoint.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/disjoint.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ disjoint)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\nfunction disjoint(values, other) {\n    const iterator = other[Symbol.iterator](), set = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet();\n    for (const v of values){\n        if (set.has(v)) return false;\n        let value, done;\n        while({ value, done } = iterator.next()){\n            if (done) break;\n            if (Object.is(v, value)) return false;\n            set.add(value);\n        }\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Rpc2pvaW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DO0FBRXJCLFNBQVNDLFNBQVNDLE1BQU0sRUFBRUMsS0FBSztJQUM1QyxNQUFNQyxXQUFXRCxLQUFLLENBQUNFLE9BQU9ELFFBQVEsQ0FBQyxJQUFJRSxNQUFNLElBQUlOLGdEQUFTQTtJQUM5RCxLQUFLLE1BQU1PLEtBQUtMLE9BQVE7UUFDdEIsSUFBSUksSUFBSUUsR0FBRyxDQUFDRCxJQUFJLE9BQU87UUFDdkIsSUFBSUUsT0FBT0M7UUFDWCxNQUFRLEVBQUNELEtBQUssRUFBRUMsSUFBSSxFQUFDLEdBQUdOLFNBQVNPLElBQUksR0FBSztZQUN4QyxJQUFJRCxNQUFNO1lBQ1YsSUFBSUUsT0FBT0MsRUFBRSxDQUFDTixHQUFHRSxRQUFRLE9BQU87WUFDaENILElBQUlRLEdBQUcsQ0FBQ0w7UUFDVjtJQUNGO0lBQ0EsT0FBTztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Rpc2pvaW50LmpzPzI1MjUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtJbnRlcm5TZXR9IGZyb20gXCJpbnRlcm5tYXBcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZGlzam9pbnQodmFsdWVzLCBvdGhlcikge1xuICBjb25zdCBpdGVyYXRvciA9IG90aGVyW1N5bWJvbC5pdGVyYXRvcl0oKSwgc2V0ID0gbmV3IEludGVyblNldCgpO1xuICBmb3IgKGNvbnN0IHYgb2YgdmFsdWVzKSB7XG4gICAgaWYgKHNldC5oYXModikpIHJldHVybiBmYWxzZTtcbiAgICBsZXQgdmFsdWUsIGRvbmU7XG4gICAgd2hpbGUgKCh7dmFsdWUsIGRvbmV9ID0gaXRlcmF0b3IubmV4dCgpKSkge1xuICAgICAgaWYgKGRvbmUpIGJyZWFrO1xuICAgICAgaWYgKE9iamVjdC5pcyh2LCB2YWx1ZSkpIHJldHVybiBmYWxzZTtcbiAgICAgIHNldC5hZGQodmFsdWUpO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cbiJdLCJuYW1lcyI6WyJJbnRlcm5TZXQiLCJkaXNqb2ludCIsInZhbHVlcyIsIm90aGVyIiwiaXRlcmF0b3IiLCJTeW1ib2wiLCJzZXQiLCJ2IiwiaGFzIiwidmFsdWUiLCJkb25lIiwibmV4dCIsIk9iamVjdCIsImlzIiwiYWRkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/disjoint.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/every.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/every.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ every)\n/* harmony export */ });\nfunction every(values, test) {\n    if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n    let index = -1;\n    for (const value of values){\n        if (!test(value, ++index, values)) {\n            return false;\n        }\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2V2ZXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxNQUFNQyxNQUFNLEVBQUVDLElBQUk7SUFDeEMsSUFBSSxPQUFPQSxTQUFTLFlBQVksTUFBTSxJQUFJQyxVQUFVO0lBQ3BELElBQUlDLFFBQVEsQ0FBQztJQUNiLEtBQUssTUFBTUMsU0FBU0osT0FBUTtRQUMxQixJQUFJLENBQUNDLEtBQUtHLE9BQU8sRUFBRUQsT0FBT0gsU0FBUztZQUNqQyxPQUFPO1FBQ1Q7SUFDRjtJQUNBLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9ldmVyeS5qcz8zZWUxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGV2ZXJ5KHZhbHVlcywgdGVzdCkge1xuICBpZiAodHlwZW9mIHRlc3QgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcInRlc3QgaXMgbm90IGEgZnVuY3Rpb25cIik7XG4gIGxldCBpbmRleCA9IC0xO1xuICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgIGlmICghdGVzdCh2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cbiJdLCJuYW1lcyI6WyJldmVyeSIsInZhbHVlcyIsInRlc3QiLCJUeXBlRXJyb3IiLCJpbmRleCIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/every.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/extent.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/extent.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ extent)\n/* harmony export */ });\nfunction extent(values, valueof) {\n    let min;\n    let max;\n    if (valueof === undefined) {\n        for (const value of values){\n            if (value != null) {\n                if (min === undefined) {\n                    if (value >= value) min = max = value;\n                } else {\n                    if (min > value) min = value;\n                    if (max < value) max = value;\n                }\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null) {\n                if (min === undefined) {\n                    if (value >= value) min = max = value;\n                } else {\n                    if (min > value) min = value;\n                    if (max < value) max = value;\n                }\n            }\n        }\n    }\n    return [\n        min,\n        max\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/extent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/filter.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/filter.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ filter)\n/* harmony export */ });\nfunction filter(values, test) {\n    if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n    const array = [];\n    let index = -1;\n    for (const value of values){\n        if (test(value, ++index, values)) {\n            array.push(value);\n        }\n    }\n    return array;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2ZpbHRlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsT0FBT0MsTUFBTSxFQUFFQyxJQUFJO0lBQ3pDLElBQUksT0FBT0EsU0FBUyxZQUFZLE1BQU0sSUFBSUMsVUFBVTtJQUNwRCxNQUFNQyxRQUFRLEVBQUU7SUFDaEIsSUFBSUMsUUFBUSxDQUFDO0lBQ2IsS0FBSyxNQUFNQyxTQUFTTCxPQUFRO1FBQzFCLElBQUlDLEtBQUtJLE9BQU8sRUFBRUQsT0FBT0osU0FBUztZQUNoQ0csTUFBTUcsSUFBSSxDQUFDRDtRQUNiO0lBQ0Y7SUFDQSxPQUFPRjtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2ZpbHRlci5qcz9jZGM1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGZpbHRlcih2YWx1ZXMsIHRlc3QpIHtcbiAgaWYgKHR5cGVvZiB0ZXN0ICE9PSBcImZ1bmN0aW9uXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJ0ZXN0IGlzIG5vdCBhIGZ1bmN0aW9uXCIpO1xuICBjb25zdCBhcnJheSA9IFtdO1xuICBsZXQgaW5kZXggPSAtMTtcbiAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICBpZiAodGVzdCh2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkge1xuICAgICAgYXJyYXkucHVzaCh2YWx1ZSk7XG4gICAgfVxuICB9XG4gIHJldHVybiBhcnJheTtcbn1cbiJdLCJuYW1lcyI6WyJmaWx0ZXIiLCJ2YWx1ZXMiLCJ0ZXN0IiwiVHlwZUVycm9yIiwiYXJyYXkiLCJpbmRleCIsInZhbHVlIiwicHVzaCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/filter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/fsum.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/fsum.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Adder: () => (/* binding */ Adder),\n/* harmony export */   fcumsum: () => (/* binding */ fcumsum),\n/* harmony export */   fsum: () => (/* binding */ fsum)\n/* harmony export */ });\n// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nclass Adder {\n    constructor(){\n        this._partials = new Float64Array(32);\n        this._n = 0;\n    }\n    add(x) {\n        const p = this._partials;\n        let i = 0;\n        for(let j = 0; j < this._n && j < 32; j++){\n            const y = p[j], hi = x + y, lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n            if (lo) p[i++] = lo;\n            x = hi;\n        }\n        p[i] = x;\n        this._n = i + 1;\n        return this;\n    }\n    valueOf() {\n        const p = this._partials;\n        let n = this._n, x, y, lo, hi = 0;\n        if (n > 0) {\n            hi = p[--n];\n            while(n > 0){\n                x = hi;\n                y = p[--n];\n                hi = x + y;\n                lo = y - (hi - x);\n                if (lo) break;\n            }\n            if (n > 0 && (lo < 0 && p[n - 1] < 0 || lo > 0 && p[n - 1] > 0)) {\n                y = lo * 2;\n                x = hi + y;\n                if (y == x - hi) hi = x;\n            }\n        }\n        return hi;\n    }\n}\nfunction fsum(values, valueof) {\n    const adder = new Adder();\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value = +value) {\n                adder.add(value);\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if (value = +valueof(value, ++index, values)) {\n                adder.add(value);\n            }\n        }\n    }\n    return +adder;\n}\nfunction fcumsum(values, valueof) {\n    const adder = new Adder();\n    let index = -1;\n    return Float64Array.from(values, valueof === undefined ? (v)=>adder.add(+v || 0) : (v)=>adder.add(+valueof(v, ++index, values) || 0));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/fsum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/greatest.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/greatest.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ greatest)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n\nfunction greatest(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    let max;\n    let defined = false;\n    if (compare.length === 1) {\n        let maxValue;\n        for (const element of values){\n            const value = compare(element);\n            if (defined ? (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, maxValue) > 0 : (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, value) === 0) {\n                max = element;\n                maxValue = value;\n                defined = true;\n            }\n        }\n    } else {\n        for (const value of values){\n            if (defined ? compare(value, max) > 0 : compare(value, value) === 0) {\n                max = value;\n                defined = true;\n            }\n        }\n    }\n    return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyZWF0ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDO0FBRXhCLFNBQVNDLFNBQVNDLE1BQU0sRUFBRUMsVUFBVUgscURBQVM7SUFDMUQsSUFBSUk7SUFDSixJQUFJQyxVQUFVO0lBQ2QsSUFBSUYsUUFBUUcsTUFBTSxLQUFLLEdBQUc7UUFDeEIsSUFBSUM7UUFDSixLQUFLLE1BQU1DLFdBQVdOLE9BQVE7WUFDNUIsTUFBTU8sUUFBUU4sUUFBUUs7WUFDdEIsSUFBSUgsVUFDRUwseURBQVNBLENBQUNTLE9BQU9GLFlBQVksSUFDN0JQLHlEQUFTQSxDQUFDUyxPQUFPQSxXQUFXLEdBQUc7Z0JBQ25DTCxNQUFNSTtnQkFDTkQsV0FBV0U7Z0JBQ1hKLFVBQVU7WUFDWjtRQUNGO0lBQ0YsT0FBTztRQUNMLEtBQUssTUFBTUksU0FBU1AsT0FBUTtZQUMxQixJQUFJRyxVQUNFRixRQUFRTSxPQUFPTCxPQUFPLElBQ3RCRCxRQUFRTSxPQUFPQSxXQUFXLEdBQUc7Z0JBQ2pDTCxNQUFNSztnQkFDTkosVUFBVTtZQUNaO1FBQ0Y7SUFDRjtJQUNBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvZ3JlYXRlc3QuanM/ZjViNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBncmVhdGVzdCh2YWx1ZXMsIGNvbXBhcmUgPSBhc2NlbmRpbmcpIHtcbiAgbGV0IG1heDtcbiAgbGV0IGRlZmluZWQgPSBmYWxzZTtcbiAgaWYgKGNvbXBhcmUubGVuZ3RoID09PSAxKSB7XG4gICAgbGV0IG1heFZhbHVlO1xuICAgIGZvciAoY29uc3QgZWxlbWVudCBvZiB2YWx1ZXMpIHtcbiAgICAgIGNvbnN0IHZhbHVlID0gY29tcGFyZShlbGVtZW50KTtcbiAgICAgIGlmIChkZWZpbmVkXG4gICAgICAgICAgPyBhc2NlbmRpbmcodmFsdWUsIG1heFZhbHVlKSA+IDBcbiAgICAgICAgICA6IGFzY2VuZGluZyh2YWx1ZSwgdmFsdWUpID09PSAwKSB7XG4gICAgICAgIG1heCA9IGVsZW1lbnQ7XG4gICAgICAgIG1heFZhbHVlID0gdmFsdWU7XG4gICAgICAgIGRlZmluZWQgPSB0cnVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKGRlZmluZWRcbiAgICAgICAgICA/IGNvbXBhcmUodmFsdWUsIG1heCkgPiAwXG4gICAgICAgICAgOiBjb21wYXJlKHZhbHVlLCB2YWx1ZSkgPT09IDApIHtcbiAgICAgICAgbWF4ID0gdmFsdWU7XG4gICAgICAgIGRlZmluZWQgPSB0cnVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gbWF4O1xufVxuIl0sIm5hbWVzIjpbImFzY2VuZGluZyIsImdyZWF0ZXN0IiwidmFsdWVzIiwiY29tcGFyZSIsIm1heCIsImRlZmluZWQiLCJsZW5ndGgiLCJtYXhWYWx1ZSIsImVsZW1lbnQiLCJ2YWx1ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/greatest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/greatestIndex.js":
/*!****************************************************!*\
  !*** ./node_modules/d3-array/src/greatestIndex.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ greatestIndex)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/./node_modules/d3-array/src/maxIndex.js\");\n\n\nfunction greatestIndex(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    if (compare.length === 1) return (0,_maxIndex_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, compare);\n    let maxValue;\n    let max = -1;\n    let index = -1;\n    for (const value of values){\n        ++index;\n        if (max < 0 ? compare(value, value) === 0 : compare(value, maxValue) > 0) {\n            maxValue = value;\n            max = index;\n        }\n    }\n    return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyZWF0ZXN0SW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVDO0FBQ0Y7QUFFdEIsU0FBU0UsY0FBY0MsTUFBTSxFQUFFQyxVQUFVSixxREFBUztJQUMvRCxJQUFJSSxRQUFRQyxNQUFNLEtBQUssR0FBRyxPQUFPSix3REFBUUEsQ0FBQ0UsUUFBUUM7SUFDbEQsSUFBSUU7SUFDSixJQUFJQyxNQUFNLENBQUM7SUFDWCxJQUFJQyxRQUFRLENBQUM7SUFDYixLQUFLLE1BQU1DLFNBQVNOLE9BQVE7UUFDMUIsRUFBRUs7UUFDRixJQUFJRCxNQUFNLElBQ0pILFFBQVFLLE9BQU9BLFdBQVcsSUFDMUJMLFFBQVFLLE9BQU9ILFlBQVksR0FBRztZQUNsQ0EsV0FBV0c7WUFDWEYsTUFBTUM7UUFDUjtJQUNGO0lBQ0EsT0FBT0Q7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9ncmVhdGVzdEluZGV4LmpzPzM4ZjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFzY2VuZGluZyBmcm9tIFwiLi9hc2NlbmRpbmcuanNcIjtcbmltcG9ydCBtYXhJbmRleCBmcm9tIFwiLi9tYXhJbmRleC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBncmVhdGVzdEluZGV4KHZhbHVlcywgY29tcGFyZSA9IGFzY2VuZGluZykge1xuICBpZiAoY29tcGFyZS5sZW5ndGggPT09IDEpIHJldHVybiBtYXhJbmRleCh2YWx1ZXMsIGNvbXBhcmUpO1xuICBsZXQgbWF4VmFsdWU7XG4gIGxldCBtYXggPSAtMTtcbiAgbGV0IGluZGV4ID0gLTE7XG4gIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgKytpbmRleDtcbiAgICBpZiAobWF4IDwgMFxuICAgICAgICA/IGNvbXBhcmUodmFsdWUsIHZhbHVlKSA9PT0gMFxuICAgICAgICA6IGNvbXBhcmUodmFsdWUsIG1heFZhbHVlKSA+IDApIHtcbiAgICAgIG1heFZhbHVlID0gdmFsdWU7XG4gICAgICBtYXggPSBpbmRleDtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1heDtcbn1cbiJdLCJuYW1lcyI6WyJhc2NlbmRpbmciLCJtYXhJbmRleCIsImdyZWF0ZXN0SW5kZXgiLCJ2YWx1ZXMiLCJjb21wYXJlIiwibGVuZ3RoIiwibWF4VmFsdWUiLCJtYXgiLCJpbmRleCIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/greatestIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/group.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/group.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ group),\n/* harmony export */   flatGroup: () => (/* binding */ flatGroup),\n/* harmony export */   flatRollup: () => (/* binding */ flatRollup),\n/* harmony export */   groups: () => (/* binding */ groups),\n/* harmony export */   index: () => (/* binding */ index),\n/* harmony export */   indexes: () => (/* binding */ indexes),\n/* harmony export */   rollup: () => (/* binding */ rollup),\n/* harmony export */   rollups: () => (/* binding */ rollups)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/./node_modules/d3-array/src/identity.js\");\n\n\nfunction group(values, ...keys) {\n    return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], keys);\n}\nfunction groups(values, ...keys) {\n    return nest(values, Array.from, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], keys);\n}\nfunction flatten(groups, keys) {\n    for(let i = 1, n = keys.length; i < n; ++i){\n        groups = groups.flatMap((g)=>g.pop().map(([key, value])=>[\n                    ...g,\n                    key,\n                    value\n                ]));\n    }\n    return groups;\n}\nfunction flatGroup(values, ...keys) {\n    return flatten(groups(values, ...keys), keys);\n}\nfunction flatRollup(values, reduce, ...keys) {\n    return flatten(rollups(values, reduce, ...keys), keys);\n}\nfunction rollup(values, reduce, ...keys) {\n    return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], reduce, keys);\n}\nfunction rollups(values, reduce, ...keys) {\n    return nest(values, Array.from, reduce, keys);\n}\nfunction index(values, ...keys) {\n    return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], unique, keys);\n}\nfunction indexes(values, ...keys) {\n    return nest(values, Array.from, unique, keys);\n}\nfunction unique(values) {\n    if (values.length !== 1) throw new Error(\"duplicate key\");\n    return values[0];\n}\nfunction nest(values, map, reduce, keys) {\n    return function regroup(values, i) {\n        if (i >= keys.length) return reduce(values);\n        const groups = new internmap__WEBPACK_IMPORTED_MODULE_1__.InternMap();\n        const keyof = keys[i++];\n        let index = -1;\n        for (const value of values){\n            const key = keyof(value, ++index, values);\n            const group = groups.get(key);\n            if (group) group.push(value);\n            else groups.set(key, [\n                value\n            ]);\n        }\n        for (const [key, values] of groups){\n            groups.set(key, regroup(values, i));\n        }\n        return map(groups);\n    }(values, 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/group.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/groupSort.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/groupSort.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ groupSort)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _group_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./group.js */ \"(ssr)/./node_modules/d3-array/src/group.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n\n\n\nfunction groupSort(values, reduce, key) {\n    return (reduce.length !== 2 ? (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_group_js__WEBPACK_IMPORTED_MODULE_1__.rollup)(values, reduce, key), ([ak, av], [bk, bv])=>(0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(av, bv) || (0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(ak, bk)) : (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_group_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, key), ([ak, av], [bk, bv])=>reduce(av, bv) || (0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(ak, bk))).map(([key])=>key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyb3VwU29ydC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVDO0FBQ0U7QUFDWjtBQUVkLFNBQVNJLFVBQVVDLE1BQU0sRUFBRUMsTUFBTSxFQUFFQyxHQUFHO0lBQ25ELE9BQU8sQ0FBQ0QsT0FBT0UsTUFBTSxLQUFLLElBQ3RCTCxvREFBSUEsQ0FBQ0QsaURBQU1BLENBQUNHLFFBQVFDLFFBQVFDLE1BQU8sQ0FBQyxDQUFDRSxJQUFJQyxHQUFHLEVBQUUsQ0FBQ0MsSUFBSUMsR0FBRyxHQUFLWix5REFBU0EsQ0FBQ1UsSUFBSUUsT0FBT1oseURBQVNBLENBQUNTLElBQUlFLE9BQzlGUixvREFBSUEsQ0FBQ0YscURBQUtBLENBQUNJLFFBQVFFLE1BQU8sQ0FBQyxDQUFDRSxJQUFJQyxHQUFHLEVBQUUsQ0FBQ0MsSUFBSUMsR0FBRyxHQUFLTixPQUFPSSxJQUFJRSxPQUFPWix5REFBU0EsQ0FBQ1MsSUFBSUUsSUFBSSxFQUN2RkUsR0FBRyxDQUFDLENBQUMsQ0FBQ04sSUFBSSxHQUFLQTtBQUNwQiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9ncm91cFNvcnQuanM/NWY2OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuaW1wb3J0IGdyb3VwLCB7cm9sbHVwfSBmcm9tIFwiLi9ncm91cC5qc1wiO1xuaW1wb3J0IHNvcnQgZnJvbSBcIi4vc29ydC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBncm91cFNvcnQodmFsdWVzLCByZWR1Y2UsIGtleSkge1xuICByZXR1cm4gKHJlZHVjZS5sZW5ndGggIT09IDJcbiAgICA/IHNvcnQocm9sbHVwKHZhbHVlcywgcmVkdWNlLCBrZXkpLCAoKFthaywgYXZdLCBbYmssIGJ2XSkgPT4gYXNjZW5kaW5nKGF2LCBidikgfHwgYXNjZW5kaW5nKGFrLCBiaykpKVxuICAgIDogc29ydChncm91cCh2YWx1ZXMsIGtleSksICgoW2FrLCBhdl0sIFtiaywgYnZdKSA9PiByZWR1Y2UoYXYsIGJ2KSB8fCBhc2NlbmRpbmcoYWssIGJrKSkpKVxuICAgIC5tYXAoKFtrZXldKSA9PiBrZXkpO1xufVxuIl0sIm5hbWVzIjpbImFzY2VuZGluZyIsImdyb3VwIiwicm9sbHVwIiwic29ydCIsImdyb3VwU29ydCIsInZhbHVlcyIsInJlZHVjZSIsImtleSIsImxlbmd0aCIsImFrIiwiYXYiLCJiayIsImJ2IiwibWFwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/groupSort.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/identity.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/identity.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ identity)\n/* harmony export */ });\nfunction identity(x) {\n    return x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2lkZW50aXR5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxTQUFTQyxDQUFDO0lBQ2hDLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvaWRlbnRpdHkuanM/MjlmNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBpZGVudGl0eSh4KSB7XG4gIHJldHVybiB4O1xufVxuIl0sIm5hbWVzIjpbImlkZW50aXR5IiwieCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Adder: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.Adder),\n/* harmony export */   InternMap: () => (/* reexport safe */ internmap__WEBPACK_IMPORTED_MODULE_56__.InternMap),\n/* harmony export */   InternSet: () => (/* reexport safe */ internmap__WEBPACK_IMPORTED_MODULE_56__.InternSet),\n/* harmony export */   ascending: () => (/* reexport safe */ _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   bin: () => (/* reexport safe */ _bin_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   bisect: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   bisectCenter: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectCenter),\n/* harmony export */   bisectLeft: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectLeft),\n/* harmony export */   bisectRight: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectRight),\n/* harmony export */   bisector: () => (/* reexport safe */ _bisector_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   blur: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blur),\n/* harmony export */   blur2: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blur2),\n/* harmony export */   blurImage: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blurImage),\n/* harmony export */   count: () => (/* reexport safe */ _count_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   cross: () => (/* reexport safe */ _cross_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   cumsum: () => (/* reexport safe */ _cumsum_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   descending: () => (/* reexport safe */ _descending_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   deviation: () => (/* reexport safe */ _deviation_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   difference: () => (/* reexport safe */ _difference_js__WEBPACK_IMPORTED_MODULE_50__[\"default\"]),\n/* harmony export */   disjoint: () => (/* reexport safe */ _disjoint_js__WEBPACK_IMPORTED_MODULE_51__[\"default\"]),\n/* harmony export */   every: () => (/* reexport safe */ _every_js__WEBPACK_IMPORTED_MODULE_43__[\"default\"]),\n/* harmony export */   extent: () => (/* reexport safe */ _extent_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   fcumsum: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.fcumsum),\n/* harmony export */   filter: () => (/* reexport safe */ _filter_js__WEBPACK_IMPORTED_MODULE_45__[\"default\"]),\n/* harmony export */   flatGroup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.flatGroup),\n/* harmony export */   flatRollup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.flatRollup),\n/* harmony export */   fsum: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.fsum),\n/* harmony export */   greatest: () => (/* reexport safe */ _greatest_js__WEBPACK_IMPORTED_MODULE_34__[\"default\"]),\n/* harmony export */   greatestIndex: () => (/* reexport safe */ _greatestIndex_js__WEBPACK_IMPORTED_MODULE_35__[\"default\"]),\n/* harmony export */   group: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   groupSort: () => (/* reexport safe */ _groupSort_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   groups: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.groups),\n/* harmony export */   histogram: () => (/* reexport safe */ _bin_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   index: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.index),\n/* harmony export */   indexes: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.indexes),\n/* harmony export */   intersection: () => (/* reexport safe */ _intersection_js__WEBPACK_IMPORTED_MODULE_52__[\"default\"]),\n/* harmony export */   least: () => (/* reexport safe */ _least_js__WEBPACK_IMPORTED_MODULE_32__[\"default\"]),\n/* harmony export */   leastIndex: () => (/* reexport safe */ _leastIndex_js__WEBPACK_IMPORTED_MODULE_33__[\"default\"]),\n/* harmony export */   map: () => (/* reexport safe */ _map_js__WEBPACK_IMPORTED_MODULE_46__[\"default\"]),\n/* harmony export */   max: () => (/* reexport safe */ _max_js__WEBPACK_IMPORTED_MODULE_17__[\"default\"]),\n/* harmony export */   maxIndex: () => (/* reexport safe */ _maxIndex_js__WEBPACK_IMPORTED_MODULE_18__[\"default\"]),\n/* harmony export */   mean: () => (/* reexport safe */ _mean_js__WEBPACK_IMPORTED_MODULE_19__[\"default\"]),\n/* harmony export */   median: () => (/* reexport safe */ _median_js__WEBPACK_IMPORTED_MODULE_20__[\"default\"]),\n/* harmony export */   medianIndex: () => (/* reexport safe */ _median_js__WEBPACK_IMPORTED_MODULE_20__.medianIndex),\n/* harmony export */   merge: () => (/* reexport safe */ _merge_js__WEBPACK_IMPORTED_MODULE_21__[\"default\"]),\n/* harmony export */   min: () => (/* reexport safe */ _min_js__WEBPACK_IMPORTED_MODULE_22__[\"default\"]),\n/* harmony export */   minIndex: () => (/* reexport safe */ _minIndex_js__WEBPACK_IMPORTED_MODULE_23__[\"default\"]),\n/* harmony export */   mode: () => (/* reexport safe */ _mode_js__WEBPACK_IMPORTED_MODULE_24__[\"default\"]),\n/* harmony export */   nice: () => (/* reexport safe */ _nice_js__WEBPACK_IMPORTED_MODULE_25__[\"default\"]),\n/* harmony export */   pairs: () => (/* reexport safe */ _pairs_js__WEBPACK_IMPORTED_MODULE_26__[\"default\"]),\n/* harmony export */   permute: () => (/* reexport safe */ _permute_js__WEBPACK_IMPORTED_MODULE_27__[\"default\"]),\n/* harmony export */   quantile: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__[\"default\"]),\n/* harmony export */   quantileIndex: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__.quantileIndex),\n/* harmony export */   quantileSorted: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__.quantileSorted),\n/* harmony export */   quickselect: () => (/* reexport safe */ _quickselect_js__WEBPACK_IMPORTED_MODULE_29__[\"default\"]),\n/* harmony export */   range: () => (/* reexport safe */ _range_js__WEBPACK_IMPORTED_MODULE_30__[\"default\"]),\n/* harmony export */   rank: () => (/* reexport safe */ _rank_js__WEBPACK_IMPORTED_MODULE_31__[\"default\"]),\n/* harmony export */   reduce: () => (/* reexport safe */ _reduce_js__WEBPACK_IMPORTED_MODULE_47__[\"default\"]),\n/* harmony export */   reverse: () => (/* reexport safe */ _reverse_js__WEBPACK_IMPORTED_MODULE_48__[\"default\"]),\n/* harmony export */   rollup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.rollup),\n/* harmony export */   rollups: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.rollups),\n/* harmony export */   scan: () => (/* reexport safe */ _scan_js__WEBPACK_IMPORTED_MODULE_36__[\"default\"]),\n/* harmony export */   shuffle: () => (/* reexport safe */ _shuffle_js__WEBPACK_IMPORTED_MODULE_37__[\"default\"]),\n/* harmony export */   shuffler: () => (/* reexport safe */ _shuffle_js__WEBPACK_IMPORTED_MODULE_37__.shuffler),\n/* harmony export */   some: () => (/* reexport safe */ _some_js__WEBPACK_IMPORTED_MODULE_44__[\"default\"]),\n/* harmony export */   sort: () => (/* reexport safe */ _sort_js__WEBPACK_IMPORTED_MODULE_49__[\"default\"]),\n/* harmony export */   subset: () => (/* reexport safe */ _subset_js__WEBPACK_IMPORTED_MODULE_53__[\"default\"]),\n/* harmony export */   sum: () => (/* reexport safe */ _sum_js__WEBPACK_IMPORTED_MODULE_38__[\"default\"]),\n/* harmony export */   superset: () => (/* reexport safe */ _superset_js__WEBPACK_IMPORTED_MODULE_54__[\"default\"]),\n/* harmony export */   thresholdFreedmanDiaconis: () => (/* reexport safe */ _threshold_freedmanDiaconis_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   thresholdScott: () => (/* reexport safe */ _threshold_scott_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   thresholdSturges: () => (/* reexport safe */ _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"]),\n/* harmony export */   tickIncrement: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__.tickIncrement),\n/* harmony export */   tickStep: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__.tickStep),\n/* harmony export */   ticks: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__[\"default\"]),\n/* harmony export */   transpose: () => (/* reexport safe */ _transpose_js__WEBPACK_IMPORTED_MODULE_40__[\"default\"]),\n/* harmony export */   union: () => (/* reexport safe */ _union_js__WEBPACK_IMPORTED_MODULE_55__[\"default\"]),\n/* harmony export */   variance: () => (/* reexport safe */ _variance_js__WEBPACK_IMPORTED_MODULE_41__[\"default\"]),\n/* harmony export */   zip: () => (/* reexport safe */ _zip_js__WEBPACK_IMPORTED_MODULE_42__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _bisect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bisect.js */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _bisector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bisector.js */ \"(ssr)/./node_modules/d3-array/src/bisector.js\");\n/* harmony import */ var _blur_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blur.js */ \"(ssr)/./node_modules/d3-array/src/blur.js\");\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n/* harmony import */ var _cross_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./cross.js */ \"(ssr)/./node_modules/d3-array/src/cross.js\");\n/* harmony import */ var _cumsum_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./cumsum.js */ \"(ssr)/./node_modules/d3-array/src/cumsum.js\");\n/* harmony import */ var _descending_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./descending.js */ \"(ssr)/./node_modules/d3-array/src/descending.js\");\n/* harmony import */ var _deviation_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./deviation.js */ \"(ssr)/./node_modules/d3-array/src/deviation.js\");\n/* harmony import */ var _extent_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./extent.js */ \"(ssr)/./node_modules/d3-array/src/extent.js\");\n/* harmony import */ var _fsum_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./fsum.js */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _group_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./group.js */ \"(ssr)/./node_modules/d3-array/src/group.js\");\n/* harmony import */ var _groupSort_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./groupSort.js */ \"(ssr)/./node_modules/d3-array/src/groupSort.js\");\n/* harmony import */ var _bin_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./bin.js */ \"(ssr)/./node_modules/d3-array/src/bin.js\");\n/* harmony import */ var _threshold_freedmanDiaconis_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./threshold/freedmanDiaconis.js */ \"(ssr)/./node_modules/d3-array/src/threshold/freedmanDiaconis.js\");\n/* harmony import */ var _threshold_scott_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./threshold/scott.js */ \"(ssr)/./node_modules/d3-array/src/threshold/scott.js\");\n/* harmony import */ var _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./threshold/sturges.js */ \"(ssr)/./node_modules/d3-array/src/threshold/sturges.js\");\n/* harmony import */ var _max_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./max.js */ \"(ssr)/./node_modules/d3-array/src/max.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/./node_modules/d3-array/src/maxIndex.js\");\n/* harmony import */ var _mean_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./mean.js */ \"(ssr)/./node_modules/d3-array/src/mean.js\");\n/* harmony import */ var _median_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./median.js */ \"(ssr)/./node_modules/d3-array/src/median.js\");\n/* harmony import */ var _merge_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./merge.js */ \"(ssr)/./node_modules/d3-array/src/merge.js\");\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./min.js */ \"(ssr)/./node_modules/d3-array/src/min.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/./node_modules/d3-array/src/minIndex.js\");\n/* harmony import */ var _mode_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./mode.js */ \"(ssr)/./node_modules/d3-array/src/mode.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/./node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _pairs_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./pairs.js */ \"(ssr)/./node_modules/d3-array/src/pairs.js\");\n/* harmony import */ var _permute_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./permute.js */ \"(ssr)/./node_modules/d3-array/src/permute.js\");\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./quantile.js */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n/* harmony import */ var _quickselect_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./quickselect.js */ \"(ssr)/./node_modules/d3-array/src/quickselect.js\");\n/* harmony import */ var _range_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./range.js */ \"(ssr)/./node_modules/d3-array/src/range.js\");\n/* harmony import */ var _rank_js__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./rank.js */ \"(ssr)/./node_modules/d3-array/src/rank.js\");\n/* harmony import */ var _least_js__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./least.js */ \"(ssr)/./node_modules/d3-array/src/least.js\");\n/* harmony import */ var _leastIndex_js__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./leastIndex.js */ \"(ssr)/./node_modules/d3-array/src/leastIndex.js\");\n/* harmony import */ var _greatest_js__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./greatest.js */ \"(ssr)/./node_modules/d3-array/src/greatest.js\");\n/* harmony import */ var _greatestIndex_js__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! ./greatestIndex.js */ \"(ssr)/./node_modules/d3-array/src/greatestIndex.js\");\n/* harmony import */ var _scan_js__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./scan.js */ \"(ssr)/./node_modules/d3-array/src/scan.js\");\n/* harmony import */ var _shuffle_js__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! ./shuffle.js */ \"(ssr)/./node_modules/d3-array/src/shuffle.js\");\n/* harmony import */ var _sum_js__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! ./sum.js */ \"(ssr)/./node_modules/d3-array/src/sum.js\");\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _transpose_js__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! ./transpose.js */ \"(ssr)/./node_modules/d3-array/src/transpose.js\");\n/* harmony import */ var _variance_js__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! ./variance.js */ \"(ssr)/./node_modules/d3-array/src/variance.js\");\n/* harmony import */ var _zip_js__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! ./zip.js */ \"(ssr)/./node_modules/d3-array/src/zip.js\");\n/* harmony import */ var _every_js__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! ./every.js */ \"(ssr)/./node_modules/d3-array/src/every.js\");\n/* harmony import */ var _some_js__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! ./some.js */ \"(ssr)/./node_modules/d3-array/src/some.js\");\n/* harmony import */ var _filter_js__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! ./filter.js */ \"(ssr)/./node_modules/d3-array/src/filter.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/d3-array/src/map.js\");\n/* harmony import */ var _reduce_js__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! ./reduce.js */ \"(ssr)/./node_modules/d3-array/src/reduce.js\");\n/* harmony import */ var _reverse_js__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! ./reverse.js */ \"(ssr)/./node_modules/d3-array/src/reverse.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n/* harmony import */ var _difference_js__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(/*! ./difference.js */ \"(ssr)/./node_modules/d3-array/src/difference.js\");\n/* harmony import */ var _disjoint_js__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(/*! ./disjoint.js */ \"(ssr)/./node_modules/d3-array/src/disjoint.js\");\n/* harmony import */ var _intersection_js__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(/*! ./intersection.js */ \"(ssr)/./node_modules/d3-array/src/intersection.js\");\n/* harmony import */ var _subset_js__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(/*! ./subset.js */ \"(ssr)/./node_modules/d3-array/src/subset.js\");\n/* harmony import */ var _superset_js__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(/*! ./superset.js */ \"(ssr)/./node_modules/d3-array/src/superset.js\");\n/* harmony import */ var _union_js__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(/*! ./union.js */ \"(ssr)/./node_modules/d3-array/src/union.js\");\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Deprecated; use bin.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Deprecated; use leastIndex.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/intersection.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-array/src/intersection.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ intersection)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\nfunction intersection(values, ...others) {\n    values = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n    others = others.map(set);\n    out: for (const value of values){\n        for (const other of others){\n            if (!other.has(value)) {\n                values.delete(value);\n                continue out;\n            }\n        }\n    }\n    return values;\n}\nfunction set(values) {\n    return values instanceof internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet ? values : new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2ludGVyc2VjdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvQztBQUVyQixTQUFTQyxhQUFhQyxNQUFNLEVBQUUsR0FBR0MsTUFBTTtJQUNwREQsU0FBUyxJQUFJRixnREFBU0EsQ0FBQ0U7SUFDdkJDLFNBQVNBLE9BQU9DLEdBQUcsQ0FBQ0M7SUFDcEJDLEtBQUssS0FBSyxNQUFNQyxTQUFTTCxPQUFRO1FBQy9CLEtBQUssTUFBTU0sU0FBU0wsT0FBUTtZQUMxQixJQUFJLENBQUNLLE1BQU1DLEdBQUcsQ0FBQ0YsUUFBUTtnQkFDckJMLE9BQU9RLE1BQU0sQ0FBQ0g7Z0JBQ2QsU0FBU0Q7WUFDWDtRQUNGO0lBQ0Y7SUFDQSxPQUFPSjtBQUNUO0FBRUEsU0FBU0csSUFBSUgsTUFBTTtJQUNqQixPQUFPQSxrQkFBa0JGLGdEQUFTQSxHQUFHRSxTQUFTLElBQUlGLGdEQUFTQSxDQUFDRTtBQUM5RCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9pbnRlcnNlY3Rpb24uanM/YjYzNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0ludGVyblNldH0gZnJvbSBcImludGVybm1hcFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBpbnRlcnNlY3Rpb24odmFsdWVzLCAuLi5vdGhlcnMpIHtcbiAgdmFsdWVzID0gbmV3IEludGVyblNldCh2YWx1ZXMpO1xuICBvdGhlcnMgPSBvdGhlcnMubWFwKHNldCk7XG4gIG91dDogZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICBmb3IgKGNvbnN0IG90aGVyIG9mIG90aGVycykge1xuICAgICAgaWYgKCFvdGhlci5oYXModmFsdWUpKSB7XG4gICAgICAgIHZhbHVlcy5kZWxldGUodmFsdWUpO1xuICAgICAgICBjb250aW51ZSBvdXQ7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiB2YWx1ZXM7XG59XG5cbmZ1bmN0aW9uIHNldCh2YWx1ZXMpIHtcbiAgcmV0dXJuIHZhbHVlcyBpbnN0YW5jZW9mIEludGVyblNldCA/IHZhbHVlcyA6IG5ldyBJbnRlcm5TZXQodmFsdWVzKTtcbn1cbiJdLCJuYW1lcyI6WyJJbnRlcm5TZXQiLCJpbnRlcnNlY3Rpb24iLCJ2YWx1ZXMiLCJvdGhlcnMiLCJtYXAiLCJzZXQiLCJvdXQiLCJ2YWx1ZSIsIm90aGVyIiwiaGFzIiwiZGVsZXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/intersection.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/least.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/least.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ least)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n\nfunction least(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    let min;\n    let defined = false;\n    if (compare.length === 1) {\n        let minValue;\n        for (const element of values){\n            const value = compare(element);\n            if (defined ? (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, minValue) < 0 : (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, value) === 0) {\n                min = element;\n                minValue = value;\n                defined = true;\n            }\n        }\n    } else {\n        for (const value of values){\n            if (defined ? compare(value, min) < 0 : compare(value, value) === 0) {\n                min = value;\n                defined = true;\n            }\n        }\n    }\n    return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2xlYXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDO0FBRXhCLFNBQVNDLE1BQU1DLE1BQU0sRUFBRUMsVUFBVUgscURBQVM7SUFDdkQsSUFBSUk7SUFDSixJQUFJQyxVQUFVO0lBQ2QsSUFBSUYsUUFBUUcsTUFBTSxLQUFLLEdBQUc7UUFDeEIsSUFBSUM7UUFDSixLQUFLLE1BQU1DLFdBQVdOLE9BQVE7WUFDNUIsTUFBTU8sUUFBUU4sUUFBUUs7WUFDdEIsSUFBSUgsVUFDRUwseURBQVNBLENBQUNTLE9BQU9GLFlBQVksSUFDN0JQLHlEQUFTQSxDQUFDUyxPQUFPQSxXQUFXLEdBQUc7Z0JBQ25DTCxNQUFNSTtnQkFDTkQsV0FBV0U7Z0JBQ1hKLFVBQVU7WUFDWjtRQUNGO0lBQ0YsT0FBTztRQUNMLEtBQUssTUFBTUksU0FBU1AsT0FBUTtZQUMxQixJQUFJRyxVQUNFRixRQUFRTSxPQUFPTCxPQUFPLElBQ3RCRCxRQUFRTSxPQUFPQSxXQUFXLEdBQUc7Z0JBQ2pDTCxNQUFNSztnQkFDTkosVUFBVTtZQUNaO1FBQ0Y7SUFDRjtJQUNBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbGVhc3QuanM/ZDY3ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBsZWFzdCh2YWx1ZXMsIGNvbXBhcmUgPSBhc2NlbmRpbmcpIHtcbiAgbGV0IG1pbjtcbiAgbGV0IGRlZmluZWQgPSBmYWxzZTtcbiAgaWYgKGNvbXBhcmUubGVuZ3RoID09PSAxKSB7XG4gICAgbGV0IG1pblZhbHVlO1xuICAgIGZvciAoY29uc3QgZWxlbWVudCBvZiB2YWx1ZXMpIHtcbiAgICAgIGNvbnN0IHZhbHVlID0gY29tcGFyZShlbGVtZW50KTtcbiAgICAgIGlmIChkZWZpbmVkXG4gICAgICAgICAgPyBhc2NlbmRpbmcodmFsdWUsIG1pblZhbHVlKSA8IDBcbiAgICAgICAgICA6IGFzY2VuZGluZyh2YWx1ZSwgdmFsdWUpID09PSAwKSB7XG4gICAgICAgIG1pbiA9IGVsZW1lbnQ7XG4gICAgICAgIG1pblZhbHVlID0gdmFsdWU7XG4gICAgICAgIGRlZmluZWQgPSB0cnVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKGRlZmluZWRcbiAgICAgICAgICA/IGNvbXBhcmUodmFsdWUsIG1pbikgPCAwXG4gICAgICAgICAgOiBjb21wYXJlKHZhbHVlLCB2YWx1ZSkgPT09IDApIHtcbiAgICAgICAgbWluID0gdmFsdWU7XG4gICAgICAgIGRlZmluZWQgPSB0cnVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gbWluO1xufVxuIl0sIm5hbWVzIjpbImFzY2VuZGluZyIsImxlYXN0IiwidmFsdWVzIiwiY29tcGFyZSIsIm1pbiIsImRlZmluZWQiLCJsZW5ndGgiLCJtaW5WYWx1ZSIsImVsZW1lbnQiLCJ2YWx1ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/least.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/leastIndex.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-array/src/leastIndex.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ leastIndex)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/./node_modules/d3-array/src/minIndex.js\");\n\n\nfunction leastIndex(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    if (compare.length === 1) return (0,_minIndex_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, compare);\n    let minValue;\n    let min = -1;\n    let index = -1;\n    for (const value of values){\n        ++index;\n        if (min < 0 ? compare(value, value) === 0 : compare(value, minValue) < 0) {\n            minValue = value;\n            min = index;\n        }\n    }\n    return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2xlYXN0SW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVDO0FBQ0Y7QUFFdEIsU0FBU0UsV0FBV0MsTUFBTSxFQUFFQyxVQUFVSixxREFBUztJQUM1RCxJQUFJSSxRQUFRQyxNQUFNLEtBQUssR0FBRyxPQUFPSix3REFBUUEsQ0FBQ0UsUUFBUUM7SUFDbEQsSUFBSUU7SUFDSixJQUFJQyxNQUFNLENBQUM7SUFDWCxJQUFJQyxRQUFRLENBQUM7SUFDYixLQUFLLE1BQU1DLFNBQVNOLE9BQVE7UUFDMUIsRUFBRUs7UUFDRixJQUFJRCxNQUFNLElBQ0pILFFBQVFLLE9BQU9BLFdBQVcsSUFDMUJMLFFBQVFLLE9BQU9ILFlBQVksR0FBRztZQUNsQ0EsV0FBV0c7WUFDWEYsTUFBTUM7UUFDUjtJQUNGO0lBQ0EsT0FBT0Q7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9sZWFzdEluZGV4LmpzP2JmNGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFzY2VuZGluZyBmcm9tIFwiLi9hc2NlbmRpbmcuanNcIjtcbmltcG9ydCBtaW5JbmRleCBmcm9tIFwiLi9taW5JbmRleC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBsZWFzdEluZGV4KHZhbHVlcywgY29tcGFyZSA9IGFzY2VuZGluZykge1xuICBpZiAoY29tcGFyZS5sZW5ndGggPT09IDEpIHJldHVybiBtaW5JbmRleCh2YWx1ZXMsIGNvbXBhcmUpO1xuICBsZXQgbWluVmFsdWU7XG4gIGxldCBtaW4gPSAtMTtcbiAgbGV0IGluZGV4ID0gLTE7XG4gIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgKytpbmRleDtcbiAgICBpZiAobWluIDwgMFxuICAgICAgICA/IGNvbXBhcmUodmFsdWUsIHZhbHVlKSA9PT0gMFxuICAgICAgICA6IGNvbXBhcmUodmFsdWUsIG1pblZhbHVlKSA8IDApIHtcbiAgICAgIG1pblZhbHVlID0gdmFsdWU7XG4gICAgICBtaW4gPSBpbmRleDtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1pbjtcbn1cbiJdLCJuYW1lcyI6WyJhc2NlbmRpbmciLCJtaW5JbmRleCIsImxlYXN0SW5kZXgiLCJ2YWx1ZXMiLCJjb21wYXJlIiwibGVuZ3RoIiwibWluVmFsdWUiLCJtaW4iLCJpbmRleCIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/leastIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/map.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/map.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ map)\n/* harmony export */ });\nfunction map(values, mapper) {\n    if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n    if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n    return Array.from(values, (value, index)=>mapper(value, index, values));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21hcC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsSUFBSUMsTUFBTSxFQUFFQyxNQUFNO0lBQ3hDLElBQUksT0FBT0QsTUFBTSxDQUFDRSxPQUFPQyxRQUFRLENBQUMsS0FBSyxZQUFZLE1BQU0sSUFBSUMsVUFBVTtJQUN2RSxJQUFJLE9BQU9ILFdBQVcsWUFBWSxNQUFNLElBQUlHLFVBQVU7SUFDdEQsT0FBT0MsTUFBTUMsSUFBSSxDQUFDTixRQUFRLENBQUNPLE9BQU9DLFFBQVVQLE9BQU9NLE9BQU9DLE9BQU9SO0FBQ25FIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21hcC5qcz81YzQ0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1hcCh2YWx1ZXMsIG1hcHBlcikge1xuICBpZiAodHlwZW9mIHZhbHVlc1tTeW1ib2wuaXRlcmF0b3JdICE9PSBcImZ1bmN0aW9uXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJ2YWx1ZXMgaXMgbm90IGl0ZXJhYmxlXCIpO1xuICBpZiAodHlwZW9mIG1hcHBlciAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwibWFwcGVyIGlzIG5vdCBhIGZ1bmN0aW9uXCIpO1xuICByZXR1cm4gQXJyYXkuZnJvbSh2YWx1ZXMsICh2YWx1ZSwgaW5kZXgpID0+IG1hcHBlcih2YWx1ZSwgaW5kZXgsIHZhbHVlcykpO1xufVxuIl0sIm5hbWVzIjpbIm1hcCIsInZhbHVlcyIsIm1hcHBlciIsIlN5bWJvbCIsIml0ZXJhdG9yIiwiVHlwZUVycm9yIiwiQXJyYXkiLCJmcm9tIiwidmFsdWUiLCJpbmRleCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/max.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/max.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ max)\n/* harmony export */ });\nfunction max(values, valueof) {\n    let max;\n    if (valueof === undefined) {\n        for (const value of values){\n            if (value != null && (max < value || max === undefined && value >= value)) {\n                max = value;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n                max = value;\n            }\n        }\n    }\n    return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21heC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsSUFBSUMsTUFBTSxFQUFFQyxPQUFPO0lBQ3pDLElBQUlGO0lBQ0osSUFBSUUsWUFBWUMsV0FBVztRQUN6QixLQUFLLE1BQU1DLFNBQVNILE9BQVE7WUFDMUIsSUFBSUcsU0FBUyxRQUNMSixDQUFBQSxNQUFNSSxTQUFVSixRQUFRRyxhQUFhQyxTQUFTQSxLQUFLLEdBQUk7Z0JBQzdESixNQUFNSTtZQUNSO1FBQ0Y7SUFDRixPQUFPO1FBQ0wsSUFBSUMsUUFBUSxDQUFDO1FBQ2IsS0FBSyxJQUFJRCxTQUFTSCxPQUFRO1lBQ3hCLElBQUksQ0FBQ0csUUFBUUYsUUFBUUUsT0FBTyxFQUFFQyxPQUFPSixPQUFNLEtBQU0sUUFDekNELENBQUFBLE1BQU1JLFNBQVVKLFFBQVFHLGFBQWFDLFNBQVNBLEtBQUssR0FBSTtnQkFDN0RKLE1BQU1JO1lBQ1I7UUFDRjtJQUNGO0lBQ0EsT0FBT0o7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9tYXguanM/ODlmNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtYXgodmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBtYXg7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKHZhbHVlICE9IG51bGxcbiAgICAgICAgICAmJiAobWF4IDwgdmFsdWUgfHwgKG1heCA9PT0gdW5kZWZpbmVkICYmIHZhbHVlID49IHZhbHVlKSkpIHtcbiAgICAgICAgbWF4ID0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGxldCBpbmRleCA9IC0xO1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKCh2YWx1ZSA9IHZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpICE9IG51bGxcbiAgICAgICAgICAmJiAobWF4IDwgdmFsdWUgfHwgKG1heCA9PT0gdW5kZWZpbmVkICYmIHZhbHVlID49IHZhbHVlKSkpIHtcbiAgICAgICAgbWF4ID0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBtYXg7XG59XG4iXSwibmFtZXMiOlsibWF4IiwidmFsdWVzIiwidmFsdWVvZiIsInVuZGVmaW5lZCIsInZhbHVlIiwiaW5kZXgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/max.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/maxIndex.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/maxIndex.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ maxIndex)\n/* harmony export */ });\nfunction maxIndex(values, valueof) {\n    let max;\n    let maxIndex = -1;\n    let index = -1;\n    if (valueof === undefined) {\n        for (const value of values){\n            ++index;\n            if (value != null && (max < value || max === undefined && value >= value)) {\n                max = value, maxIndex = index;\n            }\n        }\n    } else {\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n                max = value, maxIndex = index;\n            }\n        }\n    }\n    return maxIndex;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21heEluZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxTQUFTQyxNQUFNLEVBQUVDLE9BQU87SUFDOUMsSUFBSUM7SUFDSixJQUFJSCxXQUFXLENBQUM7SUFDaEIsSUFBSUksUUFBUSxDQUFDO0lBQ2IsSUFBSUYsWUFBWUcsV0FBVztRQUN6QixLQUFLLE1BQU1DLFNBQVNMLE9BQVE7WUFDMUIsRUFBRUc7WUFDRixJQUFJRSxTQUFTLFFBQ0xILENBQUFBLE1BQU1HLFNBQVVILFFBQVFFLGFBQWFDLFNBQVNBLEtBQUssR0FBSTtnQkFDN0RILE1BQU1HLE9BQU9OLFdBQVdJO1lBQzFCO1FBQ0Y7SUFDRixPQUFPO1FBQ0wsS0FBSyxJQUFJRSxTQUFTTCxPQUFRO1lBQ3hCLElBQUksQ0FBQ0ssUUFBUUosUUFBUUksT0FBTyxFQUFFRixPQUFPSCxPQUFNLEtBQU0sUUFDekNFLENBQUFBLE1BQU1HLFNBQVVILFFBQVFFLGFBQWFDLFNBQVNBLEtBQUssR0FBSTtnQkFDN0RILE1BQU1HLE9BQU9OLFdBQVdJO1lBQzFCO1FBQ0Y7SUFDRjtJQUNBLE9BQU9KO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbWF4SW5kZXguanM/N2Q2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtYXhJbmRleCh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IG1heDtcbiAgbGV0IG1heEluZGV4ID0gLTE7XG4gIGxldCBpbmRleCA9IC0xO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgICsraW5kZXg7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbFxuICAgICAgICAgICYmIChtYXggPCB2YWx1ZSB8fCAobWF4ID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtYXggPSB2YWx1ZSwgbWF4SW5kZXggPSBpbmRleDtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbFxuICAgICAgICAgICYmIChtYXggPCB2YWx1ZSB8fCAobWF4ID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtYXggPSB2YWx1ZSwgbWF4SW5kZXggPSBpbmRleDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1heEluZGV4O1xufVxuIl0sIm5hbWVzIjpbIm1heEluZGV4IiwidmFsdWVzIiwidmFsdWVvZiIsIm1heCIsImluZGV4IiwidW5kZWZpbmVkIiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/maxIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/mean.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/mean.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mean)\n/* harmony export */ });\nfunction mean(values, valueof) {\n    let count = 0;\n    let sum = 0;\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value != null && (value = +value) >= value) {\n                ++count, sum += value;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n                ++count, sum += value;\n            }\n        }\n    }\n    if (count) return sum / count;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lYW4uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLEtBQUtDLE1BQU0sRUFBRUMsT0FBTztJQUMxQyxJQUFJQyxRQUFRO0lBQ1osSUFBSUMsTUFBTTtJQUNWLElBQUlGLFlBQVlHLFdBQVc7UUFDekIsS0FBSyxJQUFJQyxTQUFTTCxPQUFRO1lBQ3hCLElBQUlLLFNBQVMsUUFBUSxDQUFDQSxRQUFRLENBQUNBLEtBQUksS0FBTUEsT0FBTztnQkFDOUMsRUFBRUgsT0FBT0MsT0FBT0U7WUFDbEI7UUFDRjtJQUNGLE9BQU87UUFDTCxJQUFJQyxRQUFRLENBQUM7UUFDYixLQUFLLElBQUlELFNBQVNMLE9BQVE7WUFDeEIsSUFBSSxDQUFDSyxRQUFRSixRQUFRSSxPQUFPLEVBQUVDLE9BQU9OLE9BQU0sS0FBTSxRQUFRLENBQUNLLFFBQVEsQ0FBQ0EsS0FBSSxLQUFNQSxPQUFPO2dCQUNsRixFQUFFSCxPQUFPQyxPQUFPRTtZQUNsQjtRQUNGO0lBQ0Y7SUFDQSxJQUFJSCxPQUFPLE9BQU9DLE1BQU1EO0FBQzFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lYW4uanM/MDFkNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtZWFuKHZhbHVlcywgdmFsdWVvZikge1xuICBsZXQgY291bnQgPSAwO1xuICBsZXQgc3VtID0gMDtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKHZhbHVlICE9IG51bGwgJiYgKHZhbHVlID0gK3ZhbHVlKSA+PSB2YWx1ZSkge1xuICAgICAgICArK2NvdW50LCBzdW0gKz0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGxldCBpbmRleCA9IC0xO1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKCh2YWx1ZSA9IHZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpICE9IG51bGwgJiYgKHZhbHVlID0gK3ZhbHVlKSA+PSB2YWx1ZSkge1xuICAgICAgICArK2NvdW50LCBzdW0gKz0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIGlmIChjb3VudCkgcmV0dXJuIHN1bSAvIGNvdW50O1xufVxuIl0sIm5hbWVzIjpbIm1lYW4iLCJ2YWx1ZXMiLCJ2YWx1ZW9mIiwiY291bnQiLCJzdW0iLCJ1bmRlZmluZWQiLCJ2YWx1ZSIsImluZGV4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/mean.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/median.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/median.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ median),\n/* harmony export */   medianIndex: () => (/* binding */ medianIndex)\n/* harmony export */ });\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quantile.js */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n\nfunction median(values, valueof) {\n    return (0,_quantile_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, 0.5, valueof);\n}\nfunction medianIndex(values, valueof) {\n    return (0,_quantile_js__WEBPACK_IMPORTED_MODULE_0__.quantileIndex)(values, 0.5, valueof);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lZGlhbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0Q7QUFFdkMsU0FBU0UsT0FBT0MsTUFBTSxFQUFFQyxPQUFPO0lBQzVDLE9BQU9KLHdEQUFRQSxDQUFDRyxRQUFRLEtBQUtDO0FBQy9CO0FBRU8sU0FBU0MsWUFBWUYsTUFBTSxFQUFFQyxPQUFPO0lBQ3pDLE9BQU9ILDJEQUFhQSxDQUFDRSxRQUFRLEtBQUtDO0FBQ3BDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lZGlhbi5qcz8zZWMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBxdWFudGlsZSwge3F1YW50aWxlSW5kZXh9IGZyb20gXCIuL3F1YW50aWxlLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1lZGlhbih2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgcmV0dXJuIHF1YW50aWxlKHZhbHVlcywgMC41LCB2YWx1ZW9mKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIG1lZGlhbkluZGV4KHZhbHVlcywgdmFsdWVvZikge1xuICByZXR1cm4gcXVhbnRpbGVJbmRleCh2YWx1ZXMsIDAuNSwgdmFsdWVvZik7XG59XG4iXSwibmFtZXMiOlsicXVhbnRpbGUiLCJxdWFudGlsZUluZGV4IiwibWVkaWFuIiwidmFsdWVzIiwidmFsdWVvZiIsIm1lZGlhbkluZGV4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/median.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/merge.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/merge.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ merge)\n/* harmony export */ });\nfunction* flatten(arrays) {\n    for (const array of arrays){\n        yield* array;\n    }\n}\nfunction merge(arrays) {\n    return Array.from(flatten(arrays));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lcmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxVQUFVQSxRQUFRQyxNQUFNO0lBQ3RCLEtBQUssTUFBTUMsU0FBU0QsT0FBUTtRQUMxQixPQUFPQztJQUNUO0FBQ0Y7QUFFZSxTQUFTQyxNQUFNRixNQUFNO0lBQ2xDLE9BQU9HLE1BQU1DLElBQUksQ0FBQ0wsUUFBUUM7QUFDNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbWVyZ2UuanM/M2IzNiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiogZmxhdHRlbihhcnJheXMpIHtcbiAgZm9yIChjb25zdCBhcnJheSBvZiBhcnJheXMpIHtcbiAgICB5aWVsZCogYXJyYXk7XG4gIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWVyZ2UoYXJyYXlzKSB7XG4gIHJldHVybiBBcnJheS5mcm9tKGZsYXR0ZW4oYXJyYXlzKSk7XG59XG4iXSwibmFtZXMiOlsiZmxhdHRlbiIsImFycmF5cyIsImFycmF5IiwibWVyZ2UiLCJBcnJheSIsImZyb20iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/min.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/min.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ min)\n/* harmony export */ });\nfunction min(values, valueof) {\n    let min;\n    if (valueof === undefined) {\n        for (const value of values){\n            if (value != null && (min > value || min === undefined && value >= value)) {\n                min = value;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (min > value || min === undefined && value >= value)) {\n                min = value;\n            }\n        }\n    }\n    return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21pbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsSUFBSUMsTUFBTSxFQUFFQyxPQUFPO0lBQ3pDLElBQUlGO0lBQ0osSUFBSUUsWUFBWUMsV0FBVztRQUN6QixLQUFLLE1BQU1DLFNBQVNILE9BQVE7WUFDMUIsSUFBSUcsU0FBUyxRQUNMSixDQUFBQSxNQUFNSSxTQUFVSixRQUFRRyxhQUFhQyxTQUFTQSxLQUFLLEdBQUk7Z0JBQzdESixNQUFNSTtZQUNSO1FBQ0Y7SUFDRixPQUFPO1FBQ0wsSUFBSUMsUUFBUSxDQUFDO1FBQ2IsS0FBSyxJQUFJRCxTQUFTSCxPQUFRO1lBQ3hCLElBQUksQ0FBQ0csUUFBUUYsUUFBUUUsT0FBTyxFQUFFQyxPQUFPSixPQUFNLEtBQU0sUUFDekNELENBQUFBLE1BQU1JLFNBQVVKLFFBQVFHLGFBQWFDLFNBQVNBLEtBQUssR0FBSTtnQkFDN0RKLE1BQU1JO1lBQ1I7UUFDRjtJQUNGO0lBQ0EsT0FBT0o7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9taW4uanM/NzhmMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtaW4odmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBtaW47XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKHZhbHVlICE9IG51bGxcbiAgICAgICAgICAmJiAobWluID4gdmFsdWUgfHwgKG1pbiA9PT0gdW5kZWZpbmVkICYmIHZhbHVlID49IHZhbHVlKSkpIHtcbiAgICAgICAgbWluID0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGxldCBpbmRleCA9IC0xO1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKCh2YWx1ZSA9IHZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpICE9IG51bGxcbiAgICAgICAgICAmJiAobWluID4gdmFsdWUgfHwgKG1pbiA9PT0gdW5kZWZpbmVkICYmIHZhbHVlID49IHZhbHVlKSkpIHtcbiAgICAgICAgbWluID0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBtaW47XG59XG4iXSwibmFtZXMiOlsibWluIiwidmFsdWVzIiwidmFsdWVvZiIsInVuZGVmaW5lZCIsInZhbHVlIiwiaW5kZXgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/min.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/minIndex.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/minIndex.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ minIndex)\n/* harmony export */ });\nfunction minIndex(values, valueof) {\n    let min;\n    let minIndex = -1;\n    let index = -1;\n    if (valueof === undefined) {\n        for (const value of values){\n            ++index;\n            if (value != null && (min > value || min === undefined && value >= value)) {\n                min = value, minIndex = index;\n            }\n        }\n    } else {\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (min > value || min === undefined && value >= value)) {\n                min = value, minIndex = index;\n            }\n        }\n    }\n    return minIndex;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21pbkluZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxTQUFTQyxNQUFNLEVBQUVDLE9BQU87SUFDOUMsSUFBSUM7SUFDSixJQUFJSCxXQUFXLENBQUM7SUFDaEIsSUFBSUksUUFBUSxDQUFDO0lBQ2IsSUFBSUYsWUFBWUcsV0FBVztRQUN6QixLQUFLLE1BQU1DLFNBQVNMLE9BQVE7WUFDMUIsRUFBRUc7WUFDRixJQUFJRSxTQUFTLFFBQ0xILENBQUFBLE1BQU1HLFNBQVVILFFBQVFFLGFBQWFDLFNBQVNBLEtBQUssR0FBSTtnQkFDN0RILE1BQU1HLE9BQU9OLFdBQVdJO1lBQzFCO1FBQ0Y7SUFDRixPQUFPO1FBQ0wsS0FBSyxJQUFJRSxTQUFTTCxPQUFRO1lBQ3hCLElBQUksQ0FBQ0ssUUFBUUosUUFBUUksT0FBTyxFQUFFRixPQUFPSCxPQUFNLEtBQU0sUUFDekNFLENBQUFBLE1BQU1HLFNBQVVILFFBQVFFLGFBQWFDLFNBQVNBLEtBQUssR0FBSTtnQkFDN0RILE1BQU1HLE9BQU9OLFdBQVdJO1lBQzFCO1FBQ0Y7SUFDRjtJQUNBLE9BQU9KO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbWluSW5kZXguanM/OGFlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtaW5JbmRleCh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IG1pbjtcbiAgbGV0IG1pbkluZGV4ID0gLTE7XG4gIGxldCBpbmRleCA9IC0xO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgICsraW5kZXg7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbFxuICAgICAgICAgICYmIChtaW4gPiB2YWx1ZSB8fCAobWluID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtaW4gPSB2YWx1ZSwgbWluSW5kZXggPSBpbmRleDtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbFxuICAgICAgICAgICYmIChtaW4gPiB2YWx1ZSB8fCAobWluID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtaW4gPSB2YWx1ZSwgbWluSW5kZXggPSBpbmRleDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1pbkluZGV4O1xufVxuIl0sIm5hbWVzIjpbIm1pbkluZGV4IiwidmFsdWVzIiwidmFsdWVvZiIsIm1pbiIsImluZGV4IiwidW5kZWZpbmVkIiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/minIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/mode.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/mode.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mode)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\nfunction mode(values, valueof) {\n    const counts = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternMap();\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value != null && value >= value) {\n                counts.set(value, (counts.get(value) || 0) + 1);\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && value >= value) {\n                counts.set(value, (counts.get(value) || 0) + 1);\n            }\n        }\n    }\n    let modeValue;\n    let modeCount = 0;\n    for (const [value, count] of counts){\n        if (count > modeCount) {\n            modeCount = count;\n            modeValue = value;\n        }\n    }\n    return modeValue;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/mode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/nice.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/nice.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ nice)\n/* harmony export */ });\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n\nfunction nice(start, stop, count) {\n    let prestep;\n    while(true){\n        const step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_0__.tickIncrement)(start, stop, count);\n        if (step === prestep || step === 0 || !isFinite(step)) {\n            return [\n                start,\n                stop\n            ];\n        } else if (step > 0) {\n            start = Math.floor(start / step) * step;\n            stop = Math.ceil(stop / step) * step;\n        } else if (step < 0) {\n            start = Math.ceil(start * step) / step;\n            stop = Math.floor(stop * step) / step;\n        }\n        prestep = step;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL25pY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7QUFFMUIsU0FBU0MsS0FBS0MsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLEtBQUs7SUFDN0MsSUFBSUM7SUFDSixNQUFPLEtBQU07UUFDWCxNQUFNQyxPQUFPTix3REFBYUEsQ0FBQ0UsT0FBT0MsTUFBTUM7UUFDeEMsSUFBSUUsU0FBU0QsV0FBV0MsU0FBUyxLQUFLLENBQUNDLFNBQVNELE9BQU87WUFDckQsT0FBTztnQkFBQ0o7Z0JBQU9DO2FBQUs7UUFDdEIsT0FBTyxJQUFJRyxPQUFPLEdBQUc7WUFDbkJKLFFBQVFNLEtBQUtDLEtBQUssQ0FBQ1AsUUFBUUksUUFBUUE7WUFDbkNILE9BQU9LLEtBQUtFLElBQUksQ0FBQ1AsT0FBT0csUUFBUUE7UUFDbEMsT0FBTyxJQUFJQSxPQUFPLEdBQUc7WUFDbkJKLFFBQVFNLEtBQUtFLElBQUksQ0FBQ1IsUUFBUUksUUFBUUE7WUFDbENILE9BQU9LLEtBQUtDLEtBQUssQ0FBQ04sT0FBT0csUUFBUUE7UUFDbkM7UUFDQUQsVUFBVUM7SUFDWjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL25pY2UuanM/ODM0MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge3RpY2tJbmNyZW1lbnR9IGZyb20gXCIuL3RpY2tzLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG5pY2Uoc3RhcnQsIHN0b3AsIGNvdW50KSB7XG4gIGxldCBwcmVzdGVwO1xuICB3aGlsZSAodHJ1ZSkge1xuICAgIGNvbnN0IHN0ZXAgPSB0aWNrSW5jcmVtZW50KHN0YXJ0LCBzdG9wLCBjb3VudCk7XG4gICAgaWYgKHN0ZXAgPT09IHByZXN0ZXAgfHwgc3RlcCA9PT0gMCB8fCAhaXNGaW5pdGUoc3RlcCkpIHtcbiAgICAgIHJldHVybiBbc3RhcnQsIHN0b3BdO1xuICAgIH0gZWxzZSBpZiAoc3RlcCA+IDApIHtcbiAgICAgIHN0YXJ0ID0gTWF0aC5mbG9vcihzdGFydCAvIHN0ZXApICogc3RlcDtcbiAgICAgIHN0b3AgPSBNYXRoLmNlaWwoc3RvcCAvIHN0ZXApICogc3RlcDtcbiAgICB9IGVsc2UgaWYgKHN0ZXAgPCAwKSB7XG4gICAgICBzdGFydCA9IE1hdGguY2VpbChzdGFydCAqIHN0ZXApIC8gc3RlcDtcbiAgICAgIHN0b3AgPSBNYXRoLmZsb29yKHN0b3AgKiBzdGVwKSAvIHN0ZXA7XG4gICAgfVxuICAgIHByZXN0ZXAgPSBzdGVwO1xuICB9XG59XG4iXSwibmFtZXMiOlsidGlja0luY3JlbWVudCIsIm5pY2UiLCJzdGFydCIsInN0b3AiLCJjb3VudCIsInByZXN0ZXAiLCJzdGVwIiwiaXNGaW5pdGUiLCJNYXRoIiwiZmxvb3IiLCJjZWlsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/nice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/number.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/number.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ number),\n/* harmony export */   numbers: () => (/* binding */ numbers)\n/* harmony export */ });\nfunction number(x) {\n    return x === null ? NaN : +x;\n}\nfunction* numbers(values, valueof) {\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value != null && (value = +value) >= value) {\n                yield value;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n                yield value;\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL251bWJlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFlLFNBQVNBLE9BQU9DLENBQUM7SUFDOUIsT0FBT0EsTUFBTSxPQUFPQyxNQUFNLENBQUNEO0FBQzdCO0FBRU8sVUFBVUUsUUFBUUMsTUFBTSxFQUFFQyxPQUFPO0lBQ3RDLElBQUlBLFlBQVlDLFdBQVc7UUFDekIsS0FBSyxJQUFJQyxTQUFTSCxPQUFRO1lBQ3hCLElBQUlHLFNBQVMsUUFBUSxDQUFDQSxRQUFRLENBQUNBLEtBQUksS0FBTUEsT0FBTztnQkFDOUMsTUFBTUE7WUFDUjtRQUNGO0lBQ0YsT0FBTztRQUNMLElBQUlDLFFBQVEsQ0FBQztRQUNiLEtBQUssSUFBSUQsU0FBU0gsT0FBUTtZQUN4QixJQUFJLENBQUNHLFFBQVFGLFFBQVFFLE9BQU8sRUFBRUMsT0FBT0osT0FBTSxLQUFNLFFBQVEsQ0FBQ0csUUFBUSxDQUFDQSxLQUFJLEtBQU1BLE9BQU87Z0JBQ2xGLE1BQU1BO1lBQ1I7UUFDRjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbnVtYmVyLmpzPzQxMGIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbnVtYmVyKHgpIHtcbiAgcmV0dXJuIHggPT09IG51bGwgPyBOYU4gOiAreDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uKiBudW1iZXJzKHZhbHVlcywgdmFsdWVvZikge1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgIHlpZWxkIHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsICYmICh2YWx1ZSA9ICt2YWx1ZSkgPj0gdmFsdWUpIHtcbiAgICAgICAgeWllbGQgdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsibnVtYmVyIiwieCIsIk5hTiIsIm51bWJlcnMiLCJ2YWx1ZXMiLCJ2YWx1ZW9mIiwidW5kZWZpbmVkIiwidmFsdWUiLCJpbmRleCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/pairs.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/pairs.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pairs),\n/* harmony export */   pair: () => (/* binding */ pair)\n/* harmony export */ });\nfunction pairs(values, pairof = pair) {\n    const pairs = [];\n    let previous;\n    let first = false;\n    for (const value of values){\n        if (first) pairs.push(pairof(previous, value));\n        previous = value;\n        first = true;\n    }\n    return pairs;\n}\nfunction pair(a, b) {\n    return [\n        a,\n        b\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3BhaXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWUsU0FBU0EsTUFBTUMsTUFBTSxFQUFFQyxTQUFTQyxJQUFJO0lBQ2pELE1BQU1ILFFBQVEsRUFBRTtJQUNoQixJQUFJSTtJQUNKLElBQUlDLFFBQVE7SUFDWixLQUFLLE1BQU1DLFNBQVNMLE9BQVE7UUFDMUIsSUFBSUksT0FBT0wsTUFBTU8sSUFBSSxDQUFDTCxPQUFPRSxVQUFVRTtRQUN2Q0YsV0FBV0U7UUFDWEQsUUFBUTtJQUNWO0lBQ0EsT0FBT0w7QUFDVDtBQUVPLFNBQVNHLEtBQUtLLENBQUMsRUFBRUMsQ0FBQztJQUN2QixPQUFPO1FBQUNEO1FBQUdDO0tBQUU7QUFDZiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9wYWlycy5qcz81NDMzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHBhaXJzKHZhbHVlcywgcGFpcm9mID0gcGFpcikge1xuICBjb25zdCBwYWlycyA9IFtdO1xuICBsZXQgcHJldmlvdXM7XG4gIGxldCBmaXJzdCA9IGZhbHNlO1xuICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgIGlmIChmaXJzdCkgcGFpcnMucHVzaChwYWlyb2YocHJldmlvdXMsIHZhbHVlKSk7XG4gICAgcHJldmlvdXMgPSB2YWx1ZTtcbiAgICBmaXJzdCA9IHRydWU7XG4gIH1cbiAgcmV0dXJuIHBhaXJzO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gcGFpcihhLCBiKSB7XG4gIHJldHVybiBbYSwgYl07XG59XG4iXSwibmFtZXMiOlsicGFpcnMiLCJ2YWx1ZXMiLCJwYWlyb2YiLCJwYWlyIiwicHJldmlvdXMiLCJmaXJzdCIsInZhbHVlIiwicHVzaCIsImEiLCJiIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/pairs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/permute.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-array/src/permute.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ permute)\n/* harmony export */ });\nfunction permute(source, keys) {\n    return Array.from(keys, (key)=>source[key]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3Blcm11dGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLFFBQVFDLE1BQU0sRUFBRUMsSUFBSTtJQUMxQyxPQUFPQyxNQUFNQyxJQUFJLENBQUNGLE1BQU1HLENBQUFBLE1BQU9KLE1BQU0sQ0FBQ0ksSUFBSTtBQUM1QyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9wZXJtdXRlLmpzPzg0MDgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcGVybXV0ZShzb3VyY2UsIGtleXMpIHtcbiAgcmV0dXJuIEFycmF5LmZyb20oa2V5cywga2V5ID0+IHNvdXJjZVtrZXldKTtcbn1cbiJdLCJuYW1lcyI6WyJwZXJtdXRlIiwic291cmNlIiwia2V5cyIsIkFycmF5IiwiZnJvbSIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/permute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/quantile.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/quantile.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quantile),\n/* harmony export */   quantileIndex: () => (/* binding */ quantileIndex),\n/* harmony export */   quantileSorted: () => (/* binding */ quantileSorted)\n/* harmony export */ });\n/* harmony import */ var _max_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./max.js */ \"(ssr)/./node_modules/d3-array/src/max.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/./node_modules/d3-array/src/maxIndex.js\");\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./min.js */ \"(ssr)/./node_modules/d3-array/src/min.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/./node_modules/d3-array/src/minIndex.js\");\n/* harmony import */ var _quickselect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./quickselect.js */ \"(ssr)/./node_modules/d3-array/src/quickselect.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-array/src/number.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n/* harmony import */ var _greatest_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./greatest.js */ \"(ssr)/./node_modules/d3-array/src/greatest.js\");\n\n\n\n\n\n\n\n\nfunction quantile(values, p, valueof) {\n    values = Float64Array.from((0,_number_js__WEBPACK_IMPORTED_MODULE_0__.numbers)(values, valueof));\n    if (!(n = values.length) || isNaN(p = +p)) return;\n    if (p <= 0 || n < 2) return (0,_min_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values);\n    if (p >= 1) return (0,_max_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(values);\n    var n, i = (n - 1) * p, i0 = Math.floor(i), value0 = (0,_max_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_quickselect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values, i0).subarray(0, i0 + 1)), value1 = (0,_min_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values.subarray(i0 + 1));\n    return value0 + (value1 - value0) * (i - i0);\n}\nfunction quantileSorted(values, p, valueof = _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    if (!(n = values.length) || isNaN(p = +p)) return;\n    if (p <= 0 || n < 2) return +valueof(values[0], 0, values);\n    if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n    var n, i = (n - 1) * p, i0 = Math.floor(i), value0 = +valueof(values[i0], i0, values), value1 = +valueof(values[i0 + 1], i0 + 1, values);\n    return value0 + (value1 - value0) * (i - i0);\n}\nfunction quantileIndex(values, p, valueof = _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    if (isNaN(p = +p)) return;\n    numbers = Float64Array.from(values, (_, i)=>(0,_number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(valueof(values[i], i, values)));\n    if (p <= 0) return (0,_minIndex_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(numbers);\n    if (p >= 1) return (0,_maxIndex_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(numbers);\n    var numbers, index = Uint32Array.from(values, (_, i)=>i), j = numbers.length - 1, i = Math.floor(j * p);\n    (0,_quickselect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(index, i, 0, j, (i, j)=>(0,_sort_js__WEBPACK_IMPORTED_MODULE_6__.ascendingDefined)(numbers[i], numbers[j]));\n    i = (0,_greatest_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(index.subarray(0, i + 1), (i)=>numbers[i]);\n    return i >= 0 ? i : -1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/quantile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/quickselect.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-array/src/quickselect.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quickselect)\n/* harmony export */ });\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nfunction quickselect(array, k, left = 0, right = Infinity, compare) {\n    k = Math.floor(k);\n    left = Math.floor(Math.max(0, left));\n    right = Math.floor(Math.min(array.length - 1, right));\n    if (!(left <= k && k <= right)) return array;\n    compare = compare === undefined ? _sort_js__WEBPACK_IMPORTED_MODULE_0__.ascendingDefined : (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__.compareDefined)(compare);\n    while(right > left){\n        if (right - left > 600) {\n            const n = right - left + 1;\n            const m = k - left + 1;\n            const z = Math.log(n);\n            const s = 0.5 * Math.exp(2 * z / 3);\n            const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n            const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n            const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n            quickselect(array, k, newLeft, newRight, compare);\n        }\n        const t = array[k];\n        let i = left;\n        let j = right;\n        swap(array, left, k);\n        if (compare(array[right], t) > 0) swap(array, left, right);\n        while(i < j){\n            swap(array, i, j), ++i, --j;\n            while(compare(array[i], t) < 0)++i;\n            while(compare(array[j], t) > 0)--j;\n        }\n        if (compare(array[left], t) === 0) swap(array, left, j);\n        else ++j, swap(array, j, right);\n        if (j <= k) left = j + 1;\n        if (k <= j) right = j - 1;\n    }\n    return array;\n}\nfunction swap(array, i, j) {\n    const t = array[i];\n    array[i] = array[j];\n    array[j] = t;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/quickselect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/range.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/range.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ range)\n/* harmony export */ });\nfunction range(start, stop, step) {\n    start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n    var i = -1, n = Math.max(0, Math.ceil((stop - start) / step)) | 0, range = new Array(n);\n    while(++i < n){\n        range[i] = start + i * step;\n    }\n    return range;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JhbmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxNQUFNQyxLQUFLLEVBQUVDLElBQUksRUFBRUMsSUFBSTtJQUM3Q0YsUUFBUSxDQUFDQSxPQUFPQyxPQUFPLENBQUNBLE1BQU1DLE9BQU8sQ0FBQ0MsSUFBSUMsVUFBVUMsTUFBTSxJQUFJLElBQUtKLENBQUFBLE9BQU9ELE9BQU9BLFFBQVEsR0FBRyxLQUFLRyxJQUFJLElBQUksSUFBSSxDQUFDRDtJQUU5RyxJQUFJSSxJQUFJLENBQUMsR0FDTEgsSUFBSUksS0FBS0MsR0FBRyxDQUFDLEdBQUdELEtBQUtFLElBQUksQ0FBQyxDQUFDUixPQUFPRCxLQUFJLElBQUtFLFNBQVMsR0FDcERILFFBQVEsSUFBSVcsTUFBTVA7SUFFdEIsTUFBTyxFQUFFRyxJQUFJSCxFQUFHO1FBQ2RKLEtBQUssQ0FBQ08sRUFBRSxHQUFHTixRQUFRTSxJQUFJSjtJQUN6QjtJQUVBLE9BQU9IO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvcmFuZ2UuanM/YTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiByYW5nZShzdGFydCwgc3RvcCwgc3RlcCkge1xuICBzdGFydCA9ICtzdGFydCwgc3RvcCA9ICtzdG9wLCBzdGVwID0gKG4gPSBhcmd1bWVudHMubGVuZ3RoKSA8IDIgPyAoc3RvcCA9IHN0YXJ0LCBzdGFydCA9IDAsIDEpIDogbiA8IDMgPyAxIDogK3N0ZXA7XG5cbiAgdmFyIGkgPSAtMSxcbiAgICAgIG4gPSBNYXRoLm1heCgwLCBNYXRoLmNlaWwoKHN0b3AgLSBzdGFydCkgLyBzdGVwKSkgfCAwLFxuICAgICAgcmFuZ2UgPSBuZXcgQXJyYXkobik7XG5cbiAgd2hpbGUgKCsraSA8IG4pIHtcbiAgICByYW5nZVtpXSA9IHN0YXJ0ICsgaSAqIHN0ZXA7XG4gIH1cblxuICByZXR1cm4gcmFuZ2U7XG59XG4iXSwibmFtZXMiOlsicmFuZ2UiLCJzdGFydCIsInN0b3AiLCJzdGVwIiwibiIsImFyZ3VtZW50cyIsImxlbmd0aCIsImkiLCJNYXRoIiwibWF4IiwiY2VpbCIsIkFycmF5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/range.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/rank.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/rank.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rank)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n\n\nfunction rank(values, valueof = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n    let V = Array.from(values);\n    const R = new Float64Array(V.length);\n    if (valueof.length !== 2) V = V.map(valueof), valueof = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n    const compareIndex = (i, j)=>valueof(V[i], V[j]);\n    let k, r;\n    values = Uint32Array.from(V, (_, i)=>i);\n    // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n    values.sort(valueof === _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] ? (i, j)=>(0,_sort_js__WEBPACK_IMPORTED_MODULE_1__.ascendingDefined)(V[i], V[j]) : (0,_sort_js__WEBPACK_IMPORTED_MODULE_1__.compareDefined)(compareIndex));\n    values.forEach((j, i)=>{\n        const c = compareIndex(j, k === undefined ? j : k);\n        if (c >= 0) {\n            if (k === undefined || c > 0) k = j, r = i;\n            R[j] = r;\n        } else {\n            R[j] = NaN;\n        }\n    });\n    return R;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/rank.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/reduce.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/reduce.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ reduce)\n/* harmony export */ });\nfunction reduce(values, reducer, value) {\n    if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n    const iterator = values[Symbol.iterator]();\n    let done, next, index = -1;\n    if (arguments.length < 3) {\n        ({ done, value } = iterator.next());\n        if (done) return;\n        ++index;\n    }\n    while({ done, value: next } = iterator.next(), !done){\n        value = reducer(value, next, ++index, values);\n    }\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JlZHVjZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsT0FBT0MsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLEtBQUs7SUFDbkQsSUFBSSxPQUFPRCxZQUFZLFlBQVksTUFBTSxJQUFJRSxVQUFVO0lBQ3ZELE1BQU1DLFdBQVdKLE1BQU0sQ0FBQ0ssT0FBT0QsUUFBUSxDQUFDO0lBQ3hDLElBQUlFLE1BQU1DLE1BQU1DLFFBQVEsQ0FBQztJQUN6QixJQUFJQyxVQUFVQyxNQUFNLEdBQUcsR0FBRztRQUN2QixHQUFDSixJQUFJLEVBQUVKLEtBQUssRUFBQyxHQUFHRSxTQUFTRyxJQUFJLEVBQUM7UUFDL0IsSUFBSUQsTUFBTTtRQUNWLEVBQUVFO0lBQ0o7SUFDQSxNQUFPLEVBQUVGLElBQUksRUFBRUosT0FBT0ssSUFBSSxFQUFDLEdBQUdILFNBQVNHLElBQUksSUFBSyxDQUFDRCxLQUFNO1FBQ3JESixRQUFRRCxRQUFRQyxPQUFPSyxNQUFNLEVBQUVDLE9BQU9SO0lBQ3hDO0lBQ0EsT0FBT0U7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9yZWR1Y2UuanM/YjY4NCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiByZWR1Y2UodmFsdWVzLCByZWR1Y2VyLCB2YWx1ZSkge1xuICBpZiAodHlwZW9mIHJlZHVjZXIgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcInJlZHVjZXIgaXMgbm90IGEgZnVuY3Rpb25cIik7XG4gIGNvbnN0IGl0ZXJhdG9yID0gdmFsdWVzW1N5bWJvbC5pdGVyYXRvcl0oKTtcbiAgbGV0IGRvbmUsIG5leHQsIGluZGV4ID0gLTE7XG4gIGlmIChhcmd1bWVudHMubGVuZ3RoIDwgMykge1xuICAgICh7ZG9uZSwgdmFsdWV9ID0gaXRlcmF0b3IubmV4dCgpKTtcbiAgICBpZiAoZG9uZSkgcmV0dXJuO1xuICAgICsraW5kZXg7XG4gIH1cbiAgd2hpbGUgKCh7ZG9uZSwgdmFsdWU6IG5leHR9ID0gaXRlcmF0b3IubmV4dCgpKSwgIWRvbmUpIHtcbiAgICB2YWx1ZSA9IHJlZHVjZXIodmFsdWUsIG5leHQsICsraW5kZXgsIHZhbHVlcyk7XG4gIH1cbiAgcmV0dXJuIHZhbHVlO1xufVxuIl0sIm5hbWVzIjpbInJlZHVjZSIsInZhbHVlcyIsInJlZHVjZXIiLCJ2YWx1ZSIsIlR5cGVFcnJvciIsIml0ZXJhdG9yIiwiU3ltYm9sIiwiZG9uZSIsIm5leHQiLCJpbmRleCIsImFyZ3VtZW50cyIsImxlbmd0aCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/reduce.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/reverse.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-array/src/reverse.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ reverse)\n/* harmony export */ });\nfunction reverse(values) {\n    if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n    return Array.from(values).reverse();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JldmVyc2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLFFBQVFDLE1BQU07SUFDcEMsSUFBSSxPQUFPQSxNQUFNLENBQUNDLE9BQU9DLFFBQVEsQ0FBQyxLQUFLLFlBQVksTUFBTSxJQUFJQyxVQUFVO0lBQ3ZFLE9BQU9DLE1BQU1DLElBQUksQ0FBQ0wsUUFBUUQsT0FBTztBQUNuQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9yZXZlcnNlLmpzP2I4MzkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcmV2ZXJzZSh2YWx1ZXMpIHtcbiAgaWYgKHR5cGVvZiB2YWx1ZXNbU3ltYm9sLml0ZXJhdG9yXSAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwidmFsdWVzIGlzIG5vdCBpdGVyYWJsZVwiKTtcbiAgcmV0dXJuIEFycmF5LmZyb20odmFsdWVzKS5yZXZlcnNlKCk7XG59XG4iXSwibmFtZXMiOlsicmV2ZXJzZSIsInZhbHVlcyIsIlN5bWJvbCIsIml0ZXJhdG9yIiwiVHlwZUVycm9yIiwiQXJyYXkiLCJmcm9tIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/reverse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/scan.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/scan.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ scan)\n/* harmony export */ });\n/* harmony import */ var _leastIndex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./leastIndex.js */ \"(ssr)/./node_modules/d3-array/src/leastIndex.js\");\n\nfunction scan(values, compare) {\n    const index = (0,_leastIndex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, compare);\n    return index < 0 ? undefined : index;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NjYW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7QUFFMUIsU0FBU0MsS0FBS0MsTUFBTSxFQUFFQyxPQUFPO0lBQzFDLE1BQU1DLFFBQVFKLDBEQUFVQSxDQUFDRSxRQUFRQztJQUNqQyxPQUFPQyxRQUFRLElBQUlDLFlBQVlEO0FBQ2pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NjYW4uanM/Y2QxMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbGVhc3RJbmRleCBmcm9tIFwiLi9sZWFzdEluZGV4LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHNjYW4odmFsdWVzLCBjb21wYXJlKSB7XG4gIGNvbnN0IGluZGV4ID0gbGVhc3RJbmRleCh2YWx1ZXMsIGNvbXBhcmUpO1xuICByZXR1cm4gaW5kZXggPCAwID8gdW5kZWZpbmVkIDogaW5kZXg7XG59XG4iXSwibmFtZXMiOlsibGVhc3RJbmRleCIsInNjYW4iLCJ2YWx1ZXMiLCJjb21wYXJlIiwiaW5kZXgiLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/scan.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/shuffle.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-array/src/shuffle.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   shuffler: () => (/* binding */ shuffler)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (shuffler(Math.random));\nfunction shuffler(random) {\n    return function shuffle(array, i0 = 0, i1 = array.length) {\n        let m = i1 - (i0 = +i0);\n        while(m){\n            const i = random() * m-- | 0, t = array[m + i0];\n            array[m + i0] = array[i + i0];\n            array[i + i0] = t;\n        }\n        return array;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NodWZmbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxpRUFBZUEsU0FBU0MsS0FBS0MsTUFBTSxDQUFDLEVBQUM7QUFFOUIsU0FBU0YsU0FBU0UsTUFBTTtJQUM3QixPQUFPLFNBQVNDLFFBQVFDLEtBQUssRUFBRUMsS0FBSyxDQUFDLEVBQUVDLEtBQUtGLE1BQU1HLE1BQU07UUFDdEQsSUFBSUMsSUFBSUYsS0FBTUQsQ0FBQUEsS0FBSyxDQUFDQSxFQUFDO1FBQ3JCLE1BQU9HLEVBQUc7WUFDUixNQUFNQyxJQUFJUCxXQUFXTSxNQUFNLEdBQUdFLElBQUlOLEtBQUssQ0FBQ0ksSUFBSUgsR0FBRztZQUMvQ0QsS0FBSyxDQUFDSSxJQUFJSCxHQUFHLEdBQUdELEtBQUssQ0FBQ0ssSUFBSUosR0FBRztZQUM3QkQsS0FBSyxDQUFDSyxJQUFJSixHQUFHLEdBQUdLO1FBQ2xCO1FBQ0EsT0FBT047SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NodWZmbGUuanM/NWM4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBzaHVmZmxlcihNYXRoLnJhbmRvbSk7XG5cbmV4cG9ydCBmdW5jdGlvbiBzaHVmZmxlcihyYW5kb20pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIHNodWZmbGUoYXJyYXksIGkwID0gMCwgaTEgPSBhcnJheS5sZW5ndGgpIHtcbiAgICBsZXQgbSA9IGkxIC0gKGkwID0gK2kwKTtcbiAgICB3aGlsZSAobSkge1xuICAgICAgY29uc3QgaSA9IHJhbmRvbSgpICogbS0tIHwgMCwgdCA9IGFycmF5W20gKyBpMF07XG4gICAgICBhcnJheVttICsgaTBdID0gYXJyYXlbaSArIGkwXTtcbiAgICAgIGFycmF5W2kgKyBpMF0gPSB0O1xuICAgIH1cbiAgICByZXR1cm4gYXJyYXk7XG4gIH07XG59XG4iXSwibmFtZXMiOlsic2h1ZmZsZXIiLCJNYXRoIiwicmFuZG9tIiwic2h1ZmZsZSIsImFycmF5IiwiaTAiLCJpMSIsImxlbmd0aCIsIm0iLCJpIiwidCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/shuffle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/some.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/some.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ some)\n/* harmony export */ });\nfunction some(values, test) {\n    if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n    let index = -1;\n    for (const value of values){\n        if (test(value, ++index, values)) {\n            return true;\n        }\n    }\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NvbWUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLEtBQUtDLE1BQU0sRUFBRUMsSUFBSTtJQUN2QyxJQUFJLE9BQU9BLFNBQVMsWUFBWSxNQUFNLElBQUlDLFVBQVU7SUFDcEQsSUFBSUMsUUFBUSxDQUFDO0lBQ2IsS0FBSyxNQUFNQyxTQUFTSixPQUFRO1FBQzFCLElBQUlDLEtBQUtHLE9BQU8sRUFBRUQsT0FBT0gsU0FBUztZQUNoQyxPQUFPO1FBQ1Q7SUFDRjtJQUNBLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9zb21lLmpzPzM5ZjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gc29tZSh2YWx1ZXMsIHRlc3QpIHtcbiAgaWYgKHR5cGVvZiB0ZXN0ICE9PSBcImZ1bmN0aW9uXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJ0ZXN0IGlzIG5vdCBhIGZ1bmN0aW9uXCIpO1xuICBsZXQgaW5kZXggPSAtMTtcbiAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICBpZiAodGVzdCh2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICB9XG4gIHJldHVybiBmYWxzZTtcbn1cbiJdLCJuYW1lcyI6WyJzb21lIiwidmFsdWVzIiwidGVzdCIsIlR5cGVFcnJvciIsImluZGV4IiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/some.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/sort.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/sort.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ascendingDefined: () => (/* binding */ ascendingDefined),\n/* harmony export */   compareDefined: () => (/* binding */ compareDefined),\n/* harmony export */   \"default\": () => (/* binding */ sort)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _permute_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./permute.js */ \"(ssr)/./node_modules/d3-array/src/permute.js\");\n\n\nfunction sort(values, ...F) {\n    if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n    values = Array.from(values);\n    let [f] = F;\n    if (f && f.length !== 2 || F.length > 1) {\n        const index = Uint32Array.from(values, (d, i)=>i);\n        if (F.length > 1) {\n            F = F.map((f)=>values.map(f));\n            index.sort((i, j)=>{\n                for (const f of F){\n                    const c = ascendingDefined(f[i], f[j]);\n                    if (c) return c;\n                }\n            });\n        } else {\n            f = values.map(f);\n            index.sort((i, j)=>ascendingDefined(f[i], f[j]));\n        }\n        return (0,_permute_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, index);\n    }\n    return values.sort(compareDefined(f));\n}\nfunction compareDefined(compare = _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n    if (compare === _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) return ascendingDefined;\n    if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n    return (a, b)=>{\n        const x = compare(a, b);\n        if (x || x === 0) return x;\n        return (compare(b, b) === 0) - (compare(a, a) === 0);\n    };\n}\nfunction ascendingDefined(a, b) {\n    return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/sort.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/subset.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/subset.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ subset)\n/* harmony export */ });\n/* harmony import */ var _superset_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./superset.js */ \"(ssr)/./node_modules/d3-array/src/superset.js\");\n\nfunction subset(values, other) {\n    return (0,_superset_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(other, values);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3N1YnNldC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQztBQUV0QixTQUFTQyxPQUFPQyxNQUFNLEVBQUVDLEtBQUs7SUFDMUMsT0FBT0gsd0RBQVFBLENBQUNHLE9BQU9EO0FBQ3pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3N1YnNldC5qcz9mZWM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBzdXBlcnNldCBmcm9tIFwiLi9zdXBlcnNldC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBzdWJzZXQodmFsdWVzLCBvdGhlcikge1xuICByZXR1cm4gc3VwZXJzZXQob3RoZXIsIHZhbHVlcyk7XG59XG4iXSwibmFtZXMiOlsic3VwZXJzZXQiLCJzdWJzZXQiLCJ2YWx1ZXMiLCJvdGhlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/subset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/sum.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/sum.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ sum)\n/* harmony export */ });\nfunction sum(values, valueof) {\n    let sum = 0;\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value = +value) {\n                sum += value;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if (value = +valueof(value, ++index, values)) {\n                sum += value;\n            }\n        }\n    }\n    return sum;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3N1bS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsSUFBSUMsTUFBTSxFQUFFQyxPQUFPO0lBQ3pDLElBQUlGLE1BQU07SUFDVixJQUFJRSxZQUFZQyxXQUFXO1FBQ3pCLEtBQUssSUFBSUMsU0FBU0gsT0FBUTtZQUN4QixJQUFJRyxRQUFRLENBQUNBLE9BQU87Z0JBQ2xCSixPQUFPSTtZQUNUO1FBQ0Y7SUFDRixPQUFPO1FBQ0wsSUFBSUMsUUFBUSxDQUFDO1FBQ2IsS0FBSyxJQUFJRCxTQUFTSCxPQUFRO1lBQ3hCLElBQUlHLFFBQVEsQ0FBQ0YsUUFBUUUsT0FBTyxFQUFFQyxPQUFPSixTQUFTO2dCQUM1Q0QsT0FBT0k7WUFDVDtRQUNGO0lBQ0Y7SUFDQSxPQUFPSjtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3N1bS5qcz9kODJjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHN1bSh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IHN1bSA9IDA7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSA9ICt2YWx1ZSkge1xuICAgICAgICBzdW0gKz0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGxldCBpbmRleCA9IC0xO1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKHZhbHVlID0gK3ZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpIHtcbiAgICAgICAgc3VtICs9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gc3VtO1xufVxuIl0sIm5hbWVzIjpbInN1bSIsInZhbHVlcyIsInZhbHVlb2YiLCJ1bmRlZmluZWQiLCJ2YWx1ZSIsImluZGV4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/sum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/superset.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/superset.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ superset)\n/* harmony export */ });\nfunction superset(values, other) {\n    const iterator = values[Symbol.iterator](), set = new Set();\n    for (const o of other){\n        const io = intern(o);\n        if (set.has(io)) continue;\n        let value, done;\n        while({ value, done } = iterator.next()){\n            if (done) return false;\n            const ivalue = intern(value);\n            set.add(ivalue);\n            if (Object.is(io, ivalue)) break;\n        }\n    }\n    return true;\n}\nfunction intern(value) {\n    return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3N1cGVyc2V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxTQUFTQyxNQUFNLEVBQUVDLEtBQUs7SUFDNUMsTUFBTUMsV0FBV0YsTUFBTSxDQUFDRyxPQUFPRCxRQUFRLENBQUMsSUFBSUUsTUFBTSxJQUFJQztJQUN0RCxLQUFLLE1BQU1DLEtBQUtMLE1BQU87UUFDckIsTUFBTU0sS0FBS0MsT0FBT0Y7UUFDbEIsSUFBSUYsSUFBSUssR0FBRyxDQUFDRixLQUFLO1FBQ2pCLElBQUlHLE9BQU9DO1FBQ1gsTUFBUSxFQUFDRCxLQUFLLEVBQUVDLElBQUksRUFBQyxHQUFHVCxTQUFTVSxJQUFJLEdBQUs7WUFDeEMsSUFBSUQsTUFBTSxPQUFPO1lBQ2pCLE1BQU1FLFNBQVNMLE9BQU9FO1lBQ3RCTixJQUFJVSxHQUFHLENBQUNEO1lBQ1IsSUFBSUUsT0FBT0MsRUFBRSxDQUFDVCxJQUFJTSxTQUFTO1FBQzdCO0lBQ0Y7SUFDQSxPQUFPO0FBQ1Q7QUFFQSxTQUFTTCxPQUFPRSxLQUFLO0lBQ25CLE9BQU9BLFVBQVUsUUFBUSxPQUFPQSxVQUFVLFdBQVdBLE1BQU1PLE9BQU8sS0FBS1A7QUFDekUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvc3VwZXJzZXQuanM/Y2UzMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBzdXBlcnNldCh2YWx1ZXMsIG90aGVyKSB7XG4gIGNvbnN0IGl0ZXJhdG9yID0gdmFsdWVzW1N5bWJvbC5pdGVyYXRvcl0oKSwgc2V0ID0gbmV3IFNldCgpO1xuICBmb3IgKGNvbnN0IG8gb2Ygb3RoZXIpIHtcbiAgICBjb25zdCBpbyA9IGludGVybihvKTtcbiAgICBpZiAoc2V0LmhhcyhpbykpIGNvbnRpbnVlO1xuICAgIGxldCB2YWx1ZSwgZG9uZTtcbiAgICB3aGlsZSAoKHt2YWx1ZSwgZG9uZX0gPSBpdGVyYXRvci5uZXh0KCkpKSB7XG4gICAgICBpZiAoZG9uZSkgcmV0dXJuIGZhbHNlO1xuICAgICAgY29uc3QgaXZhbHVlID0gaW50ZXJuKHZhbHVlKTtcbiAgICAgIHNldC5hZGQoaXZhbHVlKTtcbiAgICAgIGlmIChPYmplY3QuaXMoaW8sIGl2YWx1ZSkpIGJyZWFrO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cblxuZnVuY3Rpb24gaW50ZXJuKHZhbHVlKSB7XG4gIHJldHVybiB2YWx1ZSAhPT0gbnVsbCAmJiB0eXBlb2YgdmFsdWUgPT09IFwib2JqZWN0XCIgPyB2YWx1ZS52YWx1ZU9mKCkgOiB2YWx1ZTtcbn1cbiJdLCJuYW1lcyI6WyJzdXBlcnNldCIsInZhbHVlcyIsIm90aGVyIiwiaXRlcmF0b3IiLCJTeW1ib2wiLCJzZXQiLCJTZXQiLCJvIiwiaW8iLCJpbnRlcm4iLCJoYXMiLCJ2YWx1ZSIsImRvbmUiLCJuZXh0IiwiaXZhbHVlIiwiYWRkIiwiT2JqZWN0IiwiaXMiLCJ2YWx1ZU9mIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/superset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/threshold/freedmanDiaconis.js":
/*!*****************************************************************!*\
  !*** ./node_modules/d3-array/src/threshold/freedmanDiaconis.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdFreedmanDiaconis)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../quantile.js */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n\n\nfunction thresholdFreedmanDiaconis(values, min, max) {\n    const c = (0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values), d = (0,_quantile_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, 0.75) - (0,_quantile_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, 0.25);\n    return c && d ? Math.ceil((max - min) / (2 * d * Math.pow(c, -1 / 3))) : 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9mcmVlZG1hbkRpYWNvbmlzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnQztBQUNNO0FBRXZCLFNBQVNFLDBCQUEwQkMsTUFBTSxFQUFFQyxHQUFHLEVBQUVDLEdBQUc7SUFDaEUsTUFBTUMsSUFBSU4scURBQUtBLENBQUNHLFNBQVNJLElBQUlOLHdEQUFRQSxDQUFDRSxRQUFRLFFBQVFGLHdEQUFRQSxDQUFDRSxRQUFRO0lBQ3ZFLE9BQU9HLEtBQUtDLElBQUlDLEtBQUtDLElBQUksQ0FBQyxDQUFDSixNQUFNRCxHQUFFLElBQU0sS0FBSUcsSUFBSUMsS0FBS0UsR0FBRyxDQUFDSixHQUFHLENBQUMsSUFBSSxFQUFDLEtBQU07QUFDM0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvdGhyZXNob2xkL2ZyZWVkbWFuRGlhY29uaXMuanM/NTQzMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY291bnQgZnJvbSBcIi4uL2NvdW50LmpzXCI7XG5pbXBvcnQgcXVhbnRpbGUgZnJvbSBcIi4uL3F1YW50aWxlLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHRocmVzaG9sZEZyZWVkbWFuRGlhY29uaXModmFsdWVzLCBtaW4sIG1heCkge1xuICBjb25zdCBjID0gY291bnQodmFsdWVzKSwgZCA9IHF1YW50aWxlKHZhbHVlcywgMC43NSkgLSBxdWFudGlsZSh2YWx1ZXMsIDAuMjUpO1xuICByZXR1cm4gYyAmJiBkID8gTWF0aC5jZWlsKChtYXggLSBtaW4pIC8gKDIgKiBkICogTWF0aC5wb3coYywgLTEgLyAzKSkpIDogMTtcbn1cbiJdLCJuYW1lcyI6WyJjb3VudCIsInF1YW50aWxlIiwidGhyZXNob2xkRnJlZWRtYW5EaWFjb25pcyIsInZhbHVlcyIsIm1pbiIsIm1heCIsImMiLCJkIiwiTWF0aCIsImNlaWwiLCJwb3ciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/threshold/freedmanDiaconis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/threshold/scott.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-array/src/threshold/scott.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdScott)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n/* harmony import */ var _deviation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../deviation.js */ \"(ssr)/./node_modules/d3-array/src/deviation.js\");\n\n\nfunction thresholdScott(values, min, max) {\n    const c = (0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values), d = (0,_deviation_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values);\n    return c && d ? Math.ceil((max - min) * Math.cbrt(c) / (3.49 * d)) : 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9zY290dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFDUTtBQUV6QixTQUFTRSxlQUFlQyxNQUFNLEVBQUVDLEdBQUcsRUFBRUMsR0FBRztJQUNyRCxNQUFNQyxJQUFJTixxREFBS0EsQ0FBQ0csU0FBU0ksSUFBSU4seURBQVNBLENBQUNFO0lBQ3ZDLE9BQU9HLEtBQUtDLElBQUlDLEtBQUtDLElBQUksQ0FBQyxDQUFDSixNQUFNRCxHQUFFLElBQUtJLEtBQUtFLElBQUksQ0FBQ0osS0FBTSxRQUFPQyxDQUFBQSxLQUFNO0FBQ3ZFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9zY290dC5qcz9jN2MwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjb3VudCBmcm9tIFwiLi4vY291bnQuanNcIjtcbmltcG9ydCBkZXZpYXRpb24gZnJvbSBcIi4uL2RldmlhdGlvbi5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB0aHJlc2hvbGRTY290dCh2YWx1ZXMsIG1pbiwgbWF4KSB7XG4gIGNvbnN0IGMgPSBjb3VudCh2YWx1ZXMpLCBkID0gZGV2aWF0aW9uKHZhbHVlcyk7XG4gIHJldHVybiBjICYmIGQgPyBNYXRoLmNlaWwoKG1heCAtIG1pbikgKiBNYXRoLmNicnQoYykgLyAoMy40OSAqIGQpKSA6IDE7XG59XG4iXSwibmFtZXMiOlsiY291bnQiLCJkZXZpYXRpb24iLCJ0aHJlc2hvbGRTY290dCIsInZhbHVlcyIsIm1pbiIsIm1heCIsImMiLCJkIiwiTWF0aCIsImNlaWwiLCJjYnJ0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/threshold/scott.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/threshold/sturges.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-array/src/threshold/sturges.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdSturges)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n\nfunction thresholdSturges(values) {\n    return Math.max(1, Math.ceil(Math.log((0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values)) / Math.LN2) + 1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9zdHVyZ2VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdDO0FBRWpCLFNBQVNDLGlCQUFpQkMsTUFBTTtJQUM3QyxPQUFPQyxLQUFLQyxHQUFHLENBQUMsR0FBR0QsS0FBS0UsSUFBSSxDQUFDRixLQUFLRyxHQUFHLENBQUNOLHFEQUFLQSxDQUFDRSxXQUFXQyxLQUFLSSxHQUFHLElBQUk7QUFDckUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvdGhyZXNob2xkL3N0dXJnZXMuanM/MzIyZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY291bnQgZnJvbSBcIi4uL2NvdW50LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHRocmVzaG9sZFN0dXJnZXModmFsdWVzKSB7XG4gIHJldHVybiBNYXRoLm1heCgxLCBNYXRoLmNlaWwoTWF0aC5sb2coY291bnQodmFsdWVzKSkgLyBNYXRoLkxOMikgKyAxKTtcbn1cbiJdLCJuYW1lcyI6WyJjb3VudCIsInRocmVzaG9sZFN0dXJnZXMiLCJ2YWx1ZXMiLCJNYXRoIiwibWF4IiwiY2VpbCIsImxvZyIsIkxOMiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/threshold/sturges.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/ticks.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/ticks.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ticks),\n/* harmony export */   tickIncrement: () => (/* binding */ tickIncrement),\n/* harmony export */   tickStep: () => (/* binding */ tickStep)\n/* harmony export */ });\nconst e10 = Math.sqrt(50), e5 = Math.sqrt(10), e2 = Math.sqrt(2);\nfunction tickSpec(start, stop, count) {\n    const step = (stop - start) / Math.max(0, count), power = Math.floor(Math.log10(step)), error = step / Math.pow(10, power), factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n    let i1, i2, inc;\n    if (power < 0) {\n        inc = Math.pow(10, -power) / factor;\n        i1 = Math.round(start * inc);\n        i2 = Math.round(stop * inc);\n        if (i1 / inc < start) ++i1;\n        if (i2 / inc > stop) --i2;\n        inc = -inc;\n    } else {\n        inc = Math.pow(10, power) * factor;\n        i1 = Math.round(start / inc);\n        i2 = Math.round(stop / inc);\n        if (i1 * inc < start) ++i1;\n        if (i2 * inc > stop) --i2;\n    }\n    if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n    return [\n        i1,\n        i2,\n        inc\n    ];\n}\nfunction ticks(start, stop, count) {\n    stop = +stop, start = +start, count = +count;\n    if (!(count > 0)) return [];\n    if (start === stop) return [\n        start\n    ];\n    const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n    if (!(i2 >= i1)) return [];\n    const n = i2 - i1 + 1, ticks = new Array(n);\n    if (reverse) {\n        if (inc < 0) for(let i = 0; i < n; ++i)ticks[i] = (i2 - i) / -inc;\n        else for(let i = 0; i < n; ++i)ticks[i] = (i2 - i) * inc;\n    } else {\n        if (inc < 0) for(let i = 0; i < n; ++i)ticks[i] = (i1 + i) / -inc;\n        else for(let i = 0; i < n; ++i)ticks[i] = (i1 + i) * inc;\n    }\n    return ticks;\n}\nfunction tickIncrement(start, stop, count) {\n    stop = +stop, start = +start, count = +count;\n    return tickSpec(start, stop, count)[2];\n}\nfunction tickStep(start, stop, count) {\n    stop = +stop, start = +start, count = +count;\n    const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n    return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/ticks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/transpose.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/transpose.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ transpose)\n/* harmony export */ });\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./min.js */ \"(ssr)/./node_modules/d3-array/src/min.js\");\n\nfunction transpose(matrix) {\n    if (!(n = matrix.length)) return [];\n    for(var i = -1, m = (0,_min_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(matrix, length), transpose = new Array(m); ++i < m;){\n        for(var j = -1, n, row = transpose[i] = new Array(n); ++j < n;){\n            row[j] = matrix[j][i];\n        }\n    }\n    return transpose;\n}\nfunction length(d) {\n    return d.length;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RyYW5zcG9zZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQjtBQUVaLFNBQVNDLFVBQVVDLE1BQU07SUFDdEMsSUFBSSxDQUFFQyxDQUFBQSxJQUFJRCxPQUFPRSxNQUFNLEdBQUcsT0FBTyxFQUFFO0lBQ25DLElBQUssSUFBSUMsSUFBSSxDQUFDLEdBQUdDLElBQUlOLG1EQUFHQSxDQUFDRSxRQUFRRSxTQUFTSCxZQUFZLElBQUlNLE1BQU1ELElBQUksRUFBRUQsSUFBSUMsR0FBSTtRQUM1RSxJQUFLLElBQUlFLElBQUksQ0FBQyxHQUFHTCxHQUFHTSxNQUFNUixTQUFTLENBQUNJLEVBQUUsR0FBRyxJQUFJRSxNQUFNSixJQUFJLEVBQUVLLElBQUlMLEdBQUk7WUFDL0RNLEdBQUcsQ0FBQ0QsRUFBRSxHQUFHTixNQUFNLENBQUNNLEVBQUUsQ0FBQ0gsRUFBRTtRQUN2QjtJQUNGO0lBQ0EsT0FBT0o7QUFDVDtBQUVBLFNBQVNHLE9BQU9NLENBQUM7SUFDZixPQUFPQSxFQUFFTixNQUFNO0FBQ2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RyYW5zcG9zZS5qcz8wYjhkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBtaW4gZnJvbSBcIi4vbWluLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHRyYW5zcG9zZShtYXRyaXgpIHtcbiAgaWYgKCEobiA9IG1hdHJpeC5sZW5ndGgpKSByZXR1cm4gW107XG4gIGZvciAodmFyIGkgPSAtMSwgbSA9IG1pbihtYXRyaXgsIGxlbmd0aCksIHRyYW5zcG9zZSA9IG5ldyBBcnJheShtKTsgKytpIDwgbTspIHtcbiAgICBmb3IgKHZhciBqID0gLTEsIG4sIHJvdyA9IHRyYW5zcG9zZVtpXSA9IG5ldyBBcnJheShuKTsgKytqIDwgbjspIHtcbiAgICAgIHJvd1tqXSA9IG1hdHJpeFtqXVtpXTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHRyYW5zcG9zZTtcbn1cblxuZnVuY3Rpb24gbGVuZ3RoKGQpIHtcbiAgcmV0dXJuIGQubGVuZ3RoO1xufVxuIl0sIm5hbWVzIjpbIm1pbiIsInRyYW5zcG9zZSIsIm1hdHJpeCIsIm4iLCJsZW5ndGgiLCJpIiwibSIsIkFycmF5IiwiaiIsInJvdyIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/transpose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/union.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/union.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ union)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\nfunction union(...others) {\n    const set = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet();\n    for (const other of others){\n        for (const o of other){\n            set.add(o);\n        }\n    }\n    return set;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3VuaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DO0FBRXJCLFNBQVNDLE1BQU0sR0FBR0MsTUFBTTtJQUNyQyxNQUFNQyxNQUFNLElBQUlILGdEQUFTQTtJQUN6QixLQUFLLE1BQU1JLFNBQVNGLE9BQVE7UUFDMUIsS0FBSyxNQUFNRyxLQUFLRCxNQUFPO1lBQ3JCRCxJQUFJRyxHQUFHLENBQUNEO1FBQ1Y7SUFDRjtJQUNBLE9BQU9GO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvdW5pb24uanM/NTRlOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0ludGVyblNldH0gZnJvbSBcImludGVybm1hcFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1bmlvbiguLi5vdGhlcnMpIHtcbiAgY29uc3Qgc2V0ID0gbmV3IEludGVyblNldCgpO1xuICBmb3IgKGNvbnN0IG90aGVyIG9mIG90aGVycykge1xuICAgIGZvciAoY29uc3QgbyBvZiBvdGhlcikge1xuICAgICAgc2V0LmFkZChvKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHNldDtcbn1cbiJdLCJuYW1lcyI6WyJJbnRlcm5TZXQiLCJ1bmlvbiIsIm90aGVycyIsInNldCIsIm90aGVyIiwibyIsImFkZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/union.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/variance.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/variance.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ variance)\n/* harmony export */ });\nfunction variance(values, valueof) {\n    let count = 0;\n    let delta;\n    let mean = 0;\n    let sum = 0;\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value != null && (value = +value) >= value) {\n                delta = value - mean;\n                mean += delta / ++count;\n                sum += delta * (value - mean);\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n                delta = value - mean;\n                mean += delta / ++count;\n                sum += delta * (value - mean);\n            }\n        }\n    }\n    if (count > 1) return sum / (count - 1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/variance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/zip.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/zip.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ zip)\n/* harmony export */ });\n/* harmony import */ var _transpose_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transpose.js */ \"(ssr)/./node_modules/d3-array/src/transpose.js\");\n\nfunction zip() {\n    return (0,_transpose_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3ppcC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QztBQUV4QixTQUFTQztJQUN0QixPQUFPRCx5REFBU0EsQ0FBQ0U7QUFDbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvemlwLmpzPzIzZjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHRyYW5zcG9zZSBmcm9tIFwiLi90cmFuc3Bvc2UuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gemlwKCkge1xuICByZXR1cm4gdHJhbnNwb3NlKGFyZ3VtZW50cyk7XG59XG4iXSwibmFtZXMiOlsidHJhbnNwb3NlIiwiemlwIiwiYXJndW1lbnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/zip.js\n");

/***/ })

};
;