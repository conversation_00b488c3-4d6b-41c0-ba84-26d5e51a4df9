"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel-auto-scroll";
exports.ids = ["vendor-chunks/embla-carousel-auto-scroll"];
exports.modules = {

/***/ "(ssr)/./node_modules/embla-carousel-auto-scroll/esm/embla-carousel-auto-scroll.esm.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/embla-carousel-auto-scroll/esm/embla-carousel-auto-scroll.esm.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AutoScroll)\n/* harmony export */ });\nconst defaultOptions = {\n    direction: \"forward\",\n    speed: 2,\n    startDelay: 1000,\n    active: true,\n    breakpoints: {},\n    playOnInit: true,\n    stopOnFocusIn: true,\n    stopOnInteraction: true,\n    stopOnMouseEnter: false,\n    rootNode: null\n};\nfunction AutoScroll(userOptions = {}) {\n    let options;\n    let emblaApi;\n    let destroyed;\n    let playing = false;\n    let resume = true;\n    let timer = 0;\n    let startDelay;\n    let defaultScrollBehaviour;\n    function init(emblaApiInstance, optionsHandler) {\n        emblaApi = emblaApiInstance;\n        const { mergeOptions, optionsAtMedia } = optionsHandler;\n        const optionsBase = mergeOptions(defaultOptions, AutoScroll.globalOptions);\n        const allOptions = mergeOptions(optionsBase, userOptions);\n        options = optionsAtMedia(allOptions);\n        if (emblaApi.scrollSnapList().length <= 1) return;\n        startDelay = options.startDelay;\n        destroyed = false;\n        defaultScrollBehaviour = emblaApi.internalEngine().scrollBody;\n        const { eventStore } = emblaApi.internalEngine();\n        const emblaRoot = emblaApi.rootNode();\n        const root = options.rootNode && options.rootNode(emblaRoot) || emblaRoot;\n        const container = emblaApi.containerNode();\n        emblaApi.on(\"pointerDown\", stopScroll);\n        if (!options.stopOnInteraction) {\n            emblaApi.on(\"pointerUp\", startScrollOnSettle);\n        }\n        if (options.stopOnMouseEnter) {\n            eventStore.add(root, \"mouseenter\", ()=>{\n                resume = false;\n                stopScroll();\n            });\n            if (!options.stopOnInteraction) {\n                eventStore.add(root, \"mouseleave\", ()=>{\n                    resume = true;\n                    startScroll();\n                });\n            }\n        }\n        if (options.stopOnFocusIn) {\n            eventStore.add(container, \"focusin\", ()=>{\n                stopScroll();\n                emblaApi.scrollTo(emblaApi.selectedScrollSnap(), true);\n            });\n            if (!options.stopOnInteraction) {\n                eventStore.add(container, \"focusout\", startScroll);\n            }\n        }\n        if (options.playOnInit) startScroll();\n    }\n    function destroy() {\n        emblaApi.off(\"pointerDown\", stopScroll).off(\"pointerUp\", startScrollOnSettle).off(\"settle\", onSettle);\n        stopScroll();\n        destroyed = true;\n        playing = false;\n    }\n    function startScroll() {\n        if (destroyed || playing) return;\n        if (!resume) return;\n        emblaApi.emit(\"autoScroll:play\");\n        const engine = emblaApi.internalEngine();\n        const { ownerWindow } = engine;\n        timer = ownerWindow.setTimeout(()=>{\n            engine.scrollBody = createAutoScrollBehaviour(engine);\n            engine.animation.start();\n        }, startDelay);\n        playing = true;\n    }\n    function stopScroll() {\n        if (destroyed || !playing) return;\n        emblaApi.emit(\"autoScroll:stop\");\n        const engine = emblaApi.internalEngine();\n        const { ownerWindow } = engine;\n        engine.scrollBody = defaultScrollBehaviour;\n        ownerWindow.clearTimeout(timer);\n        timer = 0;\n        playing = false;\n    }\n    function onSettle() {\n        if (resume) startScroll();\n        emblaApi.off(\"settle\", onSettle);\n    }\n    function startScrollOnSettle() {\n        emblaApi.on(\"settle\", onSettle);\n    }\n    function createAutoScrollBehaviour(engine) {\n        const { location, target, scrollTarget, index, indexPrevious, limit: { reachedMin, reachedMax, constrain }, options: { loop } } = engine;\n        const directionSign = options.direction === \"forward\" ? -1 : 1;\n        const noop = ()=>self;\n        let bodyVelocity = 0;\n        let scrollDirection = 0;\n        let rawLocation = location.get();\n        let rawLocationPrevious = 0;\n        let hasSettled = false;\n        function seek() {\n            let directionDiff = 0;\n            bodyVelocity = directionSign * options.speed;\n            rawLocation += bodyVelocity;\n            location.add(bodyVelocity);\n            target.set(location);\n            directionDiff = rawLocation - rawLocationPrevious;\n            scrollDirection = Math.sign(directionDiff);\n            rawLocationPrevious = rawLocation;\n            const currentIndex = scrollTarget.byDistance(0, false).index;\n            if (index.get() !== currentIndex) {\n                indexPrevious.set(index.get());\n                index.set(currentIndex);\n                emblaApi.emit(\"select\");\n            }\n            const reachedEnd = options.direction === \"forward\" ? reachedMin(location.get()) : reachedMax(location.get());\n            if (!loop && reachedEnd) {\n                hasSettled = true;\n                const constrainedLocation = constrain(location.get());\n                location.set(constrainedLocation);\n                target.set(location);\n                stopScroll();\n            }\n            return self;\n        }\n        const self = {\n            direction: ()=>scrollDirection,\n            duration: ()=>-1,\n            velocity: ()=>bodyVelocity,\n            settled: ()=>hasSettled,\n            seek,\n            useBaseFriction: noop,\n            useBaseDuration: noop,\n            useFriction: noop,\n            useDuration: noop\n        };\n        return self;\n    }\n    function play(startDelayOverride) {\n        if (typeof startDelayOverride !== \"undefined\") {\n            startDelay = startDelayOverride;\n        }\n        resume = true;\n        startScroll();\n    }\n    function stop() {\n        if (playing) stopScroll();\n    }\n    function reset() {\n        if (playing) {\n            stopScroll();\n            startScrollOnSettle();\n        }\n    }\n    function isPlaying() {\n        return playing;\n    }\n    const self = {\n        name: \"autoScroll\",\n        options: userOptions,\n        init,\n        destroy,\n        play,\n        stop,\n        reset,\n        isPlaying\n    };\n    return self;\n}\nAutoScroll.globalOptions = undefined;\n //# sourceMappingURL=embla-carousel-auto-scroll.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/embla-carousel-auto-scroll/esm/embla-carousel-auto-scroll.esm.js\n");

/***/ })

};
;