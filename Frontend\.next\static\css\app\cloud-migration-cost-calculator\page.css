/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/styles/breakpoints.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/


/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/styles/variables.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/* Colors */
/* End Colors */

/* SPACING */
/* END SPACING */

/* FONT SIZES */
/* END FONT SIZES */

/*FONT WEIGHTS */
/* END FONT WEIGHTS */
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CloudMigrationQuestionAndAnswers/CloudMigrationQuestionAndAnswers.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.CloudMigrationQuestionAndAnswers_container__23uHh {
  display: flex;
  max-width: 1192px;
  gap: 20px;
  flex-direction: column;
}

.CloudMigrationQuestionAndAnswers_question_container__GaNjn {
  display: flex;
  width: -moz-fit-content;
  width: fit-content;
  gap: 10px;
  padding: 20px;

  @media screen and (max-width: 1024px) {
    width: 100%;
  }
}

.CloudMigrationQuestionAndAnswers_question_number__C7Rlp {
  font-weight: 600;
  font-size: 20px;
  line-height: 148%;
}

.CloudMigrationQuestionAndAnswers_sub_question_name__bSje1 {
  min-width: 100%;
  font-weight: 600;
  font-size: 18px;
  line-height: 164%;
}

.CloudMigrationQuestionAndAnswers_question_name__5tq0J {
  width: -moz-fit-content;
  width: fit-content;
  font-weight: 600;
  font-size: 20px;
  line-height: 148%;
}

.CloudMigrationQuestionAndAnswers_error_message__jiAyq {
  color: #ff0000;
}

.CloudMigrationQuestionAndAnswers_mcqs_container__alqLv {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  @media screen and (max-width: 1024px) {
    padding: 20px;
  }
}

.CloudMigrationQuestionAndAnswers_selected_mcq__lkerl {
  background-image: linear-gradient(#F3F3F3, #F3F3F3),
    linear-gradient(
      93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%
    );
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}

.CloudMigrationQuestionAndAnswers_mcq__hzM2A {
  background-color: #F3F3F3;
  width: -moz-fit-content;
  width: fit-content;
  padding: 12px;
  border-radius: 12px;
  font-weight: 400;
  font-size: 16px;
  line-height: 160%;
  position: relative;
  border: 2px solid transparent;
  cursor: pointer;
}

/* Support both radio and checkbox inputs */
.CloudMigrationQuestionAndAnswers_mcq__hzM2A > input[type='radio'],
.CloudMigrationQuestionAndAnswers_mcq__hzM2A > input[type='checkbox'] {
  display: none;
}

/* Cloud migration specific styling for multi-select questions */
.CloudMigrationQuestionAndAnswers_mcq__hzM2A.CloudMigrationQuestionAndAnswers_multi_select__6Y7F1 {
  position: relative;
}

.CloudMigrationQuestionAndAnswers_mcq__hzM2A.CloudMigrationQuestionAndAnswers_multi_select__6Y7F1::after {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 16px;
  height: 16px;
  border: 2px solid #666;
  border-radius: 3px;
  background: transparent;
}

.CloudMigrationQuestionAndAnswers_mcq__hzM2A.CloudMigrationQuestionAndAnswers_multi_select__6Y7F1.CloudMigrationQuestionAndAnswers_selected_mcq__lkerl::after {
  background: #30ad43;
  border-color: #30ad43;
}

.CloudMigrationQuestionAndAnswers_mcq__hzM2A.CloudMigrationQuestionAndAnswers_multi_select__6Y7F1.CloudMigrationQuestionAndAnswers_selected_mcq__lkerl::before {
  content: '✓';
  position: absolute;
  top: 6px;
  right: 10px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  z-index: 2;
}

.CloudMigrationQuestionAndAnswers_draggable_container__1Y__p {
  width: -webkit-fill-available;
  height: min-content;
  background-color: #F3F3F3;
  padding: 20px;
  border-radius: 12px;
}

.CloudMigrationQuestionAndAnswers_draggable_input__xnr9t {
  -webkit-appearance: none;
  width: -webkit-fill-available;
  height: 7px;
  background: transparent;
  border-radius: 3px;
  outline: none;
  margin: 20px 30px;
  cursor: pointer;

  @media screen and (max-width: 1200px) {
    width: 90%;
  }
}

.CloudMigrationQuestionAndAnswers_draggable_input__xnr9t::-webkit-slider-runnable-track,
.CloudMigrationQuestionAndAnswers_draggable_input__xnr9t::-moz-range-track {
  height: 7px;
}

.CloudMigrationQuestionAndAnswers_draggable_input__xnr9t::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background: #30ad43;
  border: none;
  cursor: pointer;
}

.CloudMigrationQuestionAndAnswers_draggable_input__xnr9t::-moz-range-thumb {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background: #30ad43;
  border: none;
  cursor: pointer;
}

.CloudMigrationQuestionAndAnswers_draggable_wrapper__RlBQV {
  display: flex;
  justify-content: space-between;
}

.CloudMigrationQuestionAndAnswers_draggable_label__QSi_F {
  cursor: pointer;
  text-align: center;
  width: 127px;
  height: 44px;
}

.CloudMigrationQuestionAndAnswers_selected_draggable_label__YiCHA {
  background: linear-gradient(
    to right,
    #FEBE10,
    #F47A37,
    #F05443,
    #D91A5F,
    #B41F5E
  );
  -webkit-background-clip: text;
          background-clip: text;
  color: transparent;
}

.CloudMigrationQuestionAndAnswers_number_wrapper__h26KF {
  display: flex;
  flex-direction: column;
}

.CloudMigrationQuestionAndAnswers_draggable_container_tablet___3apO {
  display: flex;
  flex-direction: row;
  padding: 20px;
}

.CloudMigrationQuestionAndAnswers_number__hjPYf {
  width: 20%;
  height: 62px;
  padding: 10px;
  border: 0.5px solid #8c8b8b;
  font-weight: 500;
  font-size: 26px;
  line-height: 164%;
  text-align: center;
}

.CloudMigrationQuestionAndAnswers_number__hjPYf:first-child {
  border-radius: 10px 0 0 10px;
}

.CloudMigrationQuestionAndAnswers_number__hjPYf:last-child {
  border-radius: 0 10px 10px 0;
}

.CloudMigrationQuestionAndAnswers_number_label__AasKk {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}

.CloudMigrationQuestionAndAnswers_number_label__AasKk > span {
  text-align: center;
  width: 20%;
}

.CloudMigrationQuestionAndAnswers_selected_number__w7Tob {
  background-color: #30ad43;
}

.CloudMigrationQuestionAndAnswers_number__hjPYf > input[type='radio'] {
  display: none;
}

/* Cloud migration specific enhancements */
.CloudMigrationQuestionAndAnswers_cloud_migration_container__gOkFf {
  border-left: 4px solid #30ad43;
  padding-left: 16px;
}

.CloudMigrationQuestionAndAnswers_cost_indicator__0MgM9 {
  font-size: 14px;
  color: #666;
  font-style: italic;
  margin-top: 4px;
}

.CloudMigrationQuestionAndAnswers_required_field__2pyY6 {
  color: #ff0000;
}

.CloudMigrationQuestionAndAnswers_required_field__2pyY6::after {
  content: ' *';
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/VideoModal/VideoModal.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
.VideoModal_respcontainer__rM1yN {
  position: relative;
  overflow: hidden;
  padding-top: 56.25%;
}

.VideoModal_respiframe__0Aj7P {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.VideoModal_modal-header__klYOg {
  border-bottom: 0;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Breadcrumb/Breadcrumb.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
.Breadcrumb_breadcrumb__Q0xQA {

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.Breadcrumb_breadcrumb__Q0xQA a {
  color: #FFFFFF;
  text-decoration: none;
}

.Breadcrumb_arrow_style___ICIk {
  color: #FFFFFF;
}
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/styles/typography.module.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
.typography_h1__DecPZ {
  font-size: 78px;

  font-weight: 700;

  @media (max-width: 450px) {
    font-size: 48px;
  }
}

.typography_h2__Dn0zf {
  font-size: 64px;

  @media (max-width: 450px) {
    font-size: 44px;
  }
}

.typography_h3__o3Abb {
  font-size: 52px;

  @media (max-width: 450px) {
    font-size: 40px;
  }
}

.typography_h4__lGrWj {
  font-size: 40px;
  font-weight: 600;

  @media (max-width: 450px) {
    font-size: 28px;
  }
}

.typography_h5__DGJHL {
  font-size: 32px;
  font-weight: 600;

  @media (max-width: 450px) {
    font-size: 22px;
  }
}

.typography_h6__vf_A0 {
  font-size: 24px;
  font-weight: 600;

  @media (max-width: 450px) {
    font-size: 18px;
  }
}

.typography_caption__hfk0A {
  font-size: 12px;
  line-height: 1.67;
  font-weight: normal;
  letter-spacing: 0.01em;
  max-width: 50em;
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Heading/Heading.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
.Heading_center__XBGsG {
  text-align: center;
}
.Heading_left__ouHog {
  text-align: left;
}
.Heading_right__jsN_Y {
  text-align: right;
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CircularButtonWithArrow/CircularButtonWithArrow.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************/
.CircularButtonWithArrow_container__9Cvr1 {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.CircularButtonWithArrow_circle__H7jjo {
  position: relative;
  width: 100%;
  height: 100%;
}

.CircularButtonWithArrow_arrow__h3ojH {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.3s ease-in-out;
}

.CircularButtonWithArrow_container__9Cvr1:hover .CircularButtonWithArrow_arrow__h3ojH {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.CircularButtonWithArrow_arrowImage__G7E_X {
  width: 100%;
  height: 100%;
}

.CircularButtonWithArrow_arrow_scroll__a_DTi {
  position: absolute;
  top: 33%;
  left: 21%;
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Button/Button.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
.Button_button__exqP_,
.Button_link__9n7Et {
  position: relative;
  background-color: #000000;
  background-image: linear-gradient(#000000, #000000),
    linear-gradient(93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;

  font-size: 14px;
  font-weight: 600;
  padding: 8px 14px;
  cursor: pointer;
  line-height: 21px;
  letter-spacing: 0.2px;
  color: #FFFFFF;
  transition: 0.2s linear;
  min-width: 120px;
  min-height: 40px;
  border: 2px solid transparent;
  /* Required to create the border effect */
  border-radius: 3px;
}


.Button_button__exqP_:hover {

  box-shadow:
    3px 3px 13px 0px #ffac7d82,
    6px -2px 11px 0px #ff72ae8f,
    -6px 3px 11px 1px #ffbb0057;
}

.Button_innerWrapper__ITLB1 {
  display: flex;
  justify-content: center;
  align-items: center;
}

.Button_leftWrapper__fWtI9 {
  margin-right: 8px;
}

.Button_rightWrapper__GkIh_ {
  margin-left: 8px;
}

.Button_link__9n7Et {
  display: inline-block;
  color: #FFFFFF;
  text-decoration: none;
}
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/ImageWithBlurPreview/ImageWithBlurPreview.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
.ImageWithBlurPreview_image_hidden__8NnZq {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.ImageWithBlurPreview_image_visible___YMAQ {
  opacity: 1;
}

.ImageWithBlurPreview_mobile_image__1Vbfb {
  object-fit: cover;
  object-position: center center;
  
  @media screen and (max-width: 800px) {
    object-position: center 20%;
  }
}

/* Show desktop images only on desktop screens when mobile image is available */
.ImageWithBlurPreview_desktop_only__JS73q {
  display: block;
  
  @media screen and (max-width: 1023px) {
    display: none !important;
  }
}

/* Show mobile images only on mobile and tablet screens */
.ImageWithBlurPreview_mobile_tablet_only__y4ZJ9 {
  display: none;

  @media screen and (max-width: 1023px) {
    display: block;
  }
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/HeroSection/HeroSection.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
.HeroSection_main_container__a_G6M {
  position: relative;
  overflow: hidden;

  /* Ensure proper height for mobile images */
  @media screen and (max-width: 1023px) {
    min-height: 500px;
  }

  @media screen and (max-width: 767px) {
    min-height: 450px;
  }
}

.HeroSection_inner_container__DZ5z1 {
  display: flex;
  flex-direction: column;
  gap: 167px;
  min-height: 741px;
  max-width: 50%;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 2000px) {
    max-width: 70%;
  }

  @media screen and (max-width: 768px) {
    max-width: 100%;
    padding: 20px 30px 0 30px !important;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px !important;
  }
}

.HeroSection_inner_container_resources__1RjK7 {
  display: flex;
  flex-direction: column;
  gap: 74px;
  min-height: 441px;
  max-width: 803px;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 768px) {
    padding: 20px 30px 0 30px;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px;
  }
}

.HeroSection_inner_container_partners__6Ff8X {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 167px;
  min-height: 741px;
  max-width: 50%;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 2000px) {
    max-width: 70%;
  }

  @media screen and (max-width: 768px) {
    max-width: 100%;
    padding: 20px 30px 0 30px !important;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px !important;
  }
}

.HeroSection_section__nzkUY {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.HeroSection_section_without_breadcrumbs__SJ09S {
  margin-top: 188px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  font-size: 20px;
}

.HeroSection_tag__ZxYj0 {
  color: #FFFFFF;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background-color: #000000;
  padding: 2px 6px;
  width: -moz-fit-content;
  width: fit-content;
  border-radius: 3px;
}

.HeroSection_background_image__CGewD {
  background-color: #000000;
  object-fit: cover;
  transition: transform 0.5s ease;
  z-index: -1;

  /* Responsive object-fit for mobile and tablet screens to prevent cutoff */
  @media screen and (max-width: 1023px) {
    object-fit: cover;
    object-position: center top;
  }

  /* For very small mobile screens, ensure full image visibility */
  @media screen and (max-width: 800px) {
    object-fit: fill;
    object-position: center center;
  }
}

.HeroSection_main_container__a_G6M:hover .HeroSection_background_image__CGewD {
  transform: scale(1.1);
}

.HeroSection_title__im1kR > h1 {
  font-size: 78px;
  font-style: normal;
  font-weight: 600;
  line-height: 113%;
  color: #FFFFFF;

  @media screen and (max-width: 768px) {
    font-size: 64px;
    line-height: 76.8px;
    letter-spacing: 1.28px;
  }

  @media screen and (max-width: 450px) {
    font-size: 44px;
    line-height: 118%;
    letter-spacing: -1.76px;
  }
}

.HeroSection_hero_desc__ui48U {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  color: #FFFFFF;
}

.HeroSection_btn__6f4Xt {
  height: 62px;
  width: 192px;
  font-size: 20px;
  padding: 16px 36px;
}

.HeroSection_inner_container_ai_readiness__FsE8Q {
  max-width: 60%;
  padding: 70px 0 105px 138px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 1440px) {
    padding: 0 0 91px 112px;
  }

  @media screen and (max-width: 1024px) {
    max-width: 100%;
    padding: 24px 20px;
  }

  @media screen and (max-width: 450px) {
    padding: 24px 4px;
  }
}

.HeroSection_heading__jTq5w > h1 {
  font-weight: 700;
  font-size: 48px;
  line-height: 129%;
  color: #FFFFFF;
}

.HeroSection_description__tUvZp {
  font-weight: 400;
  font-size: 22px;
  line-height: 160%;
  color: #FFFFFF;
}

.HeroSection_description__tUvZp > p {
  margin: 0;
}

.HeroSection_cta__drDMW {
  font-weight: 600;
  font-size: 20px;
  line-height: 100%;
  width: 260px;
  height: 62px;
  padding: 16px 36px;
  border-radius: 6px;
  border-width: 2px;
}

.HeroSection_cta_cloud_migration__wp28c {
  font-weight: 600;
  font-size: 20px;
  line-height: 1.2; 
  width: 260px;
  min-height: 62px; 
  padding: 16px;
  border-radius: 6px;
  border-width: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
}
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CloudMigrationStep/CloudMigrationStep.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
.CloudMigrationStep_container__2i9Rk {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 824px;
  margin: 0 auto;
  align-items: flex-start;
  padding-bottom: 40px;
}

.CloudMigrationStep_stepWrapper__oYD_I {
  display: flex;
  flex-direction: column;
  gap: 9px;
  align-items: center;
  position: relative;
}

.CloudMigrationStep_circle__KlE2K {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 1px solid #646464;
  color: #646464;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 400;
  z-index: 1;
  cursor: not-allowed;
}

.CloudMigrationStep_active_circle__Scn1z {
  color: #fff !important;
  border-color: #f05443 !important;
  background-color: #f05443;
  cursor: pointer;
}

.CloudMigrationStep_selected_circle__YOG19 {
  color: #fff !important;
  border-color: #f05443 !important;
  background-color: #f05443;
  cursor: pointer;
}

.CloudMigrationStep_label__RepqJ {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 139%;
  width: 128px;
  height: 42px;
  text-align: center;
  color: #000000;
  margin-bottom: 0;
  cursor: not-allowed;
}

.CloudMigrationStep_active_text__O2dRR {
  color: #f05443 !important;
  cursor: pointer;
}

.CloudMigrationStep_selected_text__0iTq5 {
  color: #f05443 !important;
  cursor: pointer;
}

.CloudMigrationStep_line__0Ht5Y {
  height: 2px;
  width: 121px;
  background-color: #646464;
  position: absolute;
  left: calc(100% + -38px);
  top: 24%;
  z-index: 0;

  @media screen and (max-width: 820px) {
    width: 13.5vw;
    left: 12vw;
  }
}

.CloudMigrationStep_line__0Ht5Y.CloudMigrationStep_active__zaJ8U {
  background-color: #f05443;
}

/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./node_modules/react-phone-input-2/lib/style.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
.react-tel-input{font-family:'Roboto',sans-serif;font-size:15px;position:relative;width:100%}.react-tel-input :disabled{cursor:not-allowed}.react-tel-input .flag{width:16px;height:11px;background-image:url(data:image/png;base64,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)}.react-tel-input .ad{background-position:-16px 0}.react-tel-input .ae{background-position:-32px 0}.react-tel-input .af{background-position:-48px 0}.react-tel-input .ag{background-position:-64px 0}.react-tel-input .ai{background-position:-80px 0}.react-tel-input .al{background-position:-96px 0}.react-tel-input .am{background-position:-112px 0}.react-tel-input .ao{background-position:-128px 0}.react-tel-input .ar{background-position:-144px 0}.react-tel-input .as{background-position:-160px 0}.react-tel-input .at{background-position:-176px 0}.react-tel-input .au{background-position:-192px 0}.react-tel-input .aw{background-position:-208px 0}.react-tel-input .az{background-position:-224px 0}.react-tel-input .ba{background-position:-240px 0}.react-tel-input .bb{background-position:0 -11px}.react-tel-input .bd{background-position:-16px -11px}.react-tel-input .be{background-position:-32px -11px}.react-tel-input .bf{background-position:-48px -11px}.react-tel-input .bg{background-position:-64px -11px}.react-tel-input .bh{background-position:-80px -11px}.react-tel-input .bi{background-position:-96px -11px}.react-tel-input .bj{background-position:-112px -11px}.react-tel-input .bm{background-position:-128px -11px}.react-tel-input .bn{background-position:-144px -11px}.react-tel-input .bo{background-position:-160px -11px}.react-tel-input .br{background-position:-176px -11px}.react-tel-input .bs{background-position:-192px -11px}.react-tel-input .bt{background-position:-208px -11px}.react-tel-input .bw{background-position:-224px -11px}.react-tel-input .by{background-position:-240px -11px}.react-tel-input .bz{background-position:0 -22px}.react-tel-input .ca{background-position:-16px -22px}.react-tel-input .cd{background-position:-32px -22px}.react-tel-input .cf{background-position:-48px -22px}.react-tel-input .cg{background-position:-64px -22px}.react-tel-input .ch{background-position:-80px -22px}.react-tel-input .ci{background-position:-96px -22px}.react-tel-input .ck{background-position:-112px -22px}.react-tel-input .cl{background-position:-128px -22px}.react-tel-input .cm{background-position:-144px -22px}.react-tel-input .cn{background-position:-160px -22px}.react-tel-input .co{background-position:-176px -22px}.react-tel-input .cr{background-position:-192px -22px}.react-tel-input .cu{background-position:-208px -22px}.react-tel-input .cv{background-position:-224px -22px}.react-tel-input .cw{background-position:-240px -22px}.react-tel-input .cy{background-position:0 -33px}.react-tel-input .cz{background-position:-16px -33px}.react-tel-input .de{background-position:-32px -33px}.react-tel-input .dj{background-position:-48px -33px}.react-tel-input .dk{background-position:-64px -33px}.react-tel-input .dm{background-position:-80px -33px}.react-tel-input .do{background-position:-96px -33px}.react-tel-input .dz{background-position:-112px -33px}.react-tel-input .ec{background-position:-128px -33px}.react-tel-input .ee{background-position:-144px -33px}.react-tel-input .eg{background-position:-160px -33px}.react-tel-input .er{background-position:-176px -33px}.react-tel-input .es{background-position:-192px -33px}.react-tel-input .et{background-position:-208px -33px}.react-tel-input .fi{background-position:-224px -33px}.react-tel-input .fj{background-position:-240px -33px}.react-tel-input .fk{background-position:0 -44px}.react-tel-input .fm{background-position:-16px -44px}.react-tel-input .fo{background-position:-32px -44px}.react-tel-input .fr,.react-tel-input .bl,.react-tel-input .mf{background-position:-48px -44px}.react-tel-input .ga{background-position:-64px -44px}.react-tel-input .gb{background-position:-80px -44px}.react-tel-input .gd{background-position:-96px -44px}.react-tel-input .ge{background-position:-112px -44px}.react-tel-input .gf{background-position:-128px -44px}.react-tel-input .gh{background-position:-144px -44px}.react-tel-input .gi{background-position:-160px -44px}.react-tel-input .gl{background-position:-176px -44px}.react-tel-input .gm{background-position:-192px -44px}.react-tel-input .gn{background-position:-208px -44px}.react-tel-input .gp{background-position:-224px -44px}.react-tel-input .gq{background-position:-240px -44px}.react-tel-input .gr{background-position:0 -55px}.react-tel-input .gt{background-position:-16px -55px}.react-tel-input .gu{background-position:-32px -55px}.react-tel-input .gw{background-position:-48px -55px}.react-tel-input .gy{background-position:-64px -55px}.react-tel-input .hk{background-position:-80px -55px}.react-tel-input .hn{background-position:-96px -55px}.react-tel-input .hr{background-position:-112px -55px}.react-tel-input .ht{background-position:-128px -55px}.react-tel-input .hu{background-position:-144px -55px}.react-tel-input .id{background-position:-160px -55px}.react-tel-input .ie{background-position:-176px -55px}.react-tel-input .il{background-position:-192px -55px}.react-tel-input .in{background-position:-208px -55px}.react-tel-input .io{background-position:-224px -55px}.react-tel-input .iq{background-position:-240px -55px}.react-tel-input .ir{background-position:0 -66px}.react-tel-input .is{background-position:-16px -66px}.react-tel-input .it{background-position:-32px -66px}.react-tel-input .je{background-position:-144px -154px}.react-tel-input .jm{background-position:-48px -66px}.react-tel-input .jo{background-position:-64px -66px}.react-tel-input .jp{background-position:-80px -66px}.react-tel-input .ke{background-position:-96px -66px}.react-tel-input .kg{background-position:-112px -66px}.react-tel-input .kh{background-position:-128px -66px}.react-tel-input .ki{background-position:-144px -66px}.react-tel-input .xk{background-position:-128px -154px}.react-tel-input .km{background-position:-160px -66px}.react-tel-input .kn{background-position:-176px -66px}.react-tel-input .kp{background-position:-192px -66px}.react-tel-input .kr{background-position:-208px -66px}.react-tel-input .kw{background-position:-224px -66px}.react-tel-input .ky{background-position:-240px -66px}.react-tel-input .kz{background-position:0 -77px}.react-tel-input .la{background-position:-16px -77px}.react-tel-input .lb{background-position:-32px -77px}.react-tel-input .lc{background-position:-48px -77px}.react-tel-input .li{background-position:-64px -77px}.react-tel-input .lk{background-position:-80px -77px}.react-tel-input .lr{background-position:-96px -77px}.react-tel-input .ls{background-position:-112px -77px}.react-tel-input .lt{background-position:-128px -77px}.react-tel-input .lu{background-position:-144px -77px}.react-tel-input .lv{background-position:-160px -77px}.react-tel-input .ly{background-position:-176px -77px}.react-tel-input .ma{background-position:-192px -77px}.react-tel-input .mc{background-position:-208px -77px}.react-tel-input .md{background-position:-224px -77px}.react-tel-input .me{background-position:-112px -154px;height:12px}.react-tel-input .mg{background-position:0 -88px}.react-tel-input .mh{background-position:-16px -88px}.react-tel-input .mk{background-position:-32px -88px}.react-tel-input .ml{background-position:-48px -88px}.react-tel-input .mm{background-position:-64px -88px}.react-tel-input .mn{background-position:-80px -88px}.react-tel-input .mo{background-position:-96px -88px}.react-tel-input .mp{background-position:-112px -88px}.react-tel-input .mq{background-position:-128px -88px}.react-tel-input .mr{background-position:-144px -88px}.react-tel-input .ms{background-position:-160px -88px}.react-tel-input .mt{background-position:-176px -88px}.react-tel-input .mu{background-position:-192px -88px}.react-tel-input .mv{background-position:-208px -88px}.react-tel-input .mw{background-position:-224px -88px}.react-tel-input .mx{background-position:-240px -88px}.react-tel-input .my{background-position:0 -99px}.react-tel-input .mz{background-position:-16px -99px}.react-tel-input .na{background-position:-32px -99px}.react-tel-input .nc{background-position:-48px -99px}.react-tel-input .ne{background-position:-64px -99px}.react-tel-input .nf{background-position:-80px -99px}.react-tel-input .ng{background-position:-96px -99px}.react-tel-input .ni{background-position:-112px -99px}.react-tel-input .nl,.react-tel-input .bq{background-position:-128px -99px}.react-tel-input .no{background-position:-144px -99px}.react-tel-input .np{background-position:-160px -99px}.react-tel-input .nr{background-position:-176px -99px}.react-tel-input .nu{background-position:-192px -99px}.react-tel-input .nz{background-position:-208px -99px}.react-tel-input .om{background-position:-224px -99px}.react-tel-input .pa{background-position:-240px -99px}.react-tel-input .pe{background-position:0 -110px}.react-tel-input .pf{background-position:-16px -110px}.react-tel-input .pg{background-position:-32px -110px}.react-tel-input .ph{background-position:-48px -110px}.react-tel-input .pk{background-position:-64px -110px}.react-tel-input .pl{background-position:-80px -110px}.react-tel-input .pm{background-position:-96px -110px}.react-tel-input .pr{background-position:-112px -110px}.react-tel-input .ps{background-position:-128px -110px}.react-tel-input .pt{background-position:-144px -110px}.react-tel-input .pw{background-position:-160px -110px}.react-tel-input .py{background-position:-176px -110px}.react-tel-input .qa{background-position:-192px -110px}.react-tel-input .re{background-position:-208px -110px}.react-tel-input .ro{background-position:-224px -110px}.react-tel-input .rs{background-position:-240px -110px}.react-tel-input .ru{background-position:0 -121px}.react-tel-input .rw{background-position:-16px -121px}.react-tel-input .sa{background-position:-32px -121px}.react-tel-input .sb{background-position:-48px -121px}.react-tel-input .sc{background-position:-64px -121px}.react-tel-input .sd{background-position:-80px -121px}.react-tel-input .se{background-position:-96px -121px}.react-tel-input .sg{background-position:-112px -121px}.react-tel-input .sh{background-position:-128px -121px}.react-tel-input .si{background-position:-144px -121px}.react-tel-input .sk{background-position:-160px -121px}.react-tel-input .sl{background-position:-176px -121px}.react-tel-input .sm{background-position:-192px -121px}.react-tel-input .sn{background-position:-208px -121px}.react-tel-input .so{background-position:-224px -121px}.react-tel-input .sr{background-position:-240px -121px}.react-tel-input .ss{background-position:0 -132px}.react-tel-input .st{background-position:-16px -132px}.react-tel-input .sv{background-position:-32px -132px}.react-tel-input .sx{background-position:-48px -132px}.react-tel-input .sy{background-position:-64px -132px}.react-tel-input .sz{background-position:-80px -132px}.react-tel-input .tc{background-position:-96px -132px}.react-tel-input .td{background-position:-112px -132px}.react-tel-input .tg{background-position:-128px -132px}.react-tel-input .th{background-position:-144px -132px}.react-tel-input .tj{background-position:-160px -132px}.react-tel-input .tk{background-position:-176px -132px}.react-tel-input .tl{background-position:-192px -132px}.react-tel-input .tm{background-position:-208px -132px}.react-tel-input .tn{background-position:-224px -132px}.react-tel-input .to{background-position:-240px -132px}.react-tel-input .tr{background-position:0 -143px}.react-tel-input .tt{background-position:-16px -143px}.react-tel-input .tv{background-position:-32px -143px}.react-tel-input .tw{background-position:-48px -143px}.react-tel-input .tz{background-position:-64px -143px}.react-tel-input .ua{background-position:-80px -143px}.react-tel-input .ug{background-position:-96px -143px}.react-tel-input .us{background-position:-112px -143px}.react-tel-input .uy{background-position:-128px -143px}.react-tel-input .uz{background-position:-144px -143px}.react-tel-input .va{background-position:-160px -143px}.react-tel-input .vc{background-position:-176px -143px}.react-tel-input .ve{background-position:-192px -143px}.react-tel-input .vg{background-position:-208px -143px}.react-tel-input .vi{background-position:-224px -143px}.react-tel-input .vn{background-position:-240px -143px}.react-tel-input .vu{background-position:0 -154px}.react-tel-input .wf{background-position:-16px -154px}.react-tel-input .ws{background-position:-32px -154px}.react-tel-input .ye{background-position:-48px -154px}.react-tel-input .za{background-position:-64px -154px}.react-tel-input .zm{background-position:-80px -154px}.react-tel-input .zw{background-position:-96px -154px}.react-tel-input *{box-sizing:border-box;-moz-box-sizing:border-box}.react-tel-input .hide{display:none}.react-tel-input .v-hide{visibility:hidden}.react-tel-input .form-control{position:relative;font-size:14px;letter-spacing:.01rem;margin-top:0 !important;margin-bottom:0 !important;padding-left:48px;margin-left:0;background:#FFFFFF;border:1px solid #CACACA;border-radius:5px;line-height:25px;height:35px;width:300px;outline:none}.react-tel-input .form-control.invalid-number{border:1px solid #d79f9f;background-color:#FAF0F0;border-left-color:#cacaca}.react-tel-input .form-control.invalid-number:focus{border:1px solid #d79f9f;border-left-color:#cacaca;background-color:#FAF0F0}.react-tel-input .flag-dropdown{position:absolute;top:0;bottom:0;padding:0;background-color:#f5f5f5;border:1px solid #cacaca;border-radius:3px 0 0 3px}.react-tel-input .flag-dropdown:hover,.react-tel-input .flag-dropdown:focus{cursor:pointer}.react-tel-input .flag-dropdown.invalid-number{border-color:#d79f9f}.react-tel-input .flag-dropdown.open{z-index:2;background:#fff;border-radius:3px 0 0 0}.react-tel-input .flag-dropdown.open .selected-flag{background:#fff;border-radius:3px 0 0 0}.react-tel-input input[disabled]+.flag-dropdown:hover{cursor:default}.react-tel-input input[disabled]+.flag-dropdown:hover .selected-flag{background-color:transparent}.react-tel-input .selected-flag{outline:none;position:relative;width:38px;height:100%;padding:0 0 0 8px;border-radius:3px 0 0 3px}.react-tel-input .selected-flag:hover,.react-tel-input .selected-flag:focus{background-color:#fff}.react-tel-input .selected-flag .flag{position:absolute;top:50%;margin-top:-5px}.react-tel-input .selected-flag .arrow{position:relative;top:50%;margin-top:-2px;left:20px;width:0;height:0;border-left:3px solid transparent;border-right:3px solid transparent;border-top:4px solid #555}.react-tel-input .selected-flag .arrow.up{border-top:none;border-bottom:4px solid #555}.react-tel-input .country-list{outline:none;z-index:1;list-style:none;position:absolute;padding:0;margin:10px 0 10px -1px;box-shadow:1px 2px 10px rgba(0,0,0,0.35);background-color:white;width:300px;max-height:200px;overflow-y:scroll;border-radius:0 0 3px 3px}.react-tel-input .country-list .flag{display:inline-block}.react-tel-input .country-list .divider{padding-bottom:5px;margin-bottom:5px;border-bottom:1px solid #ccc}.react-tel-input .country-list .country{padding:7px 9px}.react-tel-input .country-list .country .dial-code{color:#6b6b6b}.react-tel-input .country-list .country:hover{background-color:#f1f1f1}.react-tel-input .country-list .country.highlight{background-color:#f1f1f1}.react-tel-input .country-list .flag{margin-right:7px;margin-top:2px}.react-tel-input .country-list .country-name{margin-right:6px}.react-tel-input .country-list .search{position:-webkit-sticky;position:sticky;top:0;background-color:#fff;padding:10px 0 6px 10px}.react-tel-input .country-list .search-emoji{font-size:15px}.react-tel-input .country-list .search-box{border:1px solid #cacaca;border-radius:3px;font-size:15px;line-height:15px;margin-left:6px;padding:3px 8px 5px;outline:none}.react-tel-input .country-list .no-entries-message{padding:7px 10px 11px;opacity:.7}.react-tel-input .invalid-number-message{position:absolute;z-index:1;font-size:13px;left:46px;top:-8px;background:#fff;padding:0 2px;color:#de0000}.react-tel-input .special-label{display:none;position:absolute;z-index:1;font-size:13px;left:46px;top:-8px;background:#fff;padding:0 2px;white-space:nowrap}
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/AIReadinessForm/AIReadinessForm.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
.AIReadinessForm_heading__zDVKs > h2 {
  font-weight: 600;
  font-size: 32px;
  line-height: 132%;
  padding: 20px 0;
  border-bottom: 2px solid black;
}

.AIReadinessForm_formWrapper__gZIkR {
  padding: 40px 10px 0;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (max-width: 1170px) {
    padding: 2.5rem 2rem;
  }

  @media (max-width: 450px) {
    padding: 1rem;
  }
}

.AIReadinessForm_form__uu90e {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (min-width: 1400px) {
    width: 1192px;
  }
}

.AIReadinessForm_formFields__WEfzC {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.AIReadinessForm_personalDetailsWrapper__MA09e {
  display: flex;
  gap: 24px;
  flex-direction: column;
}

.AIReadinessForm_row__0FoYJ {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  row-gap: 40px;
}

.AIReadinessForm_nameAndInputWrapper__Jkgg7 {
  width: 48%;
  display: flex;
  justify-content: space-between;

  @media screen and (max-width: 1170px) {
    width: 100%;
    padding: 0 5rem;
  }

  @media screen and (max-width: 1024px) {
    width: 100%;
    padding: 0;
  }
}

.AIReadinessForm_firstRow__4fvbq {
  flex-direction: row;

  @media (max-width: 1170px) {
    flex-direction: column !important;
    gap: 24px;
  }
}

.AIReadinessForm_formLabel__Sz516 {
  width: 220px;
  color: #000000;
  font-size: 26px;
  font-style: normal;
  font-weight: 500;
  line-height: 164%;

  @media screen and (max-width: 768px) {
    width: 170px;
    font-weight: 400;
    font-size: 18px;
    line-height: 168%;
  }
}

.AIReadinessForm_formInput__LiTq7 {
  width: 300px;
  background: #F3F3F3;
  color: #000000;
  border-radius: 3px;
  height: 41px;
  border: none;
  padding: 10px;

  @media screen and (max-width: 1170px) {
    width: 68%;
  }

  @media screen and (max-width: 450px) {
    width: 48%;
  }
}

.AIReadinessForm_phoneInputWrapper__uxK7a {
  width: 300px;

  @media screen and (max-width: 1170px) {
    width: 68%;
  }

  @media screen and (max-width: 450px) {
    width: 48%;
  }
}

.AIReadinessForm_formInputPhone__Eq6EL {
  background: #F3F3F3 !important;
  color: #000000 !important;
  height: 41px !important;
  width: 100% !important;
  border: none !important;
  padding: 10px;
  box-shadow: none !important;
}

.AIReadinessForm_formInputPhone_dial_icon__H1ZPl {
  background-color: #F3F3F3 !important;
  color: #000000 !important;
  height: 41px !important;
  padding: 10px;
  border: none !important;
  box-shadow: none !important;
}

.AIReadinessForm_formInputPhone_dial_icon__H1ZPl > div,
.AIReadinessForm_formInputPhone_dial_icon__H1ZPl > div:hover {
  background-color: #F3F3F3 !important;
  color: #000000 !important;
}

.AIReadinessForm_formInputPhone_dial_icon__H1ZPl > div > div > div {
  border-bottom: none !important;
  border-top: 5px solid #000000 !important;
}

.AIReadinessForm_ph_number_countries_dropdown__5VtDb {
  color: #000000 !important;
}

.AIReadinessForm_formInputForHowCanWeHelpYou__7etT_ {
  height: 82px;
  max-height: 250px;
}

.AIReadinessForm_formInput__LiTq7:focus-visible {
  outline: none;
  margin: 0;
}

.AIReadinessForm_consentRow__YAlpQ {
  display: flex;
  gap: 12px;
}

.AIReadinessForm_consentText__jcT47 {
  display: flex;
  gap: 10px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  cursor: pointer;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.AIReadinessForm_submitButtonRow__H9FgZ {
  align-items: center;
  justify-content: start;
  gap: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 24px;
  }
}

.AIReadinessForm_submitButton__lgj0Z {
  padding: 16px 36px !important;

  @media (max-width: 768px) {
    width: 100%;
  }
}

.AIReadinessForm_submitButton__lgj0Z > div {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
}

.AIReadinessForm_submitButton__lgj0Z::before {
  border-radius: 6px;
  padding: 2px;
}

.AIReadinessForm_linkedInButton__UlLiV {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-decoration: none;
}

.AIReadinessForm_errorInput__jFI3x {
  border: 1px solid #ff0000 !important;
}

.AIReadinessForm_errorMessages__lMe0y {
  font-weight: 500;
  font-size: 16px;
  line-height: 25.6px;
  color: #ff0000;
}

.AIReadinessForm_errorLabel__Dj5XK {
  color: #ff0000;
  font-size: 26px;

  @media screen and (max-width: 768px) {
    width: 170px;
    font-weight: 400;
    font-size: 18px;
    line-height: 168%;
  }
}

.AIReadinessForm_errorLabel_consentText__xUfN3 {
  color: #ff0000;
  font-size: 16px;
}

.AIReadinessForm_container_spinner__hGhLh {
  position: relative;
  width: 170px;
  height: 68px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  border: 2px solid transparent;
  cursor: pointer;
  background-image: linear-gradient(#FFFFFF, #FFFFFF),
    linear-gradient(
      93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%
    );
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}

.AIReadinessForm_spinner__YaMdM {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(
    93deg,
    #febe10 0%,
    #f47a37 30.56%,
    #f05443 53.47%,
    #d91a5f 75.75%,
    #b41f5e 100%
  );

  -webkit-mask-image: radial-gradient(
    circle,
    rgba(0, 0, 0, 0) 55%,
    rgba(0, 0, 0, 1) 60%
  );
  animation: AIReadinessForm_spin__WCWUD 0.8s linear infinite;
}

@keyframes AIReadinessForm_spin__WCWUD {
  to {
    transform: rotate(360deg);
  }
}

.AIReadinessForm_result_button__M_E4e {
  padding: 13px 35.5px;
  border: none !important;

  font-weight: 600;
  font-size: 20px;
  line-height: 148%;

  color: #FFFFFF;
  background: linear-gradient(
    90deg,
    #FEBE10 0%,
    #F47A37 30.56%,
    #F05443 53.47%,
    #D91A5F 75.75%,
    #B41F5E 100%
  );
}

.AIReadinessForm_consentText__jcT47 > input {
  border-radius: 2px;
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CloudMigrationResults/CloudMigrationResults.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************/
.CloudMigrationResults_results_wrapper__2OJ5b {
  max-width: 1192px;
  margin: 0 auto;
  padding: 40px 20px;
  text-align: center;

  @media screen and (max-width: 1192px) {
    margin: 0 36px;
  }

  @media screen and (max-width: 1024px) {
    padding: 20px 16px;
  }
}

/* Restart Button */
.CloudMigrationResults_restart_button_wrapper__ERGxd {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 40px;

  @media screen and (max-width: 1024px) {
    margin-bottom: 20px;
  }
}

.CloudMigrationResults_restart_button__FGauo {
  display: flex !important;
  gap: 10px !important;

  padding: 13px 35.5px !important;
  border-radius: 3px !important;
  border-width: 2px !important;

  font-weight: 600 !important;
  font-size: 16px !important;

  color: #000000 !important;
  background-image: linear-gradient(#FFFFFF, #FFFFFF),
    linear-gradient(93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%) !important;
}

/* Main Heading */
.CloudMigrationResults_results_header__1vFJ7 {
  margin-bottom: 40px;

  @media screen and (max-width: 1024px) {
    margin-bottom: 30px;
  }
}

.CloudMigrationResults_main_heading__71q_N {
  font-size: 48px;
  font-weight: 700;
  color: #000000;
  line-height: 1.2;
  margin: 0;

  @media screen and (max-width: 1024px) {
    font-size: 36px;
  }

  @media screen and (max-width: 576px) {
    font-size: 28px;
  }
}

/* Cost Display */
.CloudMigrationResults_cost_display__8nqav {
  margin-bottom: 40px;

  @media screen and (max-width: 1024px) {
    margin-bottom: 30px;
  }
}

.CloudMigrationResults_cost_range__xXt9p {
  font-size: 64px;
  font-weight: 700;
  color: #FF6B6B;
  line-height: 1.1;
  margin-bottom: 20px;

  @media screen and (max-width: 1024px) {
    font-size: 48px;
  }

  @media screen and (max-width: 576px) {
    font-size: 36px;
  }
}

/* Disclaimer */
.CloudMigrationResults_disclaimer__I_zIB {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 60px;
  text-align: left;

  @media screen and (max-width: 1024px) {
    padding: 20px;
    margin-bottom: 40px;
  }
}

.CloudMigrationResults_disclaimer__I_zIB p {
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
  color: #333;

  @media screen and (max-width: 1024px) {
    font-size: 14px;
  }
}

/* Savings Section */
.CloudMigrationResults_savings_section__aF53V {
  margin-bottom: 60px;

  @media screen and (max-width: 1024px) {
    margin-bottom: 40px;
  }
}

.CloudMigrationResults_savings_heading__uY5Sd {
  font-size: 40px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 24px;
  line-height: 1.2;

  @media screen and (max-width: 1024px) {
    font-size: 32px;
    margin-bottom: 20px;
  }

  @media screen and (max-width: 576px) {
    font-size: 28px;
  }
}

.CloudMigrationResults_savings_description__C5PTN {
  font-size: 18px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;

  @media screen and (max-width: 1024px) {
    font-size: 16px;
    margin-bottom: 30px;
  }
}

/* Benefits Grid */
.CloudMigrationResults_benefits_grid__zBdVQ {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-gap: 30px;
  gap: 30px;
  margin-bottom: 50px;

  @media screen and (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 40px;
  }
}

.CloudMigrationResults_benefit_card__CD8K0 {
  background-color: #FFFFFF;
  padding: 32px 24px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  text-align: left;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  @media screen and (max-width: 1024px) {
    padding: 24px 20px;
  }
}

.CloudMigrationResults_benefit_card__CD8K0:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.CloudMigrationResults_benefit_title__tyMAr {
  font-size: 20px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 16px;
  line-height: 1.3;

  @media screen and (max-width: 1024px) {
    font-size: 18px;
    margin-bottom: 12px;
  }
}

.CloudMigrationResults_benefit_description__O1eoR {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin: 0;

  @media screen and (max-width: 1024px) {
    font-size: 14px;
  }
}

/* CTA Button */
.CloudMigrationResults_cta_button_wrapper__JCK_7 {
  display: flex;
  justify-content: center;
}

.CloudMigrationResults_cta_button__1i69Z {
  display: flex !important;
  gap: 10px !important;

  padding: 13px 35.5px !important;
  border-radius: 3px !important;
  border-width: 2px !important;

  font-weight: 600 !important;
  font-size: 16px !important;

  color: #000000 !important;
  background-image: linear-gradient(#FFFFFF, #FFFFFF),
    linear-gradient(93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%) !important;
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CloudMigrationBody/CloudMigrationBody.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
.CloudMigrationBody_container__1XSk8 {
  padding: 36px 0 80px 0;

  @media screen and (max-width: 1024px) {
    padding: 36px 0 36px 0;
  }
}

.CloudMigrationBody_step_container__I68x2 {
  height: 45px;
  padding: 16px;
  display: flex;
  gap: 10px;

  font-weight: 500;
  font-size: 18px;
  line-height: 160%;
  letter-spacing: 0.36;
}

.CloudMigrationBody_hidden__Tr03l {
  display: none;
}

.CloudMigrationBody_section_wrapper__Ur4lt {
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin: 0 auto;
  padding: 0 10px;

  @media screen and (min-width: 1200px) {
    width: 1192px;
  }
}

.CloudMigrationBody_heading__Wkgto>h2 {
  font-weight: 600;
  font-size: 32px;
  line-height: 132%;
  padding: 20px;
  border-bottom: 2px solid black;
}

.CloudMigrationBody_button_wrapper__t4Guz {
  display: flex;
  justify-content: flex-end;
  gap: 40px;
  align-items: center;
  margin-top: 40px;
}

.CloudMigrationBody_button_wrapper__t4Guz>button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.CloudMigrationBody_button_wrapper__t4Guz>button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.CloudMigrationBody_button_wrapper_mobile__0rVi2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 500;
  font-size: 16px;
}

.CloudMigrationBody_button_wrapper_mobile__0rVi2>button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.CloudMigrationBody_button_wrapper_mobile__0rVi2>button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.CloudMigrationBody_error_message__0B5r4 {
  color: #ff5656;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
  padding: 10px;
  background-color: #fff5f5;
  border: 1px solid #ff5656;
  border-radius: 4px;
}

.CloudMigrationBody_results_wrapper__CIlvG {
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin: 0 auto;
  padding: 40px 10px;
  text-align: center;

  @media screen and (min-width: 1200px) {
    width: 1192px;
  }
}

.CloudMigrationBody_cost_display__bqWSP {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 16px;
  margin: 20px 0;
}

.CloudMigrationBody_cost_range__74WFe {
  font-size: 48px;
  font-weight: 700;
  margin: 20px 0;
}

.CloudMigrationBody_cost_description__PnWei {
  font-size: 18px;
  line-height: 1.6;
  margin: 20px 0;
}

.CloudMigrationBody_consultation_button__o4t8M {
  margin: 20px auto;
  padding: 16px 32px;
  background-color: #FEBE10;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.CloudMigrationBody_consultation_button__o4t8M:hover {
  background-color: #F47A37;
}

.CloudMigrationBody_restart_button__69z5k {
  margin: 20px auto;
  padding: 12px 24px;
  background-color: transparent;
  color: #FEBE10;
  border: 2px solid #FEBE10;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.CloudMigrationBody_restart_button__69z5k:hover {
  background-color: #FEBE10;
  color: white;
}

@media screen and (max-width: 1024px) {
  .CloudMigrationBody_cost_range__74WFe {
    font-size: 36px;
  }
  
  .CloudMigrationBody_cost_description__PnWei {
    font-size: 16px;
  }
  
  .CloudMigrationBody_heading__Wkgto>h2 {
    font-size: 24px;
    padding: 16px;
  }
}

