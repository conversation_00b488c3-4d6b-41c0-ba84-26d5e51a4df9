"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-quadtree";
exports.ids = ["vendor-chunks/d3-quadtree"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-quadtree/src/add.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-quadtree/src/add.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addAll: () => (/* binding */ addAll),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(d) {\n    const x = +this._x.call(null, d), y = +this._y.call(null, d);\n    return add(this.cover(x, y), x, y, d);\n}\nfunction add(tree, x, y, d) {\n    if (isNaN(x) || isNaN(y)) return tree; // ignore invalid points\n    var parent, node = tree._root, leaf = {\n        data: d\n    }, x0 = tree._x0, y0 = tree._y0, x1 = tree._x1, y1 = tree._y1, xm, ym, xp, yp, right, bottom, i, j;\n    // If the tree is empty, initialize the root as a leaf.\n    if (!node) return tree._root = leaf, tree;\n    // Find the existing leaf for the new point, or add it.\n    while(node.length){\n        if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;\n        else x1 = xm;\n        if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;\n        else y1 = ym;\n        if (parent = node, !(node = node[i = bottom << 1 | right])) return parent[i] = leaf, tree;\n    }\n    // Is the new point is exactly coincident with the existing point?\n    xp = +tree._x.call(null, node.data);\n    yp = +tree._y.call(null, node.data);\n    if (x === xp && y === yp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n    // Otherwise, split the leaf node until the old and new point are separated.\n    do {\n        parent = parent ? parent[i] = new Array(4) : tree._root = new Array(4);\n        if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;\n        else x1 = xm;\n        if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;\n        else y1 = ym;\n    }while ((i = bottom << 1 | right) === (j = (yp >= ym) << 1 | xp >= xm));\n    return parent[j] = node, parent[i] = leaf, tree;\n}\nfunction addAll(data) {\n    var d, i, n = data.length, x, y, xz = new Array(n), yz = new Array(n), x0 = Infinity, y0 = Infinity, x1 = -Infinity, y1 = -Infinity;\n    // Compute the points and their extent.\n    for(i = 0; i < n; ++i){\n        if (isNaN(x = +this._x.call(null, d = data[i])) || isNaN(y = +this._y.call(null, d))) continue;\n        xz[i] = x;\n        yz[i] = y;\n        if (x < x0) x0 = x;\n        if (x > x1) x1 = x;\n        if (y < y0) y0 = y;\n        if (y > y1) y1 = y;\n    }\n    // If there were no (valid) points, abort.\n    if (x0 > x1 || y0 > y1) return this;\n    // Expand the tree to cover the new points.\n    this.cover(x0, y0).cover(x1, y1);\n    // Add the new points.\n    for(i = 0; i < n; ++i){\n        add(this, xz[i], yz[i], data[i]);\n    }\n    return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/add.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/cover.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-quadtree/src/cover.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, y) {\n    if (isNaN(x = +x) || isNaN(y = +y)) return this; // ignore invalid points\n    var x0 = this._x0, y0 = this._y0, x1 = this._x1, y1 = this._y1;\n    // If the quadtree has no extent, initialize them.\n    // Integer extent are necessary so that if we later double the extent,\n    // the existing quadrant boundaries don’t change due to floating point error!\n    if (isNaN(x0)) {\n        x1 = (x0 = Math.floor(x)) + 1;\n        y1 = (y0 = Math.floor(y)) + 1;\n    } else {\n        var z = x1 - x0 || 1, node = this._root, parent, i;\n        while(x0 > x || x >= x1 || y0 > y || y >= y1){\n            i = (y < y0) << 1 | x < x0;\n            parent = new Array(4), parent[i] = node, node = parent, z *= 2;\n            switch(i){\n                case 0:\n                    x1 = x0 + z, y1 = y0 + z;\n                    break;\n                case 1:\n                    x0 = x1 - z, y1 = y0 + z;\n                    break;\n                case 2:\n                    x1 = x0 + z, y0 = y1 - z;\n                    break;\n                case 3:\n                    x0 = x1 - z, y0 = y1 - z;\n                    break;\n            }\n        }\n        if (this._root && this._root.length) this._root = node;\n    }\n    this._x0 = x0;\n    this._y0 = y0;\n    this._x1 = x1;\n    this._y1 = y1;\n    return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/cover.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/data.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-quadtree/src/data.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var data = [];\n    this.visit(function(node) {\n        if (!node.length) do data.push(node.data);\n        while (node = node.next);\n    });\n    return data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL2RhdGEuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLHNDQUFXO0lBQ3hCLElBQUlBLE9BQU8sRUFBRTtJQUNiLElBQUksQ0FBQ0MsS0FBSyxDQUFDLFNBQVNDLElBQUk7UUFDdEIsSUFBSSxDQUFDQSxLQUFLQyxNQUFNLEVBQUUsR0FBR0gsS0FBS0ksSUFBSSxDQUFDRixLQUFLRixJQUFJO2VBQVVFLE9BQU9BLEtBQUtHLElBQUksRUFBQztJQUNyRTtJQUNBLE9BQU9MO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1xdWFkdHJlZS9zcmMvZGF0YS5qcz9jYWQwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICB2YXIgZGF0YSA9IFtdO1xuICB0aGlzLnZpc2l0KGZ1bmN0aW9uKG5vZGUpIHtcbiAgICBpZiAoIW5vZGUubGVuZ3RoKSBkbyBkYXRhLnB1c2gobm9kZS5kYXRhKTsgd2hpbGUgKG5vZGUgPSBub2RlLm5leHQpXG4gIH0pO1xuICByZXR1cm4gZGF0YTtcbn1cbiJdLCJuYW1lcyI6WyJkYXRhIiwidmlzaXQiLCJub2RlIiwibGVuZ3RoIiwicHVzaCIsIm5leHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/data.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/extent.js":
/*!************************************************!*\
  !*** ./node_modules/d3-quadtree/src/extent.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(_) {\n    return arguments.length ? this.cover(+_[0][0], +_[0][1]).cover(+_[1][0], +_[1][1]) : isNaN(this._x0) ? undefined : [\n        [\n            this._x0,\n            this._y0\n        ],\n        [\n            this._x1,\n            this._y1\n        ]\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL2V4dGVudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLENBQUM7SUFDdkIsT0FBT0MsVUFBVUMsTUFBTSxHQUNqQixJQUFJLENBQUNDLEtBQUssQ0FBQyxDQUFDSCxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDQSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRUcsS0FBSyxDQUFDLENBQUNILENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUNBLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxJQUN2REksTUFBTSxJQUFJLENBQUNDLEdBQUcsSUFBSUMsWUFBWTtRQUFDO1lBQUMsSUFBSSxDQUFDRCxHQUFHO1lBQUUsSUFBSSxDQUFDRSxHQUFHO1NBQUM7UUFBRTtZQUFDLElBQUksQ0FBQ0MsR0FBRztZQUFFLElBQUksQ0FBQ0MsR0FBRztTQUFDO0tBQUM7QUFDbEYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1xdWFkdHJlZS9zcmMvZXh0ZW50LmpzP2MzNzIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oXykge1xuICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aFxuICAgICAgPyB0aGlzLmNvdmVyKCtfWzBdWzBdLCArX1swXVsxXSkuY292ZXIoK19bMV1bMF0sICtfWzFdWzFdKVxuICAgICAgOiBpc05hTih0aGlzLl94MCkgPyB1bmRlZmluZWQgOiBbW3RoaXMuX3gwLCB0aGlzLl95MF0sIFt0aGlzLl94MSwgdGhpcy5feTFdXTtcbn1cbiJdLCJuYW1lcyI6WyJfIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiY292ZXIiLCJpc05hTiIsIl94MCIsInVuZGVmaW5lZCIsIl95MCIsIl94MSIsIl95MSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/extent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/find.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-quadtree/src/find.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _quad_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quad.js */ \"(ssr)/./node_modules/d3-quadtree/src/quad.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, y, radius) {\n    var data, x0 = this._x0, y0 = this._y0, x1, y1, x2, y2, x3 = this._x1, y3 = this._y1, quads = [], node = this._root, q, i;\n    if (node) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node, x0, y0, x3, y3));\n    if (radius == null) radius = Infinity;\n    else {\n        x0 = x - radius, y0 = y - radius;\n        x3 = x + radius, y3 = y + radius;\n        radius *= radius;\n    }\n    while(q = quads.pop()){\n        // Stop searching if this quadrant can’t contain a closer node.\n        if (!(node = q.node) || (x1 = q.x0) > x3 || (y1 = q.y0) > y3 || (x2 = q.x1) < x0 || (y2 = q.y1) < y0) continue;\n        // Bisect the current quadrant.\n        if (node.length) {\n            var xm = (x1 + x2) / 2, ym = (y1 + y2) / 2;\n            quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node[3], xm, ym, x2, y2), new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node[2], x1, ym, xm, y2), new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node[1], xm, y1, x2, ym), new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node[0], x1, y1, xm, ym));\n            // Visit the closest quadrant first.\n            if (i = (y >= ym) << 1 | x >= xm) {\n                q = quads[quads.length - 1];\n                quads[quads.length - 1] = quads[quads.length - 1 - i];\n                quads[quads.length - 1 - i] = q;\n            }\n        } else {\n            var dx = x - +this._x.call(null, node.data), dy = y - +this._y.call(null, node.data), d2 = dx * dx + dy * dy;\n            if (d2 < radius) {\n                var d = Math.sqrt(radius = d2);\n                x0 = x - d, y0 = y - d;\n                x3 = x + d, y3 = y + d;\n                data = node.data;\n            }\n        }\n    }\n    return data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/find.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/index.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-quadtree/src/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   quadtree: () => (/* reexport safe */ _quadtree_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _quadtree_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quadtree.js */ \"(ssr)/./node_modules/d3-quadtree/src/quadtree.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL2luZGV4LmpzP2M5MjIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHtkZWZhdWx0IGFzIHF1YWR0cmVlfSBmcm9tIFwiLi9xdWFkdHJlZS5qc1wiO1xuIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJxdWFkdHJlZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/quad.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-quadtree/src/quad.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(node, x0, y0, x1, y1) {\n    this.node = node;\n    this.x0 = x0;\n    this.y0 = y0;\n    this.x1 = x1;\n    this.y1 = y1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL3F1YWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxJQUFJLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLEVBQUU7SUFDMUMsSUFBSSxDQUFDSixJQUFJLEdBQUdBO0lBQ1osSUFBSSxDQUFDQyxFQUFFLEdBQUdBO0lBQ1YsSUFBSSxDQUFDQyxFQUFFLEdBQUdBO0lBQ1YsSUFBSSxDQUFDQyxFQUFFLEdBQUdBO0lBQ1YsSUFBSSxDQUFDQyxFQUFFLEdBQUdBO0FBQ1oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1xdWFkdHJlZS9zcmMvcXVhZC5qcz9mNzEyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKG5vZGUsIHgwLCB5MCwgeDEsIHkxKSB7XG4gIHRoaXMubm9kZSA9IG5vZGU7XG4gIHRoaXMueDAgPSB4MDtcbiAgdGhpcy55MCA9IHkwO1xuICB0aGlzLngxID0geDE7XG4gIHRoaXMueTEgPSB5MTtcbn1cbiJdLCJuYW1lcyI6WyJub2RlIiwieDAiLCJ5MCIsIngxIiwieTEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/quad.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/quadtree.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-quadtree/src/quadtree.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quadtree)\n/* harmony export */ });\n/* harmony import */ var _add_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./add.js */ \"(ssr)/./node_modules/d3-quadtree/src/add.js\");\n/* harmony import */ var _cover_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cover.js */ \"(ssr)/./node_modules/d3-quadtree/src/cover.js\");\n/* harmony import */ var _data_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./data.js */ \"(ssr)/./node_modules/d3-quadtree/src/data.js\");\n/* harmony import */ var _extent_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./extent.js */ \"(ssr)/./node_modules/d3-quadtree/src/extent.js\");\n/* harmony import */ var _find_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./find.js */ \"(ssr)/./node_modules/d3-quadtree/src/find.js\");\n/* harmony import */ var _remove_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./remove.js */ \"(ssr)/./node_modules/d3-quadtree/src/remove.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/d3-quadtree/src/root.js\");\n/* harmony import */ var _size_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./size.js */ \"(ssr)/./node_modules/d3-quadtree/src/size.js\");\n/* harmony import */ var _visit_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./visit.js */ \"(ssr)/./node_modules/d3-quadtree/src/visit.js\");\n/* harmony import */ var _visitAfter_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./visitAfter.js */ \"(ssr)/./node_modules/d3-quadtree/src/visitAfter.js\");\n/* harmony import */ var _x_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./x.js */ \"(ssr)/./node_modules/d3-quadtree/src/x.js\");\n/* harmony import */ var _y_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./y.js */ \"(ssr)/./node_modules/d3-quadtree/src/y.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction quadtree(nodes, x, y) {\n    var tree = new Quadtree(x == null ? _x_js__WEBPACK_IMPORTED_MODULE_0__.defaultX : x, y == null ? _y_js__WEBPACK_IMPORTED_MODULE_1__.defaultY : y, NaN, NaN, NaN, NaN);\n    return nodes == null ? tree : tree.addAll(nodes);\n}\nfunction Quadtree(x, y, x0, y0, x1, y1) {\n    this._x = x;\n    this._y = y;\n    this._x0 = x0;\n    this._y0 = y0;\n    this._x1 = x1;\n    this._y1 = y1;\n    this._root = undefined;\n}\nfunction leaf_copy(leaf) {\n    var copy = {\n        data: leaf.data\n    }, next = copy;\n    while(leaf = leaf.next)next = next.next = {\n        data: leaf.data\n    };\n    return copy;\n}\nvar treeProto = quadtree.prototype = Quadtree.prototype;\ntreeProto.copy = function() {\n    var copy = new Quadtree(this._x, this._y, this._x0, this._y0, this._x1, this._y1), node = this._root, nodes, child;\n    if (!node) return copy;\n    if (!node.length) return copy._root = leaf_copy(node), copy;\n    nodes = [\n        {\n            source: node,\n            target: copy._root = new Array(4)\n        }\n    ];\n    while(node = nodes.pop()){\n        for(var i = 0; i < 4; ++i){\n            if (child = node.source[i]) {\n                if (child.length) nodes.push({\n                    source: child,\n                    target: node.target[i] = new Array(4)\n                });\n                else node.target[i] = leaf_copy(child);\n            }\n        }\n    }\n    return copy;\n};\ntreeProto.add = _add_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\ntreeProto.addAll = _add_js__WEBPACK_IMPORTED_MODULE_2__.addAll;\ntreeProto.cover = _cover_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\ntreeProto.data = _data_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\ntreeProto.extent = _extent_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\ntreeProto.find = _find_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\ntreeProto.remove = _remove_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\ntreeProto.removeAll = _remove_js__WEBPACK_IMPORTED_MODULE_7__.removeAll;\ntreeProto.root = _root_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\ntreeProto.size = _size_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\ntreeProto.visit = _visit_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\ntreeProto.visitAfter = _visitAfter_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\ntreeProto.x = _x_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\ntreeProto.y = _y_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/quadtree.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/remove.js":
/*!************************************************!*\
  !*** ./node_modules/d3-quadtree/src/remove.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   removeAll: () => (/* binding */ removeAll)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(d) {\n    if (isNaN(x = +this._x.call(null, d)) || isNaN(y = +this._y.call(null, d))) return this; // ignore invalid points\n    var parent, node = this._root, retainer, previous, next, x0 = this._x0, y0 = this._y0, x1 = this._x1, y1 = this._y1, x, y, xm, ym, right, bottom, i, j;\n    // If the tree is empty, initialize the root as a leaf.\n    if (!node) return this;\n    // Find the leaf node for the point.\n    // While descending, also retain the deepest parent with a non-removed sibling.\n    if (node.length) while(true){\n        if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;\n        else x1 = xm;\n        if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;\n        else y1 = ym;\n        if (!(parent = node, node = node[i = bottom << 1 | right])) return this;\n        if (!node.length) break;\n        if (parent[i + 1 & 3] || parent[i + 2 & 3] || parent[i + 3 & 3]) retainer = parent, j = i;\n    }\n    // Find the point to remove.\n    while(node.data !== d)if (!(previous = node, node = node.next)) return this;\n    if (next = node.next) delete node.next;\n    // If there are multiple coincident points, remove just the point.\n    if (previous) return next ? previous.next = next : delete previous.next, this;\n    // If this is the root point, remove it.\n    if (!parent) return this._root = next, this;\n    // Remove this leaf.\n    next ? parent[i] = next : delete parent[i];\n    // If the parent now contains exactly one leaf, collapse superfluous parents.\n    if ((node = parent[0] || parent[1] || parent[2] || parent[3]) && node === (parent[3] || parent[2] || parent[1] || parent[0]) && !node.length) {\n        if (retainer) retainer[j] = node;\n        else this._root = node;\n    }\n    return this;\n}\nfunction removeAll(data) {\n    for(var i = 0, n = data.length; i < n; ++i)this.remove(data[i]);\n    return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/remove.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/root.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-quadtree/src/root.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return this._root;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL3Jvb3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLHNDQUFXO0lBQ3hCLE9BQU8sSUFBSSxDQUFDQSxLQUFLO0FBQ25CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL3Jvb3QuanM/N2I4ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIHRoaXMuX3Jvb3Q7XG59XG4iXSwibmFtZXMiOlsiX3Jvb3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/size.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-quadtree/src/size.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var size = 0;\n    this.visit(function(node) {\n        if (!node.length) do ++size;\n        while (node = node.next);\n    });\n    return size;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL3NpemUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLHNDQUFXO0lBQ3hCLElBQUlBLE9BQU87SUFDWCxJQUFJLENBQUNDLEtBQUssQ0FBQyxTQUFTQyxJQUFJO1FBQ3RCLElBQUksQ0FBQ0EsS0FBS0MsTUFBTSxFQUFFLEdBQUcsRUFBRUg7ZUFBYUUsT0FBT0EsS0FBS0UsSUFBSSxFQUFDO0lBQ3ZEO0lBQ0EsT0FBT0o7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy9zaXplLmpzPzhhN2QiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHZhciBzaXplID0gMDtcbiAgdGhpcy52aXNpdChmdW5jdGlvbihub2RlKSB7XG4gICAgaWYgKCFub2RlLmxlbmd0aCkgZG8gKytzaXplOyB3aGlsZSAobm9kZSA9IG5vZGUubmV4dClcbiAgfSk7XG4gIHJldHVybiBzaXplO1xufVxuIl0sIm5hbWVzIjpbInNpemUiLCJ2aXNpdCIsIm5vZGUiLCJsZW5ndGgiLCJuZXh0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/size.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/visit.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-quadtree/src/visit.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _quad_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quad.js */ \"(ssr)/./node_modules/d3-quadtree/src/quad.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(callback) {\n    var quads = [], q, node = this._root, child, x0, y0, x1, y1;\n    if (node) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node, this._x0, this._y0, this._x1, this._y1));\n    while(q = quads.pop()){\n        if (!callback(node = q.node, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1) && node.length) {\n            var xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n            if (child = node[3]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, xm, ym, x1, y1));\n            if (child = node[2]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, x0, ym, xm, y1));\n            if (child = node[1]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, xm, y0, x1, ym));\n            if (child = node[0]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, x0, y0, xm, ym));\n        }\n    }\n    return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL3Zpc2l0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTZCO0FBRTdCLDZCQUFlLG9DQUFTQyxRQUFRO0lBQzlCLElBQUlDLFFBQVEsRUFBRSxFQUFFQyxHQUFHQyxPQUFPLElBQUksQ0FBQ0MsS0FBSyxFQUFFQyxPQUFPQyxJQUFJQyxJQUFJQyxJQUFJQztJQUN6RCxJQUFJTixNQUFNRixNQUFNUyxJQUFJLENBQUMsSUFBSVgsZ0RBQUlBLENBQUNJLE1BQU0sSUFBSSxDQUFDUSxHQUFHLEVBQUUsSUFBSSxDQUFDQyxHQUFHLEVBQUUsSUFBSSxDQUFDQyxHQUFHLEVBQUUsSUFBSSxDQUFDQyxHQUFHO0lBQzFFLE1BQU9aLElBQUlELE1BQU1jLEdBQUcsR0FBSTtRQUN0QixJQUFJLENBQUNmLFNBQVNHLE9BQU9ELEVBQUVDLElBQUksRUFBRUcsS0FBS0osRUFBRUksRUFBRSxFQUFFQyxLQUFLTCxFQUFFSyxFQUFFLEVBQUVDLEtBQUtOLEVBQUVNLEVBQUUsRUFBRUMsS0FBS1AsRUFBRU8sRUFBRSxLQUFLTixLQUFLYSxNQUFNLEVBQUU7WUFDdkYsSUFBSUMsS0FBSyxDQUFDWCxLQUFLRSxFQUFDLElBQUssR0FBR1UsS0FBSyxDQUFDWCxLQUFLRSxFQUFDLElBQUs7WUFDekMsSUFBSUosUUFBUUYsSUFBSSxDQUFDLEVBQUUsRUFBRUYsTUFBTVMsSUFBSSxDQUFDLElBQUlYLGdEQUFJQSxDQUFDTSxPQUFPWSxJQUFJQyxJQUFJVixJQUFJQztZQUM1RCxJQUFJSixRQUFRRixJQUFJLENBQUMsRUFBRSxFQUFFRixNQUFNUyxJQUFJLENBQUMsSUFBSVgsZ0RBQUlBLENBQUNNLE9BQU9DLElBQUlZLElBQUlELElBQUlSO1lBQzVELElBQUlKLFFBQVFGLElBQUksQ0FBQyxFQUFFLEVBQUVGLE1BQU1TLElBQUksQ0FBQyxJQUFJWCxnREFBSUEsQ0FBQ00sT0FBT1ksSUFBSVYsSUFBSUMsSUFBSVU7WUFDNUQsSUFBSWIsUUFBUUYsSUFBSSxDQUFDLEVBQUUsRUFBRUYsTUFBTVMsSUFBSSxDQUFDLElBQUlYLGdEQUFJQSxDQUFDTSxPQUFPQyxJQUFJQyxJQUFJVSxJQUFJQztRQUM5RDtJQUNGO0lBQ0EsT0FBTyxJQUFJO0FBQ2IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1xdWFkdHJlZS9zcmMvdmlzaXQuanM/ZDJkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUXVhZCBmcm9tIFwiLi9xdWFkLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKGNhbGxiYWNrKSB7XG4gIHZhciBxdWFkcyA9IFtdLCBxLCBub2RlID0gdGhpcy5fcm9vdCwgY2hpbGQsIHgwLCB5MCwgeDEsIHkxO1xuICBpZiAobm9kZSkgcXVhZHMucHVzaChuZXcgUXVhZChub2RlLCB0aGlzLl94MCwgdGhpcy5feTAsIHRoaXMuX3gxLCB0aGlzLl95MSkpO1xuICB3aGlsZSAocSA9IHF1YWRzLnBvcCgpKSB7XG4gICAgaWYgKCFjYWxsYmFjayhub2RlID0gcS5ub2RlLCB4MCA9IHEueDAsIHkwID0gcS55MCwgeDEgPSBxLngxLCB5MSA9IHEueTEpICYmIG5vZGUubGVuZ3RoKSB7XG4gICAgICB2YXIgeG0gPSAoeDAgKyB4MSkgLyAyLCB5bSA9ICh5MCArIHkxKSAvIDI7XG4gICAgICBpZiAoY2hpbGQgPSBub2RlWzNdKSBxdWFkcy5wdXNoKG5ldyBRdWFkKGNoaWxkLCB4bSwgeW0sIHgxLCB5MSkpO1xuICAgICAgaWYgKGNoaWxkID0gbm9kZVsyXSkgcXVhZHMucHVzaChuZXcgUXVhZChjaGlsZCwgeDAsIHltLCB4bSwgeTEpKTtcbiAgICAgIGlmIChjaGlsZCA9IG5vZGVbMV0pIHF1YWRzLnB1c2gobmV3IFF1YWQoY2hpbGQsIHhtLCB5MCwgeDEsIHltKSk7XG4gICAgICBpZiAoY2hpbGQgPSBub2RlWzBdKSBxdWFkcy5wdXNoKG5ldyBRdWFkKGNoaWxkLCB4MCwgeTAsIHhtLCB5bSkpO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdGhpcztcbn1cbiJdLCJuYW1lcyI6WyJRdWFkIiwiY2FsbGJhY2siLCJxdWFkcyIsInEiLCJub2RlIiwiX3Jvb3QiLCJjaGlsZCIsIngwIiwieTAiLCJ4MSIsInkxIiwicHVzaCIsIl94MCIsIl95MCIsIl94MSIsIl95MSIsInBvcCIsImxlbmd0aCIsInhtIiwieW0iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/visit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/visitAfter.js":
/*!****************************************************!*\
  !*** ./node_modules/d3-quadtree/src/visitAfter.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _quad_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quad.js */ \"(ssr)/./node_modules/d3-quadtree/src/quad.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(callback) {\n    var quads = [], next = [], q;\n    if (this._root) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](this._root, this._x0, this._y0, this._x1, this._y1));\n    while(q = quads.pop()){\n        var node = q.node;\n        if (node.length) {\n            var child, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1, xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n            if (child = node[0]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, x0, y0, xm, ym));\n            if (child = node[1]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, xm, y0, x1, ym));\n            if (child = node[2]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, x0, ym, xm, y1));\n            if (child = node[3]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, xm, ym, x1, y1));\n        }\n        next.push(q);\n    }\n    while(q = next.pop()){\n        callback(q.node, q.x0, q.y0, q.x1, q.y1);\n    }\n    return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/visitAfter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/x.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-quadtree/src/x.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultX: () => (/* binding */ defaultX)\n/* harmony export */ });\nfunction defaultX(d) {\n    return d[0];\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(_) {\n    return arguments.length ? (this._x = _, this) : this._x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL3guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTyxTQUFTQSxTQUFTQyxDQUFDO0lBQ3hCLE9BQU9BLENBQUMsQ0FBQyxFQUFFO0FBQ2I7QUFFQSw2QkFBZSxvQ0FBU0MsQ0FBQztJQUN2QixPQUFPQyxVQUFVQyxNQUFNLEdBQUksS0FBSSxDQUFDQyxFQUFFLEdBQUdILEdBQUcsSUFBSSxJQUFJLElBQUksQ0FBQ0csRUFBRTtBQUN6RCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy94LmpzPzAxZjIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGRlZmF1bHRYKGQpIHtcbiAgcmV0dXJuIGRbMF07XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKF8pIHtcbiAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyAodGhpcy5feCA9IF8sIHRoaXMpIDogdGhpcy5feDtcbn1cbiJdLCJuYW1lcyI6WyJkZWZhdWx0WCIsImQiLCJfIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiX3giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/x.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-quadtree/src/y.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-quadtree/src/y.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultY: () => (/* binding */ defaultY)\n/* harmony export */ });\nfunction defaultY(d) {\n    return d[1];\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(_) {\n    return arguments.length ? (this._y = _, this) : this._y;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL3kuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTyxTQUFTQSxTQUFTQyxDQUFDO0lBQ3hCLE9BQU9BLENBQUMsQ0FBQyxFQUFFO0FBQ2I7QUFFQSw2QkFBZSxvQ0FBU0MsQ0FBQztJQUN2QixPQUFPQyxVQUFVQyxNQUFNLEdBQUksS0FBSSxDQUFDQyxFQUFFLEdBQUdILEdBQUcsSUFBSSxJQUFJLElBQUksQ0FBQ0csRUFBRTtBQUN6RCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy95LmpzP2MyMDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGRlZmF1bHRZKGQpIHtcbiAgcmV0dXJuIGRbMV07XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKF8pIHtcbiAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyAodGhpcy5feSA9IF8sIHRoaXMpIDogdGhpcy5feTtcbn1cbiJdLCJuYW1lcyI6WyJkZWZhdWx0WSIsImQiLCJfIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiX3kiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-quadtree/src/y.js\n");

/***/ })

};
;