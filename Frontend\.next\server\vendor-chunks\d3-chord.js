"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-chord";
exports.ids = ["vendor-chunks/d3-chord"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-chord/src/array.js":
/*!********************************************!*\
  !*** ./node_modules/d3-chord/src/array.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   slice: () => (/* binding */ slice)\n/* harmony export */ });\nvar slice = Array.prototype.slice;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY2hvcmQvc3JjL2FycmF5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxJQUFJQSxRQUFRQyxNQUFNQyxTQUFTLENBQUNGLEtBQUssQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWNob3JkL3NyYy9hcnJheS5qcz9hY2I4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgc2xpY2UgPSBBcnJheS5wcm90b3R5cGUuc2xpY2U7XG4iXSwibmFtZXMiOlsic2xpY2UiLCJBcnJheSIsInByb3RvdHlwZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-chord/src/array.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-chord/src/chord.js":
/*!********************************************!*\
  !*** ./node_modules/d3-chord/src/chord.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chordDirected: () => (/* binding */ chordDirected),\n/* harmony export */   chordTranspose: () => (/* binding */ chordTranspose),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-chord/src/math.js\");\n\nfunction range(i, j) {\n    return Array.from({\n        length: j - i\n    }, (_, k)=>i + k);\n}\nfunction compareValue(compare) {\n    return function(a, b) {\n        return compare(a.source.value + a.target.value, b.source.value + b.target.value);\n    };\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return chord(false, false);\n}\nfunction chordTranspose() {\n    return chord(false, true);\n}\nfunction chordDirected() {\n    return chord(true, false);\n}\nfunction chord(directed, transpose) {\n    var padAngle = 0, sortGroups = null, sortSubgroups = null, sortChords = null;\n    function chord(matrix) {\n        var n = matrix.length, groupSums = new Array(n), groupIndex = range(0, n), chords = new Array(n * n), groups = new Array(n), k = 0, dx;\n        matrix = Float64Array.from({\n            length: n * n\n        }, transpose ? (_, i)=>matrix[i % n][i / n | 0] : (_, i)=>matrix[i / n | 0][i % n]);\n        // Compute the scaling factor from value to angle in [0, 2pi].\n        for(let i = 0; i < n; ++i){\n            let x = 0;\n            for(let j = 0; j < n; ++j)x += matrix[i * n + j] + directed * matrix[j * n + i];\n            k += groupSums[i] = x;\n        }\n        k = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.max)(0, _math_js__WEBPACK_IMPORTED_MODULE_0__.tau - padAngle * n) / k;\n        dx = k ? padAngle : _math_js__WEBPACK_IMPORTED_MODULE_0__.tau / n;\n        // Compute the angles for each group and constituent chord.\n        {\n            let x = 0;\n            if (sortGroups) groupIndex.sort((a, b)=>sortGroups(groupSums[a], groupSums[b]));\n            for (const i of groupIndex){\n                const x0 = x;\n                if (directed) {\n                    const subgroupIndex = range(~n + 1, n).filter((j)=>j < 0 ? matrix[~j * n + i] : matrix[i * n + j]);\n                    if (sortSubgroups) subgroupIndex.sort((a, b)=>sortSubgroups(a < 0 ? -matrix[~a * n + i] : matrix[i * n + a], b < 0 ? -matrix[~b * n + i] : matrix[i * n + b]));\n                    for (const j of subgroupIndex){\n                        if (j < 0) {\n                            const chord = chords[~j * n + i] || (chords[~j * n + i] = {\n                                source: null,\n                                target: null\n                            });\n                            chord.target = {\n                                index: i,\n                                startAngle: x,\n                                endAngle: x += matrix[~j * n + i] * k,\n                                value: matrix[~j * n + i]\n                            };\n                        } else {\n                            const chord = chords[i * n + j] || (chords[i * n + j] = {\n                                source: null,\n                                target: null\n                            });\n                            chord.source = {\n                                index: i,\n                                startAngle: x,\n                                endAngle: x += matrix[i * n + j] * k,\n                                value: matrix[i * n + j]\n                            };\n                        }\n                    }\n                    groups[i] = {\n                        index: i,\n                        startAngle: x0,\n                        endAngle: x,\n                        value: groupSums[i]\n                    };\n                } else {\n                    const subgroupIndex = range(0, n).filter((j)=>matrix[i * n + j] || matrix[j * n + i]);\n                    if (sortSubgroups) subgroupIndex.sort((a, b)=>sortSubgroups(matrix[i * n + a], matrix[i * n + b]));\n                    for (const j of subgroupIndex){\n                        let chord;\n                        if (i < j) {\n                            chord = chords[i * n + j] || (chords[i * n + j] = {\n                                source: null,\n                                target: null\n                            });\n                            chord.source = {\n                                index: i,\n                                startAngle: x,\n                                endAngle: x += matrix[i * n + j] * k,\n                                value: matrix[i * n + j]\n                            };\n                        } else {\n                            chord = chords[j * n + i] || (chords[j * n + i] = {\n                                source: null,\n                                target: null\n                            });\n                            chord.target = {\n                                index: i,\n                                startAngle: x,\n                                endAngle: x += matrix[i * n + j] * k,\n                                value: matrix[i * n + j]\n                            };\n                            if (i === j) chord.source = chord.target;\n                        }\n                        if (chord.source && chord.target && chord.source.value < chord.target.value) {\n                            const source = chord.source;\n                            chord.source = chord.target;\n                            chord.target = source;\n                        }\n                    }\n                    groups[i] = {\n                        index: i,\n                        startAngle: x0,\n                        endAngle: x,\n                        value: groupSums[i]\n                    };\n                }\n                x += dx;\n            }\n        }\n        // Remove empty chords.\n        chords = Object.values(chords);\n        chords.groups = groups;\n        return sortChords ? chords.sort(sortChords) : chords;\n    }\n    chord.padAngle = function(_) {\n        return arguments.length ? (padAngle = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.max)(0, _), chord) : padAngle;\n    };\n    chord.sortGroups = function(_) {\n        return arguments.length ? (sortGroups = _, chord) : sortGroups;\n    };\n    chord.sortSubgroups = function(_) {\n        return arguments.length ? (sortSubgroups = _, chord) : sortSubgroups;\n    };\n    chord.sortChords = function(_) {\n        return arguments.length ? (_ == null ? sortChords = null : (sortChords = compareValue(_))._ = _, chord) : sortChords && sortChords._;\n    };\n    return chord;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-chord/src/chord.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-chord/src/constant.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-chord/src/constant.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return function() {\n        return x;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY2hvcmQvc3JjL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBU0EsQ0FBQztJQUN2QixPQUFPO1FBQ0wsT0FBT0E7SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtY2hvcmQvc3JjL2NvbnN0YW50LmpzPzRjNGEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oeCkge1xuICByZXR1cm4gZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIHg7XG4gIH07XG59XG4iXSwibmFtZXMiOlsieCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-chord/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-chord/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/d3-chord/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chord: () => (/* reexport safe */ _chord_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   chordDirected: () => (/* reexport safe */ _chord_js__WEBPACK_IMPORTED_MODULE_0__.chordDirected),\n/* harmony export */   chordTranspose: () => (/* reexport safe */ _chord_js__WEBPACK_IMPORTED_MODULE_0__.chordTranspose),\n/* harmony export */   ribbon: () => (/* reexport safe */ _ribbon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ribbonArrow: () => (/* reexport safe */ _ribbon_js__WEBPACK_IMPORTED_MODULE_1__.ribbonArrow)\n/* harmony export */ });\n/* harmony import */ var _chord_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chord.js */ \"(ssr)/./node_modules/d3-chord/src/chord.js\");\n/* harmony import */ var _ribbon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ribbon.js */ \"(ssr)/./node_modules/d3-chord/src/ribbon.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY2hvcmQvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBMkU7QUFDaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1jaG9yZC9zcmMvaW5kZXguanM/ZWEyOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge2RlZmF1bHQgYXMgY2hvcmQsIGNob3JkVHJhbnNwb3NlLCBjaG9yZERpcmVjdGVkfSBmcm9tIFwiLi9jaG9yZC5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHJpYmJvbiwgcmliYm9uQXJyb3d9IGZyb20gXCIuL3JpYmJvbi5qc1wiO1xuIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJjaG9yZCIsImNob3JkVHJhbnNwb3NlIiwiY2hvcmREaXJlY3RlZCIsInJpYmJvbiIsInJpYmJvbkFycm93Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-chord/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-chord/src/math.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-chord/src/math.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   abs: () => (/* binding */ abs),\n/* harmony export */   cos: () => (/* binding */ cos),\n/* harmony export */   epsilon: () => (/* binding */ epsilon),\n/* harmony export */   halfPi: () => (/* binding */ halfPi),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   pi: () => (/* binding */ pi),\n/* harmony export */   sin: () => (/* binding */ sin),\n/* harmony export */   tau: () => (/* binding */ tau)\n/* harmony export */ });\nvar abs = Math.abs;\nvar cos = Math.cos;\nvar sin = Math.sin;\nvar pi = Math.PI;\nvar halfPi = pi / 2;\nvar tau = pi * 2;\nvar max = Math.max;\nvar epsilon = 1e-12;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY2hvcmQvc3JjL21hdGguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBTyxJQUFJQSxNQUFNQyxLQUFLRCxHQUFHLENBQUM7QUFDbkIsSUFBSUUsTUFBTUQsS0FBS0MsR0FBRyxDQUFDO0FBQ25CLElBQUlDLE1BQU1GLEtBQUtFLEdBQUcsQ0FBQztBQUNuQixJQUFJQyxLQUFLSCxLQUFLSSxFQUFFLENBQUM7QUFDakIsSUFBSUMsU0FBU0YsS0FBSyxFQUFFO0FBQ3BCLElBQUlHLE1BQU1ILEtBQUssRUFBRTtBQUNqQixJQUFJSSxNQUFNUCxLQUFLTyxHQUFHLENBQUM7QUFDbkIsSUFBSUMsVUFBVSxNQUFNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtY2hvcmQvc3JjL21hdGguanM/MGM2OSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIGFicyA9IE1hdGguYWJzO1xuZXhwb3J0IHZhciBjb3MgPSBNYXRoLmNvcztcbmV4cG9ydCB2YXIgc2luID0gTWF0aC5zaW47XG5leHBvcnQgdmFyIHBpID0gTWF0aC5QSTtcbmV4cG9ydCB2YXIgaGFsZlBpID0gcGkgLyAyO1xuZXhwb3J0IHZhciB0YXUgPSBwaSAqIDI7XG5leHBvcnQgdmFyIG1heCA9IE1hdGgubWF4O1xuZXhwb3J0IHZhciBlcHNpbG9uID0gMWUtMTI7XG4iXSwibmFtZXMiOlsiYWJzIiwiTWF0aCIsImNvcyIsInNpbiIsInBpIiwiUEkiLCJoYWxmUGkiLCJ0YXUiLCJtYXgiLCJlcHNpbG9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-chord/src/math.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-chord/src/ribbon.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-chord/src/ribbon.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   ribbonArrow: () => (/* binding */ ribbonArrow)\n/* harmony export */ });\n/* harmony import */ var d3_path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-path */ \"(ssr)/./node_modules/d3-path/src/path.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-chord/src/array.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-chord/src/constant.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-chord/src/math.js\");\n\n\n\n\nfunction defaultSource(d) {\n    return d.source;\n}\nfunction defaultTarget(d) {\n    return d.target;\n}\nfunction defaultRadius(d) {\n    return d.radius;\n}\nfunction defaultStartAngle(d) {\n    return d.startAngle;\n}\nfunction defaultEndAngle(d) {\n    return d.endAngle;\n}\nfunction defaultPadAngle() {\n    return 0;\n}\nfunction defaultArrowheadRadius() {\n    return 10;\n}\nfunction ribbon(headRadius) {\n    var source = defaultSource, target = defaultTarget, sourceRadius = defaultRadius, targetRadius = defaultRadius, startAngle = defaultStartAngle, endAngle = defaultEndAngle, padAngle = defaultPadAngle, context = null;\n    function ribbon() {\n        var buffer, s = source.apply(this, arguments), t = target.apply(this, arguments), ap = padAngle.apply(this, arguments) / 2, argv = _array_js__WEBPACK_IMPORTED_MODULE_0__.slice.call(arguments), sr = +sourceRadius.apply(this, (argv[0] = s, argv)), sa0 = startAngle.apply(this, argv) - _math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi, sa1 = endAngle.apply(this, argv) - _math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi, tr = +targetRadius.apply(this, (argv[0] = t, argv)), ta0 = startAngle.apply(this, argv) - _math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi, ta1 = endAngle.apply(this, argv) - _math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi;\n        if (!context) context = buffer = (0,d3_path__WEBPACK_IMPORTED_MODULE_2__.path)();\n        if (ap > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) {\n            if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(sa1 - sa0) > ap * 2 + _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) sa1 > sa0 ? (sa0 += ap, sa1 -= ap) : (sa0 -= ap, sa1 += ap);\n            else sa0 = sa1 = (sa0 + sa1) / 2;\n            if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(ta1 - ta0) > ap * 2 + _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) ta1 > ta0 ? (ta0 += ap, ta1 -= ap) : (ta0 -= ap, ta1 += ap);\n            else ta0 = ta1 = (ta0 + ta1) / 2;\n        }\n        context.moveTo(sr * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(sa0), sr * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(sa0));\n        context.arc(0, 0, sr, sa0, sa1);\n        if (sa0 !== ta0 || sa1 !== ta1) {\n            if (headRadius) {\n                var hr = +headRadius.apply(this, arguments), tr2 = tr - hr, ta2 = (ta0 + ta1) / 2;\n                context.quadraticCurveTo(0, 0, tr2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(ta0), tr2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(ta0));\n                context.lineTo(tr * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(ta2), tr * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(ta2));\n                context.lineTo(tr2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(ta1), tr2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(ta1));\n            } else {\n                context.quadraticCurveTo(0, 0, tr * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(ta0), tr * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(ta0));\n                context.arc(0, 0, tr, ta0, ta1);\n            }\n        }\n        context.quadraticCurveTo(0, 0, sr * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(sa0), sr * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(sa0));\n        context.closePath();\n        if (buffer) return context = null, buffer + \"\" || null;\n    }\n    if (headRadius) ribbon.headRadius = function(_) {\n        return arguments.length ? (headRadius = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(+_), ribbon) : headRadius;\n    };\n    ribbon.radius = function(_) {\n        return arguments.length ? (sourceRadius = targetRadius = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(+_), ribbon) : sourceRadius;\n    };\n    ribbon.sourceRadius = function(_) {\n        return arguments.length ? (sourceRadius = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(+_), ribbon) : sourceRadius;\n    };\n    ribbon.targetRadius = function(_) {\n        return arguments.length ? (targetRadius = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(+_), ribbon) : targetRadius;\n    };\n    ribbon.startAngle = function(_) {\n        return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(+_), ribbon) : startAngle;\n    };\n    ribbon.endAngle = function(_) {\n        return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(+_), ribbon) : endAngle;\n    };\n    ribbon.padAngle = function(_) {\n        return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(+_), ribbon) : padAngle;\n    };\n    ribbon.source = function(_) {\n        return arguments.length ? (source = _, ribbon) : source;\n    };\n    ribbon.target = function(_) {\n        return arguments.length ? (target = _, ribbon) : target;\n    };\n    ribbon.context = function(_) {\n        return arguments.length ? (context = _ == null ? null : _, ribbon) : context;\n    };\n    return ribbon;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return ribbon();\n}\nfunction ribbonArrow() {\n    return ribbon(defaultArrowheadRadius);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-chord/src/ribbon.js\n");

/***/ })

};
;