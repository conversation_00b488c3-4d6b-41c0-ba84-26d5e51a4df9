"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-delaunay";
exports.ids = ["vendor-chunks/d3-delaunay"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-delaunay/src/delaunay.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-delaunay/src/delaunay.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Delaunay)\n/* harmony export */ });\n/* harmony import */ var delaunator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! delaunator */ \"(ssr)/./node_modules/delaunator/index.js\");\n/* harmony import */ var _path_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./path.js */ \"(ssr)/./node_modules/d3-delaunay/src/path.js\");\n/* harmony import */ var _polygon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./polygon.js */ \"(ssr)/./node_modules/d3-delaunay/src/polygon.js\");\n/* harmony import */ var _voronoi_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./voronoi.js */ \"(ssr)/./node_modules/d3-delaunay/src/voronoi.js\");\n\n\n\n\nconst tau = 2 * Math.PI, pow = Math.pow;\nfunction pointX(p) {\n    return p[0];\n}\nfunction pointY(p) {\n    return p[1];\n}\n// A triangulation is collinear if all its triangles have a non-null area\nfunction collinear(d) {\n    const { triangles, coords } = d;\n    for(let i = 0; i < triangles.length; i += 3){\n        const a = 2 * triangles[i], b = 2 * triangles[i + 1], c = 2 * triangles[i + 2], cross = (coords[c] - coords[a]) * (coords[b + 1] - coords[a + 1]) - (coords[b] - coords[a]) * (coords[c + 1] - coords[a + 1]);\n        if (cross > 1e-10) return false;\n    }\n    return true;\n}\nfunction jitter(x, y, r) {\n    return [\n        x + Math.sin(x + y) * r,\n        y + Math.cos(x - y) * r\n    ];\n}\nclass Delaunay {\n    static from(points, fx = pointX, fy = pointY, that) {\n        return new Delaunay(\"length\" in points ? flatArray(points, fx, fy, that) : Float64Array.from(flatIterable(points, fx, fy, that)));\n    }\n    constructor(points){\n        this._delaunator = new delaunator__WEBPACK_IMPORTED_MODULE_0__[\"default\"](points);\n        this.inedges = new Int32Array(points.length / 2);\n        this._hullIndex = new Int32Array(points.length / 2);\n        this.points = this._delaunator.coords;\n        this._init();\n    }\n    update() {\n        this._delaunator.update();\n        this._init();\n        return this;\n    }\n    _init() {\n        const d = this._delaunator, points = this.points;\n        // check for collinear\n        if (d.hull && d.hull.length > 2 && collinear(d)) {\n            this.collinear = Int32Array.from({\n                length: points.length / 2\n            }, (_, i)=>i).sort((i, j)=>points[2 * i] - points[2 * j] || points[2 * i + 1] - points[2 * j + 1]); // for exact neighbors\n            const e = this.collinear[0], f = this.collinear[this.collinear.length - 1], bounds = [\n                points[2 * e],\n                points[2 * e + 1],\n                points[2 * f],\n                points[2 * f + 1]\n            ], r = 1e-8 * Math.hypot(bounds[3] - bounds[1], bounds[2] - bounds[0]);\n            for(let i = 0, n = points.length / 2; i < n; ++i){\n                const p = jitter(points[2 * i], points[2 * i + 1], r);\n                points[2 * i] = p[0];\n                points[2 * i + 1] = p[1];\n            }\n            this._delaunator = new delaunator__WEBPACK_IMPORTED_MODULE_0__[\"default\"](points);\n        } else {\n            delete this.collinear;\n        }\n        const halfedges = this.halfedges = this._delaunator.halfedges;\n        const hull = this.hull = this._delaunator.hull;\n        const triangles = this.triangles = this._delaunator.triangles;\n        const inedges = this.inedges.fill(-1);\n        const hullIndex = this._hullIndex.fill(-1);\n        // Compute an index from each point to an (arbitrary) incoming halfedge\n        // Used to give the first neighbor of each point; for this reason,\n        // on the hull we give priority to exterior halfedges\n        for(let e = 0, n = halfedges.length; e < n; ++e){\n            const p = triangles[e % 3 === 2 ? e - 2 : e + 1];\n            if (halfedges[e] === -1 || inedges[p] === -1) inedges[p] = e;\n        }\n        for(let i = 0, n = hull.length; i < n; ++i){\n            hullIndex[hull[i]] = i;\n        }\n        // degenerate case: 1 or 2 (distinct) points\n        if (hull.length <= 2 && hull.length > 0) {\n            this.triangles = new Int32Array(3).fill(-1);\n            this.halfedges = new Int32Array(3).fill(-1);\n            this.triangles[0] = hull[0];\n            inedges[hull[0]] = 1;\n            if (hull.length === 2) {\n                inedges[hull[1]] = 0;\n                this.triangles[1] = hull[1];\n                this.triangles[2] = hull[1];\n            }\n        }\n    }\n    voronoi(bounds) {\n        return new _voronoi_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, bounds);\n    }\n    *neighbors(i) {\n        const { inedges, hull, _hullIndex, halfedges, triangles, collinear } = this;\n        // degenerate case with several collinear points\n        if (collinear) {\n            const l = collinear.indexOf(i);\n            if (l > 0) yield collinear[l - 1];\n            if (l < collinear.length - 1) yield collinear[l + 1];\n            return;\n        }\n        const e0 = inedges[i];\n        if (e0 === -1) return; // coincident point\n        let e = e0, p0 = -1;\n        do {\n            yield p0 = triangles[e];\n            e = e % 3 === 2 ? e - 2 : e + 1;\n            if (triangles[e] !== i) return; // bad triangulation\n            e = halfedges[e];\n            if (e === -1) {\n                const p = hull[(_hullIndex[i] + 1) % hull.length];\n                if (p !== p0) yield p;\n                return;\n            }\n        }while (e !== e0);\n    }\n    find(x, y, i = 0) {\n        if ((x = +x, x !== x) || (y = +y, y !== y)) return -1;\n        const i0 = i;\n        let c;\n        while((c = this._step(i, x, y)) >= 0 && c !== i && c !== i0)i = c;\n        return c;\n    }\n    _step(i, x, y) {\n        const { inedges, hull, _hullIndex, halfedges, triangles, points } = this;\n        if (inedges[i] === -1 || !points.length) return (i + 1) % (points.length >> 1);\n        let c = i;\n        let dc = pow(x - points[i * 2], 2) + pow(y - points[i * 2 + 1], 2);\n        const e0 = inedges[i];\n        let e = e0;\n        do {\n            let t = triangles[e];\n            const dt = pow(x - points[t * 2], 2) + pow(y - points[t * 2 + 1], 2);\n            if (dt < dc) dc = dt, c = t;\n            e = e % 3 === 2 ? e - 2 : e + 1;\n            if (triangles[e] !== i) break; // bad triangulation\n            e = halfedges[e];\n            if (e === -1) {\n                e = hull[(_hullIndex[i] + 1) % hull.length];\n                if (e !== t) {\n                    if (pow(x - points[e * 2], 2) + pow(y - points[e * 2 + 1], 2) < dc) return e;\n                }\n                break;\n            }\n        }while (e !== e0);\n        return c;\n    }\n    render(context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n        const { points, halfedges, triangles } = this;\n        for(let i = 0, n = halfedges.length; i < n; ++i){\n            const j = halfedges[i];\n            if (j < i) continue;\n            const ti = triangles[i] * 2;\n            const tj = triangles[j] * 2;\n            context.moveTo(points[ti], points[ti + 1]);\n            context.lineTo(points[tj], points[tj + 1]);\n        }\n        this.renderHull(context);\n        return buffer && buffer.value();\n    }\n    renderPoints(context, r) {\n        if (r === undefined && (!context || typeof context.moveTo !== \"function\")) r = context, context = null;\n        r = r == undefined ? 2 : +r;\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n        const { points } = this;\n        for(let i = 0, n = points.length; i < n; i += 2){\n            const x = points[i], y = points[i + 1];\n            context.moveTo(x + r, y);\n            context.arc(x, y, r, 0, tau);\n        }\n        return buffer && buffer.value();\n    }\n    renderHull(context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n        const { hull, points } = this;\n        const h = hull[0] * 2, n = hull.length;\n        context.moveTo(points[h], points[h + 1]);\n        for(let i = 1; i < n; ++i){\n            const h = 2 * hull[i];\n            context.lineTo(points[h], points[h + 1]);\n        }\n        context.closePath();\n        return buffer && buffer.value();\n    }\n    hullPolygon() {\n        const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        this.renderHull(polygon);\n        return polygon.value();\n    }\n    renderTriangle(i, context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n        const { points, triangles } = this;\n        const t0 = triangles[i *= 3] * 2;\n        const t1 = triangles[i + 1] * 2;\n        const t2 = triangles[i + 2] * 2;\n        context.moveTo(points[t0], points[t0 + 1]);\n        context.lineTo(points[t1], points[t1 + 1]);\n        context.lineTo(points[t2], points[t2 + 1]);\n        context.closePath();\n        return buffer && buffer.value();\n    }\n    *trianglePolygons() {\n        const { triangles } = this;\n        for(let i = 0, n = triangles.length / 3; i < n; ++i){\n            yield this.trianglePolygon(i);\n        }\n    }\n    trianglePolygon(i) {\n        const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        this.renderTriangle(i, polygon);\n        return polygon.value();\n    }\n}\nfunction flatArray(points, fx, fy, that) {\n    const n = points.length;\n    const array = new Float64Array(n * 2);\n    for(let i = 0; i < n; ++i){\n        const p = points[i];\n        array[i * 2] = fx.call(that, p, i, points);\n        array[i * 2 + 1] = fy.call(that, p, i, points);\n    }\n    return array;\n}\nfunction* flatIterable(points, fx, fy, that) {\n    let i = 0;\n    for (const p of points){\n        yield fx.call(that, p, i, points);\n        yield fy.call(that, p, i, points);\n        ++i;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/delaunay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/index.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-delaunay/src/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Delaunay: () => (/* reexport safe */ _delaunay_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Voronoi: () => (/* reexport safe */ _voronoi_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _delaunay_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./delaunay.js */ \"(ssr)/./node_modules/d3-delaunay/src/delaunay.js\");\n/* harmony import */ var _voronoi_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./voronoi.js */ \"(ssr)/./node_modules/d3-delaunay/src/voronoi.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZGVsYXVuYXkvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBa0Q7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLWRlbGF1bmF5L3NyYy9pbmRleC5qcz84ZGJiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7ZGVmYXVsdCBhcyBEZWxhdW5heX0gZnJvbSBcIi4vZGVsYXVuYXkuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBWb3Jvbm9pfSBmcm9tIFwiLi92b3Jvbm9pLmpzXCI7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIkRlbGF1bmF5IiwiVm9yb25vaSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/path.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-delaunay/src/path.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Path)\n/* harmony export */ });\nconst epsilon = 1e-6;\nclass Path {\n    constructor(){\n        this._x0 = this._y0 = this._x1 = this._y1 = null; // end of current subpath\n        this._ = \"\";\n    }\n    moveTo(x, y) {\n        this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n    }\n    closePath() {\n        if (this._x1 !== null) {\n            this._x1 = this._x0, this._y1 = this._y0;\n            this._ += \"Z\";\n        }\n    }\n    lineTo(x, y) {\n        this._ += `L${this._x1 = +x},${this._y1 = +y}`;\n    }\n    arc(x, y, r) {\n        x = +x, y = +y, r = +r;\n        const x0 = x + r;\n        const y0 = y;\n        if (r < 0) throw new Error(\"negative radius\");\n        if (this._x1 === null) this._ += `M${x0},${y0}`;\n        else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) this._ += \"L\" + x0 + \",\" + y0;\n        if (!r) return;\n        this._ += `A${r},${r},0,1,1,${x - r},${y}A${r},${r},0,1,1,${this._x1 = x0},${this._y1 = y0}`;\n    }\n    rect(x, y, w, h) {\n        this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${+w}v${+h}h${-w}Z`;\n    }\n    value() {\n        return this._ || null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/path.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/polygon.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-delaunay/src/polygon.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Polygon)\n/* harmony export */ });\nclass Polygon {\n    constructor(){\n        this._ = [];\n    }\n    moveTo(x, y) {\n        this._.push([\n            x,\n            y\n        ]);\n    }\n    closePath() {\n        this._.push(this._[0].slice());\n    }\n    lineTo(x, y) {\n        this._.push([\n            x,\n            y\n        ]);\n    }\n    value() {\n        return this._.length ? this._ : null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZGVsYXVuYXkvc3JjL3BvbHlnb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLE1BQU1BO0lBQ25CQyxhQUFjO1FBQ1osSUFBSSxDQUFDQyxDQUFDLEdBQUcsRUFBRTtJQUNiO0lBQ0FDLE9BQU9DLENBQUMsRUFBRUMsQ0FBQyxFQUFFO1FBQ1gsSUFBSSxDQUFDSCxDQUFDLENBQUNJLElBQUksQ0FBQztZQUFDRjtZQUFHQztTQUFFO0lBQ3BCO0lBQ0FFLFlBQVk7UUFDVixJQUFJLENBQUNMLENBQUMsQ0FBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQ0osQ0FBQyxDQUFDLEVBQUUsQ0FBQ00sS0FBSztJQUM3QjtJQUNBQyxPQUFPTCxDQUFDLEVBQUVDLENBQUMsRUFBRTtRQUNYLElBQUksQ0FBQ0gsQ0FBQyxDQUFDSSxJQUFJLENBQUM7WUFBQ0Y7WUFBR0M7U0FBRTtJQUNwQjtJQUNBSyxRQUFRO1FBQ04sT0FBTyxJQUFJLENBQUNSLENBQUMsQ0FBQ1MsTUFBTSxHQUFHLElBQUksQ0FBQ1QsQ0FBQyxHQUFHO0lBQ2xDO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1kZWxhdW5heS9zcmMvcG9seWdvbi5qcz82ZjA3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGNsYXNzIFBvbHlnb24ge1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICB0aGlzLl8gPSBbXTtcbiAgfVxuICBtb3ZlVG8oeCwgeSkge1xuICAgIHRoaXMuXy5wdXNoKFt4LCB5XSk7XG4gIH1cbiAgY2xvc2VQYXRoKCkge1xuICAgIHRoaXMuXy5wdXNoKHRoaXMuX1swXS5zbGljZSgpKTtcbiAgfVxuICBsaW5lVG8oeCwgeSkge1xuICAgIHRoaXMuXy5wdXNoKFt4LCB5XSk7XG4gIH1cbiAgdmFsdWUoKSB7XG4gICAgcmV0dXJuIHRoaXMuXy5sZW5ndGggPyB0aGlzLl8gOiBudWxsO1xuICB9XG59XG4iXSwibmFtZXMiOlsiUG9seWdvbiIsImNvbnN0cnVjdG9yIiwiXyIsIm1vdmVUbyIsIngiLCJ5IiwicHVzaCIsImNsb3NlUGF0aCIsInNsaWNlIiwibGluZVRvIiwidmFsdWUiLCJsZW5ndGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/polygon.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/voronoi.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-delaunay/src/voronoi.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Voronoi)\n/* harmony export */ });\n/* harmony import */ var _path_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./path.js */ \"(ssr)/./node_modules/d3-delaunay/src/path.js\");\n/* harmony import */ var _polygon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./polygon.js */ \"(ssr)/./node_modules/d3-delaunay/src/polygon.js\");\n\n\nclass Voronoi {\n    constructor(delaunay, [xmin, ymin, xmax, ymax] = [\n        0,\n        0,\n        960,\n        500\n    ]){\n        if (!((xmax = +xmax) >= (xmin = +xmin)) || !((ymax = +ymax) >= (ymin = +ymin))) throw new Error(\"invalid bounds\");\n        this.delaunay = delaunay;\n        this._circumcenters = new Float64Array(delaunay.points.length * 2);\n        this.vectors = new Float64Array(delaunay.points.length * 2);\n        this.xmax = xmax, this.xmin = xmin;\n        this.ymax = ymax, this.ymin = ymin;\n        this._init();\n    }\n    update() {\n        this.delaunay.update();\n        this._init();\n        return this;\n    }\n    _init() {\n        const { delaunay: { points, hull, triangles }, vectors } = this;\n        let bx, by; // lazily computed barycenter of the hull\n        // Compute circumcenters.\n        const circumcenters = this.circumcenters = this._circumcenters.subarray(0, triangles.length / 3 * 2);\n        for(let i = 0, j = 0, n = triangles.length, x, y; i < n; i += 3, j += 2){\n            const t1 = triangles[i] * 2;\n            const t2 = triangles[i + 1] * 2;\n            const t3 = triangles[i + 2] * 2;\n            const x1 = points[t1];\n            const y1 = points[t1 + 1];\n            const x2 = points[t2];\n            const y2 = points[t2 + 1];\n            const x3 = points[t3];\n            const y3 = points[t3 + 1];\n            const dx = x2 - x1;\n            const dy = y2 - y1;\n            const ex = x3 - x1;\n            const ey = y3 - y1;\n            const ab = (dx * ey - dy * ex) * 2;\n            if (Math.abs(ab) < 1e-9) {\n                // For a degenerate triangle, the circumcenter is at the infinity, in a\n                // direction orthogonal to the halfedge and away from the “center” of\n                // the diagram <bx, by>, defined as the hull’s barycenter.\n                if (bx === undefined) {\n                    bx = by = 0;\n                    for (const i of hull)bx += points[i * 2], by += points[i * 2 + 1];\n                    bx /= hull.length, by /= hull.length;\n                }\n                const a = 1e9 * Math.sign((bx - x1) * ey - (by - y1) * ex);\n                x = (x1 + x3) / 2 - a * ey;\n                y = (y1 + y3) / 2 + a * ex;\n            } else {\n                const d = 1 / ab;\n                const bl = dx * dx + dy * dy;\n                const cl = ex * ex + ey * ey;\n                x = x1 + (ey * bl - dy * cl) * d;\n                y = y1 + (dx * cl - ex * bl) * d;\n            }\n            circumcenters[j] = x;\n            circumcenters[j + 1] = y;\n        }\n        // Compute exterior cell rays.\n        let h = hull[hull.length - 1];\n        let p0, p1 = h * 4;\n        let x0, x1 = points[2 * h];\n        let y0, y1 = points[2 * h + 1];\n        vectors.fill(0);\n        for(let i = 0; i < hull.length; ++i){\n            h = hull[i];\n            p0 = p1, x0 = x1, y0 = y1;\n            p1 = h * 4, x1 = points[2 * h], y1 = points[2 * h + 1];\n            vectors[p0 + 2] = vectors[p1] = y0 - y1;\n            vectors[p0 + 3] = vectors[p1 + 1] = x1 - x0;\n        }\n    }\n    render(context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n        const { delaunay: { halfedges, inedges, hull }, circumcenters, vectors } = this;\n        if (hull.length <= 1) return null;\n        for(let i = 0, n = halfedges.length; i < n; ++i){\n            const j = halfedges[i];\n            if (j < i) continue;\n            const ti = Math.floor(i / 3) * 2;\n            const tj = Math.floor(j / 3) * 2;\n            const xi = circumcenters[ti];\n            const yi = circumcenters[ti + 1];\n            const xj = circumcenters[tj];\n            const yj = circumcenters[tj + 1];\n            this._renderSegment(xi, yi, xj, yj, context);\n        }\n        let h0, h1 = hull[hull.length - 1];\n        for(let i = 0; i < hull.length; ++i){\n            h0 = h1, h1 = hull[i];\n            const t = Math.floor(inedges[h1] / 3) * 2;\n            const x = circumcenters[t];\n            const y = circumcenters[t + 1];\n            const v = h0 * 4;\n            const p = this._project(x, y, vectors[v + 2], vectors[v + 3]);\n            if (p) this._renderSegment(x, y, p[0], p[1], context);\n        }\n        return buffer && buffer.value();\n    }\n    renderBounds(context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n        context.rect(this.xmin, this.ymin, this.xmax - this.xmin, this.ymax - this.ymin);\n        return buffer && buffer.value();\n    }\n    renderCell(i, context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n        const points = this._clip(i);\n        if (points === null || !points.length) return;\n        context.moveTo(points[0], points[1]);\n        let n = points.length;\n        while(points[0] === points[n - 2] && points[1] === points[n - 1] && n > 1)n -= 2;\n        for(let i = 2; i < n; i += 2){\n            if (points[i] !== points[i - 2] || points[i + 1] !== points[i - 1]) context.lineTo(points[i], points[i + 1]);\n        }\n        context.closePath();\n        return buffer && buffer.value();\n    }\n    *cellPolygons() {\n        const { delaunay: { points } } = this;\n        for(let i = 0, n = points.length / 2; i < n; ++i){\n            const cell = this.cellPolygon(i);\n            if (cell) cell.index = i, yield cell;\n        }\n    }\n    cellPolygon(i) {\n        const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        this.renderCell(i, polygon);\n        return polygon.value();\n    }\n    _renderSegment(x0, y0, x1, y1, context) {\n        let S;\n        const c0 = this._regioncode(x0, y0);\n        const c1 = this._regioncode(x1, y1);\n        if (c0 === 0 && c1 === 0) {\n            context.moveTo(x0, y0);\n            context.lineTo(x1, y1);\n        } else if (S = this._clipSegment(x0, y0, x1, y1, c0, c1)) {\n            context.moveTo(S[0], S[1]);\n            context.lineTo(S[2], S[3]);\n        }\n    }\n    contains(i, x, y) {\n        if ((x = +x, x !== x) || (y = +y, y !== y)) return false;\n        return this.delaunay._step(i, x, y) === i;\n    }\n    *neighbors(i) {\n        const ci = this._clip(i);\n        if (ci) for (const j of this.delaunay.neighbors(i)){\n            const cj = this._clip(j);\n            // find the common edge\n            if (cj) loop: for(let ai = 0, li = ci.length; ai < li; ai += 2){\n                for(let aj = 0, lj = cj.length; aj < lj; aj += 2){\n                    if (ci[ai] === cj[aj] && ci[ai + 1] === cj[aj + 1] && ci[(ai + 2) % li] === cj[(aj + lj - 2) % lj] && ci[(ai + 3) % li] === cj[(aj + lj - 1) % lj]) {\n                        yield j;\n                        break loop;\n                    }\n                }\n            }\n        }\n    }\n    _cell(i) {\n        const { circumcenters, delaunay: { inedges, halfedges, triangles } } = this;\n        const e0 = inedges[i];\n        if (e0 === -1) return null; // coincident point\n        const points = [];\n        let e = e0;\n        do {\n            const t = Math.floor(e / 3);\n            points.push(circumcenters[t * 2], circumcenters[t * 2 + 1]);\n            e = e % 3 === 2 ? e - 2 : e + 1;\n            if (triangles[e] !== i) break; // bad triangulation\n            e = halfedges[e];\n        }while (e !== e0 && e !== -1);\n        return points;\n    }\n    _clip(i) {\n        // degenerate case (1 valid point: return the box)\n        if (i === 0 && this.delaunay.hull.length === 1) {\n            return [\n                this.xmax,\n                this.ymin,\n                this.xmax,\n                this.ymax,\n                this.xmin,\n                this.ymax,\n                this.xmin,\n                this.ymin\n            ];\n        }\n        const points = this._cell(i);\n        if (points === null) return null;\n        const { vectors: V } = this;\n        const v = i * 4;\n        return this._simplify(V[v] || V[v + 1] ? this._clipInfinite(i, points, V[v], V[v + 1], V[v + 2], V[v + 3]) : this._clipFinite(i, points));\n    }\n    _clipFinite(i, points) {\n        const n = points.length;\n        let P = null;\n        let x0, y0, x1 = points[n - 2], y1 = points[n - 1];\n        let c0, c1 = this._regioncode(x1, y1);\n        let e0, e1 = 0;\n        for(let j = 0; j < n; j += 2){\n            x0 = x1, y0 = y1, x1 = points[j], y1 = points[j + 1];\n            c0 = c1, c1 = this._regioncode(x1, y1);\n            if (c0 === 0 && c1 === 0) {\n                e0 = e1, e1 = 0;\n                if (P) P.push(x1, y1);\n                else P = [\n                    x1,\n                    y1\n                ];\n            } else {\n                let S, sx0, sy0, sx1, sy1;\n                if (c0 === 0) {\n                    if ((S = this._clipSegment(x0, y0, x1, y1, c0, c1)) === null) continue;\n                    [sx0, sy0, sx1, sy1] = S;\n                } else {\n                    if ((S = this._clipSegment(x1, y1, x0, y0, c1, c0)) === null) continue;\n                    [sx1, sy1, sx0, sy0] = S;\n                    e0 = e1, e1 = this._edgecode(sx0, sy0);\n                    if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n                    if (P) P.push(sx0, sy0);\n                    else P = [\n                        sx0,\n                        sy0\n                    ];\n                }\n                e0 = e1, e1 = this._edgecode(sx1, sy1);\n                if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n                if (P) P.push(sx1, sy1);\n                else P = [\n                    sx1,\n                    sy1\n                ];\n            }\n        }\n        if (P) {\n            e0 = e1, e1 = this._edgecode(P[0], P[1]);\n            if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n        } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n            return [\n                this.xmax,\n                this.ymin,\n                this.xmax,\n                this.ymax,\n                this.xmin,\n                this.ymax,\n                this.xmin,\n                this.ymin\n            ];\n        }\n        return P;\n    }\n    _clipSegment(x0, y0, x1, y1, c0, c1) {\n        // for more robustness, always consider the segment in the same order\n        const flip = c0 < c1;\n        if (flip) [x0, y0, x1, y1, c0, c1] = [\n            x1,\n            y1,\n            x0,\n            y0,\n            c1,\n            c0\n        ];\n        while(true){\n            if (c0 === 0 && c1 === 0) return flip ? [\n                x1,\n                y1,\n                x0,\n                y0\n            ] : [\n                x0,\n                y0,\n                x1,\n                y1\n            ];\n            if (c0 & c1) return null;\n            let x, y, c = c0 || c1;\n            if (c & 8) x = x0 + (x1 - x0) * (this.ymax - y0) / (y1 - y0), y = this.ymax;\n            else if (c & 4) x = x0 + (x1 - x0) * (this.ymin - y0) / (y1 - y0), y = this.ymin;\n            else if (c & 2) y = y0 + (y1 - y0) * (this.xmax - x0) / (x1 - x0), x = this.xmax;\n            else y = y0 + (y1 - y0) * (this.xmin - x0) / (x1 - x0), x = this.xmin;\n            if (c0) x0 = x, y0 = y, c0 = this._regioncode(x0, y0);\n            else x1 = x, y1 = y, c1 = this._regioncode(x1, y1);\n        }\n    }\n    _clipInfinite(i, points, vx0, vy0, vxn, vyn) {\n        let P = Array.from(points), p;\n        if (p = this._project(P[0], P[1], vx0, vy0)) P.unshift(p[0], p[1]);\n        if (p = this._project(P[P.length - 2], P[P.length - 1], vxn, vyn)) P.push(p[0], p[1]);\n        if (P = this._clipFinite(i, P)) {\n            for(let j = 0, n = P.length, c0, c1 = this._edgecode(P[n - 2], P[n - 1]); j < n; j += 2){\n                c0 = c1, c1 = this._edgecode(P[j], P[j + 1]);\n                if (c0 && c1) j = this._edge(i, c0, c1, P, j), n = P.length;\n            }\n        } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n            P = [\n                this.xmin,\n                this.ymin,\n                this.xmax,\n                this.ymin,\n                this.xmax,\n                this.ymax,\n                this.xmin,\n                this.ymax\n            ];\n        }\n        return P;\n    }\n    _edge(i, e0, e1, P, j) {\n        while(e0 !== e1){\n            let x, y;\n            switch(e0){\n                case 5:\n                    e0 = 4;\n                    continue; // top-left\n                case 4:\n                    e0 = 6, x = this.xmax, y = this.ymin;\n                    break; // top\n                case 6:\n                    e0 = 2;\n                    continue; // top-right\n                case 2:\n                    e0 = 10, x = this.xmax, y = this.ymax;\n                    break; // right\n                case 10:\n                    e0 = 8;\n                    continue; // bottom-right\n                case 8:\n                    e0 = 9, x = this.xmin, y = this.ymax;\n                    break; // bottom\n                case 9:\n                    e0 = 1;\n                    continue; // bottom-left\n                case 1:\n                    e0 = 5, x = this.xmin, y = this.ymin;\n                    break; // left\n            }\n            // Note: this implicitly checks for out of bounds: if P[j] or P[j+1] are\n            // undefined, the conditional statement will be executed.\n            if ((P[j] !== x || P[j + 1] !== y) && this.contains(i, x, y)) {\n                P.splice(j, 0, x, y), j += 2;\n            }\n        }\n        return j;\n    }\n    _project(x0, y0, vx, vy) {\n        let t = Infinity, c, x, y;\n        if (vy < 0) {\n            if (y0 <= this.ymin) return null;\n            if ((c = (this.ymin - y0) / vy) < t) y = this.ymin, x = x0 + (t = c) * vx;\n        } else if (vy > 0) {\n            if (y0 >= this.ymax) return null;\n            if ((c = (this.ymax - y0) / vy) < t) y = this.ymax, x = x0 + (t = c) * vx;\n        }\n        if (vx > 0) {\n            if (x0 >= this.xmax) return null;\n            if ((c = (this.xmax - x0) / vx) < t) x = this.xmax, y = y0 + (t = c) * vy;\n        } else if (vx < 0) {\n            if (x0 <= this.xmin) return null;\n            if ((c = (this.xmin - x0) / vx) < t) x = this.xmin, y = y0 + (t = c) * vy;\n        }\n        return [\n            x,\n            y\n        ];\n    }\n    _edgecode(x, y) {\n        return (x === this.xmin ? 1 : x === this.xmax ? 2 : 0) | (y === this.ymin ? 4 : y === this.ymax ? 8 : 0);\n    }\n    _regioncode(x, y) {\n        return (x < this.xmin ? 1 : x > this.xmax ? 2 : 0) | (y < this.ymin ? 4 : y > this.ymax ? 8 : 0);\n    }\n    _simplify(P) {\n        if (P && P.length > 4) {\n            for(let i = 0; i < P.length; i += 2){\n                const j = (i + 2) % P.length, k = (i + 4) % P.length;\n                if (P[i] === P[j] && P[j] === P[k] || P[i + 1] === P[j + 1] && P[j + 1] === P[k + 1]) {\n                    P.splice(j, 2), i -= 2;\n                }\n            }\n            if (!P.length) P = null;\n        }\n        return P;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/voronoi.js\n");

/***/ })

};
;