"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-timer";
exports.ids = ["vendor-chunks/d3-timer"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-timer/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/d3-timer/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   interval: () => (/* reexport safe */ _interval_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   now: () => (/* reexport safe */ _timer_js__WEBPACK_IMPORTED_MODULE_0__.now),\n/* harmony export */   timeout: () => (/* reexport safe */ _timeout_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   timer: () => (/* reexport safe */ _timer_js__WEBPACK_IMPORTED_MODULE_0__.timer),\n/* harmony export */   timerFlush: () => (/* reexport safe */ _timer_js__WEBPACK_IMPORTED_MODULE_0__.timerFlush)\n/* harmony export */ });\n/* harmony import */ var _timer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./timer.js */ \"(ssr)/./node_modules/d3-timer/src/timer.js\");\n/* harmony import */ var _timeout_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./timeout.js */ \"(ssr)/./node_modules/d3-timer/src/timeout.js\");\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/./node_modules/d3-timer/src/interval.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtdGltZXIvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBSW9CO0FBSUU7QUFJQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLXRpbWVyL3NyYy9pbmRleC5qcz84ZGZhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7XG4gIG5vdyxcbiAgdGltZXIsXG4gIHRpbWVyRmx1c2hcbn0gZnJvbSBcIi4vdGltZXIuanNcIjtcblxuZXhwb3J0IHtcbiAgZGVmYXVsdCBhcyB0aW1lb3V0XG59IGZyb20gXCIuL3RpbWVvdXQuanNcIjtcblxuZXhwb3J0IHtcbiAgZGVmYXVsdCBhcyBpbnRlcnZhbFxufSBmcm9tIFwiLi9pbnRlcnZhbC5qc1wiO1xuIl0sIm5hbWVzIjpbIm5vdyIsInRpbWVyIiwidGltZXJGbHVzaCIsImRlZmF1bHQiLCJ0aW1lb3V0IiwiaW50ZXJ2YWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-timer/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-timer/src/interval.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-timer/src/interval.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _timer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./timer.js */ \"(ssr)/./node_modules/d3-timer/src/timer.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(callback, delay, time) {\n    var t = new _timer_js__WEBPACK_IMPORTED_MODULE_0__.Timer, total = delay;\n    if (delay == null) return t.restart(callback, delay, time), t;\n    t._restart = t.restart;\n    t.restart = function(callback, delay, time) {\n        delay = +delay, time = time == null ? (0,_timer_js__WEBPACK_IMPORTED_MODULE_0__.now)() : +time;\n        t._restart(function tick(elapsed) {\n            elapsed += total;\n            t._restart(tick, total += delay, time);\n            callback(elapsed);\n        }, delay, time);\n    };\n    t.restart(callback, delay, time);\n    return t;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtdGltZXIvc3JjL2ludGVydmFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDO0FBRXRDLDZCQUFlLG9DQUFTRSxRQUFRLEVBQUVDLEtBQUssRUFBRUMsSUFBSTtJQUMzQyxJQUFJQyxJQUFJLElBQUlMLDRDQUFLQSxFQUFFTSxRQUFRSDtJQUMzQixJQUFJQSxTQUFTLE1BQU0sT0FBT0UsRUFBRUUsT0FBTyxDQUFDTCxVQUFVQyxPQUFPQyxPQUFPQztJQUM1REEsRUFBRUcsUUFBUSxHQUFHSCxFQUFFRSxPQUFPO0lBQ3RCRixFQUFFRSxPQUFPLEdBQUcsU0FBU0wsUUFBUSxFQUFFQyxLQUFLLEVBQUVDLElBQUk7UUFDeENELFFBQVEsQ0FBQ0EsT0FBT0MsT0FBT0EsUUFBUSxPQUFPSCw4Q0FBR0EsS0FBSyxDQUFDRztRQUMvQ0MsRUFBRUcsUUFBUSxDQUFDLFNBQVNDLEtBQUtDLE9BQU87WUFDOUJBLFdBQVdKO1lBQ1hELEVBQUVHLFFBQVEsQ0FBQ0MsTUFBTUgsU0FBU0gsT0FBT0M7WUFDakNGLFNBQVNRO1FBQ1gsR0FBR1AsT0FBT0M7SUFDWjtJQUNBQyxFQUFFRSxPQUFPLENBQUNMLFVBQVVDLE9BQU9DO0lBQzNCLE9BQU9DO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy10aW1lci9zcmMvaW50ZXJ2YWwuanM/NmI4ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1RpbWVyLCBub3d9IGZyb20gXCIuL3RpbWVyLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKGNhbGxiYWNrLCBkZWxheSwgdGltZSkge1xuICB2YXIgdCA9IG5ldyBUaW1lciwgdG90YWwgPSBkZWxheTtcbiAgaWYgKGRlbGF5ID09IG51bGwpIHJldHVybiB0LnJlc3RhcnQoY2FsbGJhY2ssIGRlbGF5LCB0aW1lKSwgdDtcbiAgdC5fcmVzdGFydCA9IHQucmVzdGFydDtcbiAgdC5yZXN0YXJ0ID0gZnVuY3Rpb24oY2FsbGJhY2ssIGRlbGF5LCB0aW1lKSB7XG4gICAgZGVsYXkgPSArZGVsYXksIHRpbWUgPSB0aW1lID09IG51bGwgPyBub3coKSA6ICt0aW1lO1xuICAgIHQuX3Jlc3RhcnQoZnVuY3Rpb24gdGljayhlbGFwc2VkKSB7XG4gICAgICBlbGFwc2VkICs9IHRvdGFsO1xuICAgICAgdC5fcmVzdGFydCh0aWNrLCB0b3RhbCArPSBkZWxheSwgdGltZSk7XG4gICAgICBjYWxsYmFjayhlbGFwc2VkKTtcbiAgICB9LCBkZWxheSwgdGltZSk7XG4gIH1cbiAgdC5yZXN0YXJ0KGNhbGxiYWNrLCBkZWxheSwgdGltZSk7XG4gIHJldHVybiB0O1xufVxuIl0sIm5hbWVzIjpbIlRpbWVyIiwibm93IiwiY2FsbGJhY2siLCJkZWxheSIsInRpbWUiLCJ0IiwidG90YWwiLCJyZXN0YXJ0IiwiX3Jlc3RhcnQiLCJ0aWNrIiwiZWxhcHNlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-timer/src/interval.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-timer/src/timeout.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-timer/src/timeout.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _timer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./timer.js */ \"(ssr)/./node_modules/d3-timer/src/timer.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(callback, delay, time) {\n    var t = new _timer_js__WEBPACK_IMPORTED_MODULE_0__.Timer;\n    delay = delay == null ? 0 : +delay;\n    t.restart((elapsed)=>{\n        t.stop();\n        callback(elapsed + delay);\n    }, delay, time);\n    return t;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtdGltZXIvc3JjL3RpbWVvdXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUM7QUFFakMsNkJBQWUsb0NBQVNDLFFBQVEsRUFBRUMsS0FBSyxFQUFFQyxJQUFJO0lBQzNDLElBQUlDLElBQUksSUFBSUosNENBQUtBO0lBQ2pCRSxRQUFRQSxTQUFTLE9BQU8sSUFBSSxDQUFDQTtJQUM3QkUsRUFBRUMsT0FBTyxDQUFDQyxDQUFBQTtRQUNSRixFQUFFRyxJQUFJO1FBQ05OLFNBQVNLLFVBQVVKO0lBQ3JCLEdBQUdBLE9BQU9DO0lBQ1YsT0FBT0M7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLXRpbWVyL3NyYy90aW1lb3V0LmpzP2JkMTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtUaW1lcn0gZnJvbSBcIi4vdGltZXIuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oY2FsbGJhY2ssIGRlbGF5LCB0aW1lKSB7XG4gIHZhciB0ID0gbmV3IFRpbWVyO1xuICBkZWxheSA9IGRlbGF5ID09IG51bGwgPyAwIDogK2RlbGF5O1xuICB0LnJlc3RhcnQoZWxhcHNlZCA9PiB7XG4gICAgdC5zdG9wKCk7XG4gICAgY2FsbGJhY2soZWxhcHNlZCArIGRlbGF5KTtcbiAgfSwgZGVsYXksIHRpbWUpO1xuICByZXR1cm4gdDtcbn1cbiJdLCJuYW1lcyI6WyJUaW1lciIsImNhbGxiYWNrIiwiZGVsYXkiLCJ0aW1lIiwidCIsInJlc3RhcnQiLCJlbGFwc2VkIiwic3RvcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-timer/src/timeout.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-timer/src/timer.js":
/*!********************************************!*\
  !*** ./node_modules/d3-timer/src/timer.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Timer: () => (/* binding */ Timer),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   timer: () => (/* binding */ timer),\n/* harmony export */   timerFlush: () => (/* binding */ timerFlush)\n/* harmony export */ });\nvar frame = 0, timeout = 0, interval = 0, pokeDelay = 1000, taskHead, taskTail, clockLast = 0, clockNow = 0, clockSkew = 0, clock = typeof performance === \"object\" && performance.now ? performance : Date, setFrame =  false ? 0 : function(f) {\n    setTimeout(f, 17);\n};\nfunction now() {\n    return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\nfunction clearNow() {\n    clockNow = 0;\n}\nfunction Timer() {\n    this._call = this._time = this._next = null;\n}\nTimer.prototype = timer.prototype = {\n    constructor: Timer,\n    restart: function(callback, delay, time) {\n        if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n        time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n        if (!this._next && taskTail !== this) {\n            if (taskTail) taskTail._next = this;\n            else taskHead = this;\n            taskTail = this;\n        }\n        this._call = callback;\n        this._time = time;\n        sleep();\n    },\n    stop: function() {\n        if (this._call) {\n            this._call = null;\n            this._time = Infinity;\n            sleep();\n        }\n    }\n};\nfunction timer(callback, delay, time) {\n    var t = new Timer;\n    t.restart(callback, delay, time);\n    return t;\n}\nfunction timerFlush() {\n    now(); // Get the current time, if not already set.\n    ++frame; // Pretend we’ve set an alarm, if we haven’t already.\n    var t = taskHead, e;\n    while(t){\n        if ((e = clockNow - t._time) >= 0) t._call.call(undefined, e);\n        t = t._next;\n    }\n    --frame;\n}\nfunction wake() {\n    clockNow = (clockLast = clock.now()) + clockSkew;\n    frame = timeout = 0;\n    try {\n        timerFlush();\n    } finally{\n        frame = 0;\n        nap();\n        clockNow = 0;\n    }\n}\nfunction poke() {\n    var now = clock.now(), delay = now - clockLast;\n    if (delay > pokeDelay) clockSkew -= delay, clockLast = now;\n}\nfunction nap() {\n    var t0, t1 = taskHead, t2, time = Infinity;\n    while(t1){\n        if (t1._call) {\n            if (time > t1._time) time = t1._time;\n            t0 = t1, t1 = t1._next;\n        } else {\n            t2 = t1._next, t1._next = null;\n            t1 = t0 ? t0._next = t2 : taskHead = t2;\n        }\n    }\n    taskTail = t0;\n    sleep(time);\n}\nfunction sleep(time) {\n    if (frame) return; // Soonest alarm already set, or will be.\n    if (timeout) timeout = clearTimeout(timeout);\n    var delay = time - clockNow; // Strictly less than if we recomputed clockNow.\n    if (delay > 24) {\n        if (time < Infinity) timeout = setTimeout(wake, time - clock.now() - clockSkew);\n        if (interval) interval = clearInterval(interval);\n    } else {\n        if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n        frame = 1, setFrame(wake);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-timer/src/timer.js\n");

/***/ })

};
;