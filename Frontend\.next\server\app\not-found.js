/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/breakpoints.module.css":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/breakpoints.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"breakpoint-xs\": \"0\",\n\t\"breakpoint-sm-195\": \"195px\",\n\t\"breakpoint-sm-270\": \"270px\",\n\t\"breakpoint-sm-200\": \"200px\",\n\t\"breakpoint-sm-320\": \"320px\",\n\t\"breakpoint-sm-326\": \"326px\",\n\t\"breakpoint-sm-390\": \"390px\",\n\t\"breakpoint-sm-367\": \"367px\",\n\t\"breakpoint-sm-365\": \"365px\",\n\t\"breakpoint-sm-340\": \"340px\",\n\t\"breakpoint-sm-350\": \"350px\",\n\t\"breakpoint-sm-370\": \"370px\",\n\t\"breakpoint-sm-380\": \"380px\",\n\t\"breakpoint-sm-424\": \"424px\",\n\t\"breakpoint-sm-427\": \"427px\",\n\t\"breakpoint-sm-420\": \"420px\",\n\t\"breakpoint-sm-430\": \"430px\",\n\t\"breakpoint-sm-450\": \"450px\",\n\t\"breakpoint-sm-460\": \"460px\",\n\t\"breakpoint-sm-484\": \"484px\",\n\t\"breakpoint-sm-480\": \"480px\",\n\t\"breakpoint-sm-532\": \"532px\",\n\t\"breakpoint-sm-550\": \"550px\",\n\t\"breakpoint-sm\": \"576px\",\n\t\"breakpoint-md-579\": \"579px\",\n\t\"breakpoint-md-585\": \"585px\",\n\t\"breakpoint-md-767\": \"767px\",\n\t\"breakpoint-md\": \"768px\",\n\t\"breakpoint-md-769\": \"769px\",\n\t\"breakpoint-md-820\": \"820px\",\n\t\"breakpoint-md-850\": \"850px\",\n\t\"breakpoint-lg-901\": \"901px\",\n\t\"breakpoint-lg\": \"992px\",\n\t\"breakpoint-lg-991px\": \"991px\",\n\t\"breakpoint-xl-1024\": \"1024px\",\n\t\"breakpoint-xl-1051\": \"1051px\",\n\t\"breakpoint-xl-1208\": \"1208px\",\n\t\"breakpoint-xl-1023\": \"1023px\",\n\t\"breakpoint-xl-1199\": \"1199px\",\n\t\"breakpoint-xl-1188\": \"1188px\",\n\t\"breakpoint-xl\": \"1200px\",\n\t\"breakpoint-xl-1365\": \"1365px\",\n\t\"breakpoint-xl-1366\": \"1366px\",\n\t\"breakpoint-xl-1309\": \"1309px\",\n\t\"breakpoint-xl-1400\": \"1400px\",\n\t\"breakpoint-xl-1439\": \"1439px\",\n\t\"breakpoint-xl-1440\": \"1440px\",\n\t\"breakpoint-xl-1405\": \"1405px\",\n\t\"breakpoint-xl-1406\": \"1406px\",\n\t\"breakpoint-xl-1600\": \"1600px\",\n\t\"breakpoint-xl-1800\": \"1800px\",\n\t\"breakpoint-xl-2000\": \"2000px\",\n\t\"breakpoint-xl-2100\": \"2100px\",\n\t\"breakpoint-xl-2442\": \"2442px\",\n\t\"breakpoint-xl-2559\": \"2559px\",\n\t\"breakpoint-xl-2560\": \"2560px\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/breakpoints.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/variables.module.css":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/variables.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"brandColorOne\": \"#FEBE10\",\n\t\"brandColorTwo\": \"#F47A37\",\n\t\"brandColorThree\": \"#F05443\",\n\t\"brandColorFour\": \"#D91A5F\",\n\t\"brandColorFive\": \"#B41F5E\",\n\t\"colorBlack\": \"#000000\",\n\t\"colorWhite\": \"#FFFFFF\",\n\t\"gray\": \"#202020\",\n\t\"gray100\": \"#FCFCFC\",\n\t\"gray200\": \"#F8F8F8\",\n\t\"gray300\": \"#F3F3F3\",\n\t\"gray400\": \"#E4E4E4\",\n\t\"gray500\": \"#CDCDCD\",\n\t\"gray600\": \"#B1B1B1\",\n\t\"gray700\": \"#808080\",\n\t\"gray800\": \"#646464\",\n\t\"gray900\": \"#3A3A3A\",\n\t\"error\": \"#FF6D60\",\n\t\"success\": \"#23A881\",\n\t\"grayBorder\": \"#8C8B8B\",\n\t\"link\": \"#0075FF\",\n\t\"grayBlueFonts\": \"#262531\",\n\t\"grayFonts\": \"#C3C3C3\",\n\t\"grayBg\": \"#F5F5F5\",\n\t\"halfSpace\": \"4px\",\n\t\"oneSpace\": \"8px\",\n\t\"twoSpace\": \"16px\",\n\t\"threeSpace\": \"24px\",\n\t\"fourSpace\": \"32px\",\n\t\"fiveSpace\": \"40px\",\n\t\"sixSpace\": \"48px\",\n\t\"eightSpace\": \"64px\",\n\t\"tenSpace\": \"80px\",\n\t\"fifteenSpace\": \"120px\",\n\t\"twentyFiveSpace\": \"200px\",\n\t\"h1FontSize\": \"78px\",\n\t\"h1MobileFontSize\": \"48px\",\n\t\"h2FontSize\": \"64px\",\n\t\"h2MobileFontSize\": \"44px\",\n\t\"h3FontSize\": \"52px\",\n\t\"h3MobileFontSize\": \"40px\",\n\t\"h4FontSize\": \"40px\",\n\t\"h4MobileFontSize\": \"28px\",\n\t\"h5FontSize\": \"32px\",\n\t\"h5MobileFontSize\": \"22px\",\n\t\"h6FontSize\": \"24px\",\n\t\"h6MobileFontSize\": \"18px\",\n\t\"bodyHeadingXL\": \"56px\",\n\t\"bodyHeadingL\": \"24px\",\n\t\"bodyHeadingM\": \"21px\",\n\t\"bodyHeadingS\": \"20px\",\n\t\"bodyHeadingXS\": \"18px\",\n\t\"bodyHeadingXSS\": \"16px\",\n\t\"buttonLabelXLargeFontSize\": \"26px\",\n\t\"buttonLabelLargeFontSize\": \"20px\",\n\t\"buttonLabelMediumFontSize\": \"16px\",\n\t\"buttonLabelSmallFontSize\": \"14px\",\n\t\"bodyTextXLarge\": \"26px\",\n\t\"bodyTextLarge\": \"22px\",\n\t\"bodyTextMedium\": \"20px\",\n\t\"bodyTextSmall\": \"18px\",\n\t\"bodyTextXSmall\": \"16px\",\n\t\"bodyTextXXSmall\": \"14px\",\n\t\"bodyTextXXXSSmall\": \"8px\",\n\t\"bodyLinkXXLarge\": \"26px\",\n\t\"bodyLinkXLarge\": \"22px\",\n\t\"bodyLinkLarge\": \"19px\",\n\t\"bodyLinkMedium\": \"18px\",\n\t\"bodyLinkSmall\": \"17px\",\n\t\"bodyLinkXSmall\": \"16px\",\n\t\"bodyLinkXXSmall\": \"15px\",\n\t\"fontWeight100\": \"100\",\n\t\"fontWeight200\": \"200\",\n\t\"fontWeight300\": \"300\",\n\t\"fontWeight400\": \"400\",\n\t\"fontWeight500\": \"500\",\n\t\"fontWeight600\": \"600\",\n\t\"fontWeight700\": \"700\",\n\t\"fontWeight800\": \"800\",\n\t\"fontWeight900\": \"900\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/variables.module.css\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-bailout-to-csr.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22100%22%2C%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-poppins%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cbootstrap%5Cdist%5Ccss%5Cbootstrap.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CBootstrapClient%5CBootstrapClient.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCircularButtonWithArrow%5CCircularButtonWithArrow.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCookiesConsentBanner%5CCookiesConsentBanner.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CFooter%5CFooter.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CHeader%5CHeader.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cbase.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-bailout-to-csr.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22100%22%2C%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-poppins%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cbootstrap%5Cdist%5Ccss%5Cbootstrap.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CBootstrapClient%5CBootstrapClient.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCircularButtonWithArrow%5CCircularButtonWithArrow.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCookiesConsentBanner%5CCookiesConsentBanner.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CFooter%5CFooter.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CHeader%5CHeader.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cbase.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js */ \"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/BootstrapClient/BootstrapClient.tsx */ \"(ssr)/./src/components/BootstrapClient/BootstrapClient.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CircularButtonWithArrow/CircularButtonWithArrow.tsx */ \"(ssr)/./src/components/CircularButtonWithArrow/CircularButtonWithArrow.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CookiesConsentBanner/CookiesConsentBanner.tsx */ \"(ssr)/./src/components/CookiesConsentBanner/CookiesConsentBanner.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer/Footer.tsx */ \"(ssr)/./src/components/Footer/Footer.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header/Header.tsx */ \"(ssr)/./src/components/Header/Header.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-bailout-to-csr.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22100%22%2C%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-poppins%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cbootstrap%5Cdist%5Ccss%5Cbootstrap.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CBootstrapClient%5CBootstrapClient.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCircularButtonWithArrow%5CCircularButtonWithArrow.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCookiesConsentBanner%5CCookiesConsentBanner.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CFooter%5CFooter.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CHeader%5CHeader.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cbase.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Capp%5Cnot-found.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Capp%5Cnot-found.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(ssr)/./src/app/not-found.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDS3J1bmFsJTIwRHViZXklNUNEb2N1bWVudHMlNUNHaXRIdWIlNUNtdGwtbmV4dGpzLWF3cy1zaXRlJTVDRnJvbnRlbmQlNUNzcmMlNUNhcHAlNUNub3QtZm91bmQudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLz84Mjc0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcS3J1bmFsIER1YmV5XFxcXERvY3VtZW50c1xcXFxHaXRIdWJcXFxcbXRsLW5leHRqcy1hd3Mtc2l0ZVxcXFxGcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXG5vdC1mb3VuZC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Capp%5Cnot-found.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @components/Button */ \"(ssr)/./src/components/Button/index.ts\");\n/* harmony import */ var _not_found_module_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./not-found.module.css */ \"(ssr)/./src/app/not-found.module.css\");\n/* harmony import */ var _not_found_module_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_not_found_module_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Container_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Container!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Container.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction NotFound() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        fluid: true,\n        className: (_not_found_module_css__WEBPACK_IMPORTED_MODULE_6___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                src: `${\"https://cdn.marutitech.com\"}/cuate_1745166738.svg`,\n                className: (_not_found_module_css__WEBPACK_IMPORTED_MODULE_6___default().image),\n                alt: \"404\",\n                width: 666,\n                height: 666\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: (_not_found_module_css__WEBPACK_IMPORTED_MODULE_6___default().button),\n                type: \"button\",\n                onClick: ()=>{\n                    router.push(\"/\");\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_not_found_module_css__WEBPACK_IMPORTED_MODULE_6___default().backToHomeButton),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: `${\"https://cdn.marutitech.com\"}/arrow_left_1c4ee29f6b.svg`,\n                            width: 24,\n                            height: 24,\n                            alt: \"arrow\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Back To Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/BootstrapClient/BootstrapClient.tsx":
/*!************************************************************!*\
  !*** ./src/components/BootstrapClient/BootstrapClient.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction BootstrapClient() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // eslint-disable-next-line global-require\n        __webpack_require__(/*! bootstrap/dist/js/bootstrap.bundle.min.js */ \"(ssr)/./node_modules/bootstrap/dist/js/bootstrap.bundle.min.js\");\n    }, []);\n    return null;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BootstrapClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Cb290c3RyYXBDbGllbnQvQm9vdHN0cmFwQ2xpZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7NkRBRWtDO0FBRWxDLFNBQVNDO0lBQ1BELGdEQUFTQSxDQUFDO1FBQ1IsMENBQTBDO1FBQzFDRSxtQkFBT0EsQ0FBQztJQUNWLEdBQUcsRUFBRTtJQUVMLE9BQU87QUFDVDtBQUVBLGlFQUFlRCxlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9zcmMvY29tcG9uZW50cy9Cb290c3RyYXBDbGllbnQvQm9vdHN0cmFwQ2xpZW50LnRzeD8xZTQ3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmZ1bmN0aW9uIEJvb3RzdHJhcENsaWVudCgpIHtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGdsb2JhbC1yZXF1aXJlXHJcbiAgICByZXF1aXJlKCdib290c3RyYXAvZGlzdC9qcy9ib290c3RyYXAuYnVuZGxlLm1pbi5qcycpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgcmV0dXJuIG51bGw7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IEJvb3RzdHJhcENsaWVudDtcclxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsIkJvb3RzdHJhcENsaWVudCIsInJlcXVpcmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/BootstrapClient/BootstrapClient.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Button/Button.tsx":
/*!******************************************!*\
  !*** ./src/components/Button/Button.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _utils_classNames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @utils/classNames */ \"(ssr)/./src/utils/classNames.ts\");\n/* harmony import */ var _Button_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Button.module.css */ \"(ssr)/./src/components/Button/Button.module.css\");\n/* harmony import */ var _Button_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Button_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Button({ label = \"\", className = \"\", type = \"button\", isLink = false, leftIcon = null, rightIcon = null, href = \"\", children = null, isExternal = false, onClick = ()=>{}, dataID = null, onMouseDown = ()=>{}, onMouseUp = ()=>{}, onTouchStart = ()=>{}, onTouchEnd = ()=>{}, scrollToForm }) {\n    const inner = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_Button_module_css__WEBPACK_IMPORTED_MODULE_3___default().innerWrapper),\n        children: [\n            leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (_Button_module_css__WEBPACK_IMPORTED_MODULE_3___default().leftWrapper),\n                children: leftIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Button\\\\Button.tsx\",\n                lineNumber: 50,\n                columnNumber: 20\n            }, this),\n            label,\n            rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (_Button_module_css__WEBPACK_IMPORTED_MODULE_3___default().rightWrapper),\n                children: rightIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Button\\\\Button.tsx\",\n                lineNumber: 52,\n                columnNumber: 21\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Button\\\\Button.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n    const target = isExternal ? \"_blank\" : \"_self\";\n    const rel = isExternal ? \"noreferrer\" : null;\n    const handleButtonClick = (e)=>{\n        if (scrollToForm) {\n            scrollToForm();\n        }\n        if (onClick) {\n            onClick(e);\n        }\n    };\n    return !isLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        // eslint-disable-next-line react/button-has-type\n        type: type,\n        className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((_Button_module_css__WEBPACK_IMPORTED_MODULE_3___default().button), className),\n        \"data-id\": dataID,\n        onClick: (e)=>handleButtonClick(e),\n        onMouseDown: onMouseDown,\n        onMouseUp: onMouseUp,\n        onTouchStart: onTouchStart,\n        onTouchEnd: onTouchEnd,\n        children: [\n            inner,\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Button\\\\Button.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        href: href,\n        target: target,\n        rel: rel,\n        className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((_Button_module_css__WEBPACK_IMPORTED_MODULE_3___default().link), className),\n        \"data-id\": dataID,\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: inner\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Button\\\\Button.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Button\\\\Button.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Button/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Button/index.ts":
/*!****************************************!*\
  !*** ./src/components/Button/index.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _Button__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/Button/Button.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9CdXR0b24vaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBbUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL3NyYy9jb21wb25lbnRzL0J1dHRvbi9pbmRleC50cz8zYTIzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tICcuL0J1dHRvbic7XHJcbiJdLCJuYW1lcyI6WyJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Button/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/CircularButtonWithArrow/CircularButtonWithArrow.tsx":
/*!****************************************************************************!*\
  !*** ./src/components/CircularButtonWithArrow/CircularButtonWithArrow.tsx ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CircularButtonWithArrow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _CircularButtonWithArrow_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CircularButtonWithArrow.module.css */ \"(ssr)/./src/components/CircularButtonWithArrow/CircularButtonWithArrow.module.css\");\n/* harmony import */ var _CircularButtonWithArrow_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_CircularButtonWithArrow_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction CircularButtonWithArrow({ variant = \"large\", scroll_to = \"false\" }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // State to control visibility\n    if (scroll_to === true) {\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            const handleScroll = ()=>{\n                const scrollTop = window.scrollY;\n                if (scrollTop > 600) {\n                    setIsVisible(true);\n                } else {\n                    setIsVisible(false);\n                }\n            };\n            window.addEventListener(\"scroll\", handleScroll);\n            return ()=>{\n                window.removeEventListener(\"scroll\", handleScroll);\n            };\n        }, []);\n    }\n    const imageSizes = {\n        large: {\n            circle: {\n                url: `${\"https://cdn.marutitech.com\"}/circle_large_5d770e7963.svg`,\n                width: 98,\n                height: 98\n            },\n            arrow: {\n                url: `${\"https://cdn.marutitech.com\"}/arrow_large_4c0720e1c9.svg`,\n                width: 53,\n                height: 53\n            }\n        },\n        medium: {\n            circle: {\n                url: `${\"https://cdn.marutitech.com\"}/circle_medium_cc9c77e620.svg`,\n                width: 77,\n                height: 77\n            },\n            arrow: {\n                url: `${\"https://cdn.marutitech.com\"}/arrow_medium_2fd9472b7f.svg`,\n                width: 43,\n                height: 43\n            }\n        },\n        small: {\n            circle: {\n                url: `${\"https://cdn.marutitech.com\"}/circle_small_b122f67035.svg`,\n                width: 52,\n                height: 52\n            },\n            arrow: {\n                url: `${\"https://cdn.marutitech.com\"}/arrow_small_113c2e8618.svg`,\n                width: 30,\n                height: 30\n            }\n        },\n        scroll_to_top: {\n            circle: {\n                url: `${\"https://cdn.marutitech.com\"}/Ellipse_2_Stroke_c048e39778.svg`,\n                width: 60,\n                height: 60\n            },\n            arrow: {\n                url: `${\"https://cdn.marutitech.com\"}/Group_5_b251b9ec9d.svg`,\n                width: 35,\n                height: 34\n            }\n        }\n    };\n    const { circle, arrow } = imageSizes[variant];\n    // Function to handle scrolling to the top\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            scroll_to && isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_CircularButtonWithArrow_module_css__WEBPACK_IMPORTED_MODULE_3___default().container),\n                style: {\n                    width: circle.width,\n                    height: circle.height\n                },\n                onClick: scrollToTop,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_CircularButtonWithArrow_module_css__WEBPACK_IMPORTED_MODULE_3___default().circle),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: circle.url,\n                            alt: \"Circle\",\n                            width: circle.width,\n                            height: circle.height\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CircularButtonWithArrow\\\\CircularButtonWithArrow.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CircularButtonWithArrow\\\\CircularButtonWithArrow.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_CircularButtonWithArrow_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrow_scroll),\n                        style: {\n                            width: arrow.width,\n                            height: arrow.height\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: arrow.url,\n                            alt: \"Arrow\",\n                            width: arrow.width,\n                            height: arrow.height,\n                            className: (_CircularButtonWithArrow_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowImage)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CircularButtonWithArrow\\\\CircularButtonWithArrow.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CircularButtonWithArrow\\\\CircularButtonWithArrow.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CircularButtonWithArrow\\\\CircularButtonWithArrow.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this),\n            scroll_to === \"false\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_CircularButtonWithArrow_module_css__WEBPACK_IMPORTED_MODULE_3___default().container),\n                style: {\n                    width: circle.width,\n                    height: circle.height\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_CircularButtonWithArrow_module_css__WEBPACK_IMPORTED_MODULE_3___default().circle),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: circle.url,\n                            alt: \"Circle\",\n                            width: circle.width,\n                            height: circle.height\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CircularButtonWithArrow\\\\CircularButtonWithArrow.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CircularButtonWithArrow\\\\CircularButtonWithArrow.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_CircularButtonWithArrow_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrow),\n                        style: {\n                            width: arrow.width,\n                            height: arrow.height\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: arrow.url,\n                            alt: \"Arrow\",\n                            width: arrow.width,\n                            height: arrow.height,\n                            className: (_CircularButtonWithArrow_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowImage)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CircularButtonWithArrow\\\\CircularButtonWithArrow.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CircularButtonWithArrow\\\\CircularButtonWithArrow.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CircularButtonWithArrow\\\\CircularButtonWithArrow.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CircularButtonWithArrow/CircularButtonWithArrow.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CookiesConsentBanner/CookiesConsentBanner.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/CookiesConsentBanner/CookiesConsentBanner.tsx ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CookiesConsentBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_cookie_consent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-cookie-consent */ \"(ssr)/./node_modules/react-cookie-consent/dist/react-cookie-consent.esm.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CookiesConsentBanner() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_cookie_consent__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            debug: false,\n            style: {\n                padding: \"12px\",\n                background: \"#59595990\",\n                textAlign: \"center\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: \"8px\",\n                fontSize: \"12px\",\n                zIndex: \"1111\"\n            },\n            buttonStyle: {\n                background: \"#000000\",\n                color: \"white\",\n                borderRadius: \"6px\",\n                margin: \"0px\"\n            },\n            buttonText: \"Ok\",\n            enableDeclineButton: true,\n            declineButtonText: \"\\xd7\",\n            declineButtonStyle: {\n                background: \"transparent\",\n                alignSelf: \"anchor-center\",\n                color: \"#ffffff\",\n                fontSize: \"22px\",\n                padding: \"0\",\n                margin: \"0\",\n                position: \"absolute\",\n                right: \"1rem\"\n            },\n            hideOnAccept: true,\n            children: [\n                \"We use cookies to improve your browsing experience. Learn about our\",\n                \" \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: \"/privacy-policy/\",\n                    children: \"Privacy policy\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CookiesConsentBanner\\\\CookiesConsentBanner.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                \".\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\CookiesConsentBanner\\\\CookiesConsentBanner.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Db29raWVzQ29uc2VudEJhbm5lci9Db29raWVzQ29uc2VudEJhbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWlEO0FBQ3BCO0FBRWQsU0FBU0U7SUFDdEIscUJBQ0U7a0JBQ0UsNEVBQUNGLDREQUFhQTtZQUNaRyxPQUFPO1lBQ1BDLE9BQU87Z0JBQ0xDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLFdBQVc7Z0JBQ1hDLFNBQVM7Z0JBQ1RDLGdCQUFnQjtnQkFDaEJDLFlBQVk7Z0JBQ1pDLEtBQUs7Z0JBQ0xDLFVBQVU7Z0JBQ1ZDLFFBQVE7WUFDVjtZQUNBQyxhQUFhO2dCQUNYUixZQUFZO2dCQUNaUyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxRQUFRO1lBQ1Y7WUFDQUMsWUFBVztZQUNYQyxtQkFBbUI7WUFDbkJDLG1CQUFrQjtZQUNsQkMsb0JBQW9CO2dCQUNsQmYsWUFBWTtnQkFDWmdCLFdBQVc7Z0JBQ1hQLE9BQU87Z0JBQ1BILFVBQVU7Z0JBQ1ZQLFNBQVM7Z0JBQ1RZLFFBQVE7Z0JBQ1JNLFVBQVU7Z0JBQ1ZDLE9BQU87WUFDVDtZQUNBQyxjQUFjOztnQkFDZjtnQkFDcUU7OEJBQ3BFLDhEQUFDeEIsaURBQUlBO29CQUFDeUIsTUFBSzs4QkFBbUI7Ozs7OztnQkFBcUI7Ozs7Ozs7O0FBSTNEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9zcmMvY29tcG9uZW50cy9Db29raWVzQ29uc2VudEJhbm5lci9Db29raWVzQ29uc2VudEJhbm5lci50c3g/YjhhMSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgQ29va2llQ29uc2VudCBmcm9tICdyZWFjdC1jb29raWUtY29uc2VudCc7XHJcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDb29raWVzQ29uc2VudEJhbm5lcigpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgPENvb2tpZUNvbnNlbnRcclxuICAgICAgICBkZWJ1Zz17ZmFsc2V9XHJcbiAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgIHBhZGRpbmc6ICcxMnB4JyxcclxuICAgICAgICAgIGJhY2tncm91bmQ6ICcjNTk1OTU5OTAnLFxyXG4gICAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJyxcclxuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcclxuICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcclxuICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxyXG4gICAgICAgICAgZ2FwOiAnOHB4JyxcclxuICAgICAgICAgIGZvbnRTaXplOiAnMTJweCcsXHJcbiAgICAgICAgICB6SW5kZXg6ICcxMTExJyxcclxuICAgICAgICB9fVxyXG4gICAgICAgIGJ1dHRvblN0eWxlPXt7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzAwMDAwMCcsXHJcbiAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcclxuICAgICAgICAgIGJvcmRlclJhZGl1czogJzZweCcsXHJcbiAgICAgICAgICBtYXJnaW46ICcwcHgnLFxyXG4gICAgICAgIH19XHJcbiAgICAgICAgYnV0dG9uVGV4dD1cIk9rXCJcclxuICAgICAgICBlbmFibGVEZWNsaW5lQnV0dG9uXHJcbiAgICAgICAgZGVjbGluZUJ1dHRvblRleHQ9XCLDl1wiXHJcbiAgICAgICAgZGVjbGluZUJ1dHRvblN0eWxlPXt7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAndHJhbnNwYXJlbnQnLFxyXG4gICAgICAgICAgYWxpZ25TZWxmOiAnYW5jaG9yLWNlbnRlcicsXHJcbiAgICAgICAgICBjb2xvcjogJyNmZmZmZmYnLFxyXG4gICAgICAgICAgZm9udFNpemU6ICcyMnB4JyxcclxuICAgICAgICAgIHBhZGRpbmc6ICcwJyxcclxuICAgICAgICAgIG1hcmdpbjogJzAnLFxyXG4gICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXHJcbiAgICAgICAgICByaWdodDogJzFyZW0nLFxyXG4gICAgICAgIH19XHJcbiAgICAgICAgaGlkZU9uQWNjZXB0PXt0cnVlfVxyXG4gICAgICA+XHJcbiAgICAgICAgV2UgdXNlIGNvb2tpZXMgdG8gaW1wcm92ZSB5b3VyIGJyb3dzaW5nIGV4cGVyaWVuY2UuIExlYXJuIGFib3V0IG91cnsnICd9XHJcbiAgICAgICAgPExpbmsgaHJlZj1cIi9wcml2YWN5LXBvbGljeS9cIj5Qcml2YWN5IHBvbGljeTwvTGluaz4uXHJcbiAgICAgIDwvQ29va2llQ29uc2VudD5cclxuICAgIDwvPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIkNvb2tpZUNvbnNlbnQiLCJMaW5rIiwiQ29va2llc0NvbnNlbnRCYW5uZXIiLCJkZWJ1ZyIsInN0eWxlIiwicGFkZGluZyIsImJhY2tncm91bmQiLCJ0ZXh0QWxpZ24iLCJkaXNwbGF5IiwianVzdGlmeUNvbnRlbnQiLCJhbGlnbkl0ZW1zIiwiZ2FwIiwiZm9udFNpemUiLCJ6SW5kZXgiLCJidXR0b25TdHlsZSIsImNvbG9yIiwiYm9yZGVyUmFkaXVzIiwibWFyZ2luIiwiYnV0dG9uVGV4dCIsImVuYWJsZURlY2xpbmVCdXR0b24iLCJkZWNsaW5lQnV0dG9uVGV4dCIsImRlY2xpbmVCdXR0b25TdHlsZSIsImFsaWduU2VsZiIsInBvc2l0aW9uIiwicmlnaHQiLCJoaWRlT25BY2NlcHQiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CookiesConsentBanner/CookiesConsentBanner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Footer/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Container_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Container!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Container.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_HashLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @components/HashLink */ \"(ssr)/./src/components/HashLink/index.ts\");\n/* harmony import */ var _components_Heading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/Heading */ \"(ssr)/./src/components/Heading/index.ts\");\n/* harmony import */ var _Footer_module_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Footer.module.css */ \"(ssr)/./src/components/Footer/Footer.module.css\");\n/* harmony import */ var _Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_Footer_module_css__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Footer({ footerData }) {\n    const props = footerData.data.attributes;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        fluid: true,\n        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().main_container),\n        \"data-crawler-ignore\": true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().inner_container),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().first_second_row),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().firstrow),\n                            children: props?.sector_row?.map((data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().column),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HashLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: data?.link || \"/\",\n                                            className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().title_firstrow),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Heading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                headingType: \"h4\",\n                                                title: data?.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, data?.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().link_title),\n                                            children: data?.Sublinks.map((subsector)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().sublink_title),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HashLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        href: subsector?.link || \"/\",\n                                                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().sublink_title),\n                                                        children: subsector?.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                        lineNumber: 29,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, subsector?.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 28,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, data?.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().secondrow),\n                            children: props?.pages_row?.map((data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().column),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HashLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: data?.link || \"/\",\n                                            className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().title_firstrow),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Heading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                headingType: \"h4\",\n                                                title: data?.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, data?.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().link_title),\n                                            children: data?.Sublinks.map((subsector)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().sublink_title),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HashLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        href: subsector?.link || \"/\",\n                                                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().sublink_title),\n                                                        children: subsector?.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                        lineNumber: 54,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, subsector?.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, data?.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().thirdrow),\n                    children: props?.terms_and_condition_section.map((data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HashLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: data?.link || \"/\",\n                            className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().terms_and_condition_title),\n                            children: data.title\n                        }, data?.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().company_logo_section),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: props?.company_logo_section?.link || \"/\",\n                            className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().imageContainer),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: props?.company_logo_section?.image?.data?.attributes?.url,\n                                alt: \"Logo Image\",\n                                width: 175,\n                                height: 32\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().iconsContainer),\n                            children: props?.company_logo_section?.social_platforms.map((data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: data?.link || \"/\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: data?.image?.data?.attributes?.url,\n                                        alt: \"Icons\",\n                                        width: 40,\n                                        height: 40,\n                                        quality: 100,\n                                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().icon)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this)\n                                }, data.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_6___default().copyright),\n                    children: props?.company_logo_section?.Copyright\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Footer\\\\Footer.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/HashLink/HashLink.tsx":
/*!**********************************************!*\
  !*** ./src/components/HashLink/HashLink.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HashLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _utils_classNames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @utils/classNames */ \"(ssr)/./src/utils/classNames.ts\");\n/* harmony import */ var _utils_hashNavigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @utils/hashNavigation */ \"(ssr)/./src/utils/hashNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n/**\r\n * Enhanced Link component that properly handles hash navigation\r\n * for static sites with consistent behavior between local and production\r\n */ function HashLink({ id, children, href = \"\", className, style, isExternal = false, onClick, scroll = true, shallow = false, ariaLabel, dataID = null, suppressHydrationWarning = false, useHashNavigation = true }) {\n    const handleClick = (event)=>{\n        // Call the original onClick handler if provided\n        if (onClick) {\n            onClick(event);\n        }\n        // Handle hash navigation for internal links\n        if (!isExternal && useHashNavigation && href.includes(\"#\")) {\n            event.preventDefault();\n            (0,_utils_hashNavigation__WEBPACK_IMPORTED_MODULE_4__.handleHashNavigation)(href, ()=>{\n            // Additional click handling can go here\n            });\n            return;\n        }\n    };\n    // External links\n    if (isExternal) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            suppressHydrationWarning: suppressHydrationWarning,\n            id: id,\n            className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(className),\n            href: href,\n            style: style,\n            target: \"_blank\",\n            rel: \"noreferrer\",\n            \"aria-label\": ariaLabel,\n            onClick: handleClick,\n            \"data-id\": dataID,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HashLink\\\\HashLink.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    // Internal links with hash navigation\n    if (useHashNavigation && href.includes(\"#\")) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            suppressHydrationWarning: suppressHydrationWarning,\n            id: id,\n            className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(className),\n            href: href,\n            style: style,\n            onClick: handleClick,\n            \"data-id\": dataID,\n            \"aria-label\": ariaLabel,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HashLink\\\\HashLink.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this);\n    }\n    // Regular internal links (use Next.js Link)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        href: href,\n        scroll: scroll,\n        shallow: shallow,\n        suppressHydrationWarning: suppressHydrationWarning,\n        id: id,\n        className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(className),\n        style: style,\n        onClick: handleClick,\n        \"data-id\": dataID,\n        \"aria-label\": ariaLabel,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HashLink\\\\HashLink.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HashLink/HashLink.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/HashLink/index.ts":
/*!******************************************!*\
  !*** ./src/components/HashLink/index.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _HashLink__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _HashLink__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./HashLink */ \"(ssr)/./src/components/HashLink/HashLink.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9IYXNoTGluay9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vc3JjL2NvbXBvbmVudHMvSGFzaExpbmsvaW5kZXgudHM/ZGI1NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IH0gZnJvbSAnLi9IYXNoTGluayc7XHJcbiJdLCJuYW1lcyI6WyJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HashLink/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/HeaderMegaMenu/Links.tsx":
/*!*************************************************!*\
  !*** ./src/components/HeaderMegaMenu/Links.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Links)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Row.js\");\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Col.js\");\n/* harmony import */ var _components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/Header/Header.module.css */ \"(ssr)/./src/components/Header/Header.module.css\");\n/* harmony import */ var _components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_classNames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @utils/classNames */ \"(ssr)/./src/utils/classNames.ts\");\n/* harmony import */ var _MenuLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MenuLink */ \"(ssr)/./src/components/HeaderMegaMenu/MenuLink.tsx\");\n\n\n\n\n\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction Links({ links, onClick, variant = \"default\" }) {\n    const forCareers = variant === \"careers\" ? true : \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4___default().megaMenuContent),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuWrapper),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        children: links?.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\"col-sm-12 col-md-6 col-lg-3 col-xl-3\", (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4___default().flexDirectionColumn)),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MenuLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    linkTitle: link?.title,\n                                    href: link?.link,\n                                    onClick: onClick,\n                                    forCareers: forCareers\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\Links.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            }, link?.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\Links.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\Links.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\Links.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\Links.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4___default().bottom_border)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\Links.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HeaderMegaMenu/Links.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/HeaderMegaMenu/LinksWithBottomRightButton.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/HeaderMegaMenu/LinksWithBottomRightButton.tsx ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LinksWithBottomRightButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Row.js\");\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Col.js\");\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @components/Button */ \"(ssr)/./src/components/Button/index.ts\");\n/* harmony import */ var _components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @components/Header/Header.module.css */ \"(ssr)/./src/components/Header/Header.module.css\");\n/* harmony import */ var _components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_Icons_DownArrowIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @components/Icons/DownArrowIcon */ \"(ssr)/./src/components/Icons/DownArrowIcon.tsx\");\n/* harmony import */ var _hooks_useMediaQueryState__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hooks/useMediaQueryState */ \"(ssr)/./src/hooks/useMediaQueryState.ts\");\n/* harmony import */ var _utils_classNames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @utils/classNames */ \"(ssr)/./src/utils/classNames.ts\");\n/* harmony import */ var _styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @styles/breakpoints.module.css */ \"(ssr)/./src/styles/breakpoints.module.css\");\n/* harmony import */ var _styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _MenuLink__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MenuLink */ \"(ssr)/./src/components/HeaderMegaMenu/MenuLink.tsx\");\n\n\n\n\n\n\n\n\n\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction LinksWithBottomRightButton({ links, button, onClick }) {\n    const isTablet = (0,_hooks_useMediaQueryState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        query: `(max-width: ${(_styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_7___default()[\"breakpoint-lg\"])})`\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_8___default().megaMenuContent),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_8___default().menuWrapper),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            children: links?.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"col-sm-12 col-md-3 col-lg-3 col-xl-3\", (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_8___default().flexDirectionColumn)),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MenuLink__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        linkTitle: link?.title,\n                                        href: link?.link,\n                                        onClick: onClick\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 17\n                                    }, this)\n                                }, link?.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: isTablet && (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_8___default().hide),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"col-md-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"col-md-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"col-md-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"col-md-3\",\n                                    children: button && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_8___default().brandsButton),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            type: \"button\",\n                                            label: button?.title,\n                                            rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons_DownArrowIcon__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 32\n                                            }, void 0),\n                                            onClick: onClick\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_8___default().bottom_border)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithBottomRightButton.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9IZWFkZXJNZWdhTWVudS9MaW5rc1dpdGhCb3R0b21SaWdodEJ1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMEI7QUFDaUI7QUFDSDtBQUNrQjtBQUNFO0FBQ0Q7QUFDaEI7QUFDYztBQUV2QjtBQUVsQyw4REFBOEQ7QUFDL0MsU0FBU1UsMkJBQTJCLEVBQ2pEQyxLQUFLLEVBQ0xDLE1BQU0sRUFDTkMsT0FBTyxFQUNIO0lBQ0osTUFBTUMsV0FBV1IscUVBQWtCQSxDQUFDO1FBQ2xDUyxPQUFPLENBQUMsWUFBWSxFQUFFUCx3RkFBNEIsQ0FBQyxDQUFDLENBQUM7SUFDdkQ7SUFDQSxxQkFDRTs7MEJBQ0UsOERBQUNRO2dCQUFJQyxXQUFXYiw2RkFBc0I7MEJBQ3BDLDRFQUFDWTtvQkFBSUMsV0FBV2IseUZBQWtCOztzQ0FDaEMsOERBQUNGLHNGQUFHQTtzQ0FDRFMsT0FBT1MsSUFBSUMsQ0FBQUEscUJBQ1YsOERBQUNwQix1RkFBR0E7b0NBQ0ZnQixXQUFXViw2REFBVUEsQ0FDbkIsd0NBQ0FILGlHQUEwQjs4Q0FJNUIsNEVBQUNLLGlEQUFRQTt3Q0FDUGMsV0FBV0YsTUFBTUc7d0NBQ2pCQyxNQUFNSixNQUFNQTt3Q0FDWlIsU0FBU0E7Ozs7OzttQ0FMTlEsTUFBTUs7Ozs7Ozs7Ozs7c0NBVWpCLDhEQUFDeEIsc0ZBQUdBOzRCQUFDZSxXQUFXSCxZQUFZVixrRkFBVzs7OENBQ3JDLDhEQUFDSCx1RkFBR0E7b0NBQUNnQixXQUFVOzs7Ozs7OENBQ2YsOERBQUNoQix1RkFBR0E7b0NBQUNnQixXQUFVOzs7Ozs7OENBQ2YsOERBQUNoQix1RkFBR0E7b0NBQUNnQixXQUFVOzs7Ozs7OENBQ2YsOERBQUNoQix1RkFBR0E7b0NBQUNnQixXQUFVOzhDQUNaTCx3QkFDQyw4REFBQ0k7d0NBQUlDLFdBQVdiLDBGQUFtQjtrREFDakMsNEVBQUNELDBEQUFNQTs0Q0FDTDBCLE1BQUs7NENBQ0xDLE9BQU9sQixRQUFRWTs0Q0FDZk8seUJBQVcsOERBQUMxQix1RUFBYUE7Ozs7OzRDQUN6QlEsU0FBU0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRdkIsOERBQUNHO2dCQUFJQyxXQUFXYiwyRkFBb0I7Ozs7Ozs7O0FBRzFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9zcmMvY29tcG9uZW50cy9IZWFkZXJNZWdhTWVudS9MaW5rc1dpdGhCb3R0b21SaWdodEJ1dHRvbi50c3g/Y2YyZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBDb2wsIFJvdyB9IGZyb20gJ3JlYWN0LWJvb3RzdHJhcCc7XHJcbmltcG9ydCBCdXR0b24gZnJvbSAnQGNvbXBvbmVudHMvQnV0dG9uJztcclxuaW1wb3J0IHN0eWxlcyBmcm9tICdAY29tcG9uZW50cy9IZWFkZXIvSGVhZGVyLm1vZHVsZS5jc3MnO1xyXG5pbXBvcnQgRG93bkFycm93SWNvbiBmcm9tICdAY29tcG9uZW50cy9JY29ucy9Eb3duQXJyb3dJY29uJztcclxuaW1wb3J0IHVzZU1lZGlhUXVlcnlTdGF0ZSBmcm9tICdAaG9va3MvdXNlTWVkaWFRdWVyeVN0YXRlJztcclxuaW1wb3J0IGNsYXNzTmFtZXMgZnJvbSAnQHV0aWxzL2NsYXNzTmFtZXMnO1xyXG5pbXBvcnQgYnJlYWtwb2ludHMgZnJvbSAnQHN0eWxlcy9icmVha3BvaW50cy5tb2R1bGUuY3NzJztcclxuXHJcbmltcG9ydCBNZW51TGluayBmcm9tICcuL01lbnVMaW5rJztcclxuXHJcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55XHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExpbmtzV2l0aEJvdHRvbVJpZ2h0QnV0dG9uKHtcclxuICBsaW5rcyxcclxuICBidXR0b24sXHJcbiAgb25DbGljayxcclxufTogYW55KSB7XHJcbiAgY29uc3QgaXNUYWJsZXQgPSB1c2VNZWRpYVF1ZXJ5U3RhdGUoe1xyXG4gICAgcXVlcnk6IGAobWF4LXdpZHRoOiAke2JyZWFrcG9pbnRzWydicmVha3BvaW50LWxnJ119KWAsXHJcbiAgfSk7XHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubWVnYU1lbnVDb250ZW50fT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm1lbnVXcmFwcGVyfT5cclxuICAgICAgICAgIDxSb3c+XHJcbiAgICAgICAgICAgIHtsaW5rcz8ubWFwKGxpbmsgPT4gKFxyXG4gICAgICAgICAgICAgIDxDb2xcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y2xhc3NOYW1lcyhcclxuICAgICAgICAgICAgICAgICAgJ2NvbC1zbS0xMiBjb2wtbWQtMyBjb2wtbGctMyBjb2wteGwtMycsXHJcbiAgICAgICAgICAgICAgICAgIHN0eWxlcy5mbGV4RGlyZWN0aW9uQ29sdW1uLFxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIGtleT17bGluaz8uaWR9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPE1lbnVMaW5rXHJcbiAgICAgICAgICAgICAgICAgIGxpbmtUaXRsZT17bGluaz8udGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgIGhyZWY9e2xpbms/Lmxpbmt9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xpY2t9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvQ29sPlxyXG4gICAgICAgICAgICApKX1cclxuICAgICAgICAgIDwvUm93PlxyXG4gICAgICAgICAgPFJvdyBjbGFzc05hbWU9e2lzVGFibGV0ICYmIHN0eWxlcy5oaWRlfT5cclxuICAgICAgICAgICAgPENvbCBjbGFzc05hbWU9XCJjb2wtbWQtM1wiIC8+XHJcbiAgICAgICAgICAgIDxDb2wgY2xhc3NOYW1lPVwiY29sLW1kLTNcIiAvPlxyXG4gICAgICAgICAgICA8Q29sIGNsYXNzTmFtZT1cImNvbC1tZC0zXCIgLz5cclxuICAgICAgICAgICAgPENvbCBjbGFzc05hbWU9XCJjb2wtbWQtM1wiPlxyXG4gICAgICAgICAgICAgIHtidXR0b24gJiYgKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5icmFuZHNCdXR0b259PlxyXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9e2J1dHRvbj8udGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgICAgcmlnaHRJY29uPXs8RG93bkFycm93SWNvbiAvPn1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtvbkNsaWNrfVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9Db2w+XHJcbiAgICAgICAgICA8L1Jvdz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuYm90dG9tX2JvcmRlcn0+PC9kaXY+XHJcbiAgICA8Lz5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNvbCIsIlJvdyIsIkJ1dHRvbiIsInN0eWxlcyIsIkRvd25BcnJvd0ljb24iLCJ1c2VNZWRpYVF1ZXJ5U3RhdGUiLCJjbGFzc05hbWVzIiwiYnJlYWtwb2ludHMiLCJNZW51TGluayIsIkxpbmtzV2l0aEJvdHRvbVJpZ2h0QnV0dG9uIiwibGlua3MiLCJidXR0b24iLCJvbkNsaWNrIiwiaXNUYWJsZXQiLCJxdWVyeSIsImRpdiIsImNsYXNzTmFtZSIsIm1lZ2FNZW51Q29udGVudCIsIm1lbnVXcmFwcGVyIiwibWFwIiwibGluayIsImZsZXhEaXJlY3Rpb25Db2x1bW4iLCJsaW5rVGl0bGUiLCJ0aXRsZSIsImhyZWYiLCJpZCIsImhpZGUiLCJicmFuZHNCdXR0b24iLCJ0eXBlIiwibGFiZWwiLCJyaWdodEljb24iLCJib3R0b21fYm9yZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HeaderMegaMenu/LinksWithBottomRightButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/HeaderMegaMenu/LinksWithLatestBlogLink.tsx":
/*!*******************************************************************!*\
  !*** ./src/components/HeaderMegaMenu/LinksWithLatestBlogLink.tsx ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LinksWithLatestBlogLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Row.js\");\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Col.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _public_arrow_right_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @public/arrow-right.svg */ \"(ssr)/./src/public/arrow-right.svg\");\n/* harmony import */ var _components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @components/Header/Header.module.css */ \"(ssr)/./src/components/Header/Header.module.css\");\n/* harmony import */ var _components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Link_Link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/Link/Link */ \"(ssr)/./src/components/Link/Link.tsx\");\n/* harmony import */ var _utils_classNames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @utils/classNames */ \"(ssr)/./src/utils/classNames.ts\");\n/* harmony import */ var _MenuLink__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MenuLink */ \"(ssr)/./src/components/HeaderMegaMenu/MenuLink.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/* eslint-disable react/no-danger */ \n\n\n\n\n\n\n\nfunction LinksWithLatestBlogLink({ links, button, titleDescription, onClick }) {\n    const [isRendered, setIsRendered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsRendered(true);\n    }, []);\n    if (!isRendered) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().megaMenuContent),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().menuWrapper),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: links?.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"col-sm-12 col-md-12 col-lg-4 col-xl-4\", (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().flexDirectionColumn)),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MenuLink__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                linkTitle: link?.title,\n                                                href: link.link,\n                                                onClick: onClick\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, link?.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"col-sm-12 col-md-12 col-lg-4 col-xl-3\", (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().flexDirectionColumn)),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().menuWrapper),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().latestBlogWrapper), (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().link)),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().blogHeading),\n                                            children: titleDescription?.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().linkTitle), (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().blogTitle)),\n                                            /* eslint-disable-next-line react/no-danger */ dangerouslySetInnerHTML: {\n                                                __html: titleDescription?.description\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this),\n                                        button?.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().blogCTALink),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Link_Link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                href: `${button?.link}`,\n                                                onClick: onClick,\n                                                className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().ctaLink),\n                                                children: [\n                                                    button?.title,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: _public_arrow_right_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                                            alt: \"right Arrow\",\n                                                            width: 24,\n                                                            height: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_7___default().bottom_border)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\LinksWithLatestBlogLink.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HeaderMegaMenu/LinksWithLatestBlogLink.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/HeaderMegaMenu/MenuLink.tsx":
/*!****************************************************!*\
  !*** ./src/components/HeaderMegaMenu/MenuLink.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MenuLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/Header/Header.module.css */ \"(ssr)/./src/components/Header/Header.module.css\");\n/* harmony import */ var _components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_HashLink__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @components/HashLink */ \"(ssr)/./src/components/HashLink/index.ts\");\n/* harmony import */ var _utils_classNames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @utils/classNames */ \"(ssr)/./src/utils/classNames.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction MenuLink({ linkTitle, href, onClick, fromSevices = false }) {\n    const [isRendered, setIsRendered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsRendered(true);\n    }, []);\n    if (!isRendered) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4___default().linkWrapper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HashLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(fromSevices === true ? (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4___default().link) : (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_4___default().linkTitle_others)),\n            href: href,\n            onClick: onClick,\n            children: linkTitle\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\MenuLink.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\MenuLink.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HeaderMegaMenu/MenuLink.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/HeaderMegaMenu/TitleWithLinks.tsx":
/*!**********************************************************!*\
  !*** ./src/components/HeaderMegaMenu/TitleWithLinks.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TitleWithLinks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Row.js\");\n/* harmony import */ var _barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Row!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Col.js\");\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @components/Button */ \"(ssr)/./src/components/Button/index.ts\");\n/* harmony import */ var _components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @components/Header/Header.module.css */ \"(ssr)/./src/components/Header/Header.module.css\");\n/* harmony import */ var _components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _components_Icons_RightArrowIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @components/Icons/RightArrowIcon */ \"(ssr)/./src/components/Icons/RightArrowIcon.tsx\");\n/* harmony import */ var _components_Link_Link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/Link/Link */ \"(ssr)/./src/components/Link/Link.tsx\");\n/* harmony import */ var _hooks_useMediaQueryState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @hooks/useMediaQueryState */ \"(ssr)/./src/hooks/useMediaQueryState.ts\");\n/* harmony import */ var _utils_classNames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @utils/classNames */ \"(ssr)/./src/utils/classNames.ts\");\n/* harmony import */ var _styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @styles/breakpoints.module.css */ \"(ssr)/./src/styles/breakpoints.module.css\");\n/* harmony import */ var _styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _MenuLink__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MenuLink */ \"(ssr)/./src/components/HeaderMegaMenu/MenuLink.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/* eslint-disable react/no-array-index-key */ \n\n\n\n\n\n\n\n\n\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction TitleWithLinks({ menuArray, button, onClick }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const isLargeScreen = (0,_hooks_useMediaQueryState__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n        query: `(min-width: ${(_styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_9___default()[\"breakpoint-xl-2442\"])})`\n    });\n    const [isRendered, setIsRendered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsRendered(true);\n    }, []);\n    if (!isRendered) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(isLargeScreen ? (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_10___default().largeDeviceSpacing) : (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_10___default().megaMenuContent)),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    children: [\n                        menuArray.map((menu)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(\"col-sm-12 col-md-6 col-lg-3 col-xl-3\", (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_10___default().flexDirectionColumn)),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_10___default().menuWrapper),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Link_Link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_10___default().linkTitle),\n                                                href: menu?.link,\n                                                onClick: onClick,\n                                                children: menu?.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 19\n                                            }, this),\n                                            menu?.sublinks?.map((sublink, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MenuLink__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    linkTitle: sublink?.title,\n                                                    href: sublink?.link,\n                                                    onClick: onClick,\n                                                    fromSevices: true\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 21\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this)\n                            }, menu?.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"col-md-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Row_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"pt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_10___default().menuWrapper), (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_10___default().all_service_button)),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        type: \"button\",\n                                        label: button?.title,\n                                        onClick: ()=>{\n                                            onClick();\n                                            router.push(`${button?.link}`);\n                                        },\n                                        rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons_RightArrowIcon__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 30\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_components_Header_Header_module_css__WEBPACK_IMPORTED_MODULE_10___default().bottom_border)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\HeaderMegaMenu\\\\TitleWithLinks.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HeaderMegaMenu/TitleWithLinks.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Header/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Navbar.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Nav.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @components/Button */ \"(ssr)/./src/components/Button/index.ts\");\n/* harmony import */ var _components_HeaderMegaMenu_Links__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/HeaderMegaMenu/Links */ \"(ssr)/./src/components/HeaderMegaMenu/Links.tsx\");\n/* harmony import */ var _components_HeaderMegaMenu_LinksWithBottomRightButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @components/HeaderMegaMenu/LinksWithBottomRightButton */ \"(ssr)/./src/components/HeaderMegaMenu/LinksWithBottomRightButton.tsx\");\n/* harmony import */ var _components_HeaderMegaMenu_LinksWithLatestBlogLink__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @components/HeaderMegaMenu/LinksWithLatestBlogLink */ \"(ssr)/./src/components/HeaderMegaMenu/LinksWithLatestBlogLink.tsx\");\n/* harmony import */ var _components_HeaderMegaMenu_TitleWithLinks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @components/HeaderMegaMenu/TitleWithLinks */ \"(ssr)/./src/components/HeaderMegaMenu/TitleWithLinks.tsx\");\n/* harmony import */ var _components_Icons_DropdownIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @components/Icons/DropdownIcon */ \"(ssr)/./src/components/Icons/DropdownIcon.tsx\");\n/* harmony import */ var _components_Icons_HamburgerMenuIcon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @components/Icons/HamburgerMenuIcon */ \"(ssr)/./src/components/Icons/HamburgerMenuIcon.tsx\");\n/* harmony import */ var _components_Icons_CrossIcon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @components/Icons/CrossIcon */ \"(ssr)/./src/components/Icons/CrossIcon.tsx\");\n/* harmony import */ var _hooks_useMediaQueryState__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hooks/useMediaQueryState */ \"(ssr)/./src/hooks/useMediaQueryState.ts\");\n/* harmony import */ var _utils_classNames__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @utils/classNames */ \"(ssr)/./src/utils/classNames.ts\");\n/* harmony import */ var _styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @styles/breakpoints.module.css */ \"(ssr)/./src/styles/breakpoints.module.css\");\n/* harmony import */ var _styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(_styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Header_module_css__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./Header.module.css */ \"(ssr)/./src/components/Header/Header.module.css\");\n/* harmony import */ var _Header_module_css__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(_Header_module_css__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Header({ headerData }) {\n    const [show, setShow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Navbar visibility toggle\n    const [lastScrollY, setLastScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track last scroll position\n    const [isNavbarCollapseMenuOpen, setIsNavbarCollapseMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeMegaMenu, setActiveMegaMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // Active mega menu\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_14__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Reset the collapse menu state when navigating to a new page\n        setIsNavbarCollapseMenuOpen(false);\n    }, []);\n    const controlNavbar = ()=>{\n        if (false) {}\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        lastScrollY,\n        controlNavbar\n    ]);\n    const handleCollapseClick = ()=>{\n        setIsNavbarCollapseMenuOpen(!isNavbarCollapseMenuOpen);\n    };\n    const closeMegaMenu = ()=>{\n        setActiveMegaMenu(null);\n    };\n    const { data: { attributes: { logo: { image: { data: { attributes: { url, height, width, alternativeText } } } }, menu } } } = headerData;\n    const isTablet = (0,_hooks_useMediaQueryState__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({\n        query: `(max-width: ${(_styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_15___default()[\"breakpoint-lg\"])})`\n    });\n    const isMobile = (0,_hooks_useMediaQueryState__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({\n        query: `(max-width: ${(_styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_15___default()[\"breakpoint-sm-427\"])})`\n    });\n    const renderMegaMenu = (menuTitle, menuLinks, button = undefined, titleDescription = undefined)=>{\n        const handleMegaMenuLinkClick = ()=>{\n            setActiveMegaMenu(null);\n            if (isTablet) {\n                setIsNavbarCollapseMenuOpen(false);\n            }\n        };\n        switch(menuTitle){\n            case \"Services\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeaderMegaMenu_TitleWithLinks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    menuArray: menuLinks,\n                    button: button,\n                    onClick: handleMegaMenuLinkClick\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this);\n            case \"ValueQuest\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeaderMegaMenu_LinksWithLatestBlogLink__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    links: menuLinks,\n                    button: button,\n                    titleDescription: titleDescription,\n                    onClick: handleMegaMenuLinkClick\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this);\n            case \"Industries\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeaderMegaMenu_Links__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    links: menuLinks,\n                    onClick: handleMegaMenuLinkClick\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 16\n                }, this);\n            case \"Resources\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeaderMegaMenu_LinksWithLatestBlogLink__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    links: menuLinks,\n                    button: button,\n                    titleDescription: titleDescription,\n                    onClick: handleMegaMenuLinkClick\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this);\n            case \"About Us\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeaderMegaMenu_LinksWithBottomRightButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    links: menuLinks,\n                    button: button,\n                    onClick: handleMegaMenuLinkClick\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this);\n            default:\n                return \"\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n        expand: \"lg\",\n        fixed: \"top\",\n        className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().navbarWrapper), show && (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().hidden), isNavbarCollapseMenuOpen && (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().hidden_override)),\n        \"data-crawler-ignore\": true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            fluid: true,\n            className: (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().applicationContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    href: \"/\",\n                    prefetch: false,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Brand, {\n                        className: (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().brandContainer),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: url,\n                            height: 28,\n                            width: 150,\n                            className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(\"d-inline-block align-center\", (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().mtech_logo)),\n                            alt: alternativeText,\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this),\n                isTablet && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"d-flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            type: \"button\",\n                            label: \"Get In Touch\",\n                            className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().button), isMobile && (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().hide)),\n                            onClick: ()=>{\n                                isNavbarCollapseMenuOpen && handleCollapseClick();\n                                router.push(\"/contact-us\");\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Toggle, {\n                            \"aria-controls\": \"basic-navbar-nav\",\n                            className: (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().navBarToggler),\n                            onClick: handleCollapseClick,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons_HamburgerMenuIcon__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: `${(_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().menuIcons)} ${isNavbarCollapseMenuOpen ? (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().clicked) : \"\"}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons_CrossIcon__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: `${(_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().menuIcons)} ${!isNavbarCollapseMenuOpen ? (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().clicked) : \"\"}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            href: \"/search\",\n                            \"aria-label\": \"search\",\n                            className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().search), (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().searchWrapper)),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: `${\"https://cdn.marutitech.com\"}/search_icon_3736bff546.svg`,\n                                width: 24,\n                                height: 25,\n                                alt: \"Search_icon\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, this),\n                isTablet ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"basic-navbar-nav\",\n                    className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().navbarCollapse)),\n                    style: {\n                        display: !isNavbarCollapseMenuOpen ? \"none\" : \"block\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        className: (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().nav),\n                        children: [\n                            menu?.map((navMenu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onMouseEnter: ()=>setActiveMegaMenu(navMenu?.title),\n                                        onMouseLeave: closeMegaMenu,\n                                        className: (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().navItem),\n                                        \"data-title\": navMenu?.title,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                href: navMenu?.link,\n                                                prefetch: false,\n                                                passHref: true,\n                                                legacyBehavior: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Link, {\n                                                    className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().navLink)),\n                                                    \"data-title\": navMenu?.title,\n                                                    children: [\n                                                        navMenu?.title,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().arrowIcon),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons_DropdownIcon__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeMegaMenu === navMenu?.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().megamenu), {\n                                                    [(_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().visible)]: activeMegaMenu === navMenu?.title\n                                                }),\n                                                \"data-title\": navMenu?.title,\n                                                children: renderMegaMenu(navMenu?.title, navMenu?.subMenu || navMenu?.subLinks, navMenu?.button, navMenu?.titleDescription)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                type: \"button\",\n                                label: \"Get In Touch\",\n                                className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().button), (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().visibility)),\n                                onClick: ()=>{\n                                    isNavbarCollapseMenuOpen && handleCollapseClick();\n                                    router.push(\"/contact-us\");\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                href: \"/search\",\n                                \"aria-label\": \"search\",\n                                className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().search), (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().searchWrapper), isTablet && (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().hide)),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: `${\"https://cdn.marutitech.com\"}/search_icon_3736bff546.svg`,\n                                    width: 24,\n                                    height: 25,\n                                    alt: \"Search_icon\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Collapse, {\n                    id: \"basic-navbar-nav\",\n                    className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().navbarCollapse)),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        className: (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().nav),\n                        children: [\n                            menu?.map((navMenu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onMouseEnter: ()=>setActiveMegaMenu(navMenu?.title),\n                                    onMouseLeave: closeMegaMenu,\n                                    className: (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().navItem),\n                                    \"data-title\": navMenu?.title,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            href: navMenu?.link,\n                                            prefetch: false,\n                                            passHref: true,\n                                            legacyBehavior: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Link, {\n                                                className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().navLink)),\n                                                \"data-title\": navMenu?.title,\n                                                children: [\n                                                    navMenu?.title,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().arrowIcon),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Icons_DropdownIcon__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 19\n                                        }, this),\n                                        activeMegaMenu === navMenu?.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().megamenu), {\n                                                [(_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().visible)]: activeMegaMenu === navMenu?.title\n                                            }),\n                                            \"data-title\": navMenu?.title,\n                                            children: renderMegaMenu(navMenu?.title, navMenu?.subMenu || navMenu?.subLinks, navMenu?.button, navMenu?.titleDescription)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                type: \"button\",\n                                label: \"Get In Touch\",\n                                className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().button), (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().visibility)),\n                                onClick: ()=>{\n                                    handleCollapseClick();\n                                    router.push(\"/contact-us\");\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                href: \"/search\",\n                                \"aria-label\": \"search\",\n                                className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().search), (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().searchWrapper), isTablet && (_Header_module_css__WEBPACK_IMPORTED_MODULE_17___default().hide)),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: `${\"https://cdn.marutitech.com\"}/search_icon_3736bff546.svg`,\n                                    width: 24,\n                                    height: 25,\n                                    alt: \"Search_icon\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Header\\\\Header.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Heading/Heading.tsx":
/*!********************************************!*\
  !*** ./src/components/Heading/Heading.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Heading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_classNames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @utils/classNames */ \"(ssr)/./src/utils/classNames.ts\");\n/* harmony import */ var _styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @styles/typography.module.css */ \"(ssr)/./src/styles/typography.module.css\");\n/* harmony import */ var _styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Heading_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Heading.module.css */ \"(ssr)/./src/components/Heading/Heading.module.css\");\n/* harmony import */ var _Heading_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_Heading_module_css__WEBPACK_IMPORTED_MODULE_4__);\n/* eslint-disable react/jsx-props-no-spreading */ /* eslint-disable react/no-danger-with-children */ \n\n\n\n\nfunction Heading({ headingType, title, position, style, className, richTextValue }) {\n    const renderHeading = (heading, label)=>{\n        switch(heading){\n            case \"h1\":\n                return richTextValue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h1),\n                    style: style,\n                    dangerouslySetInnerHTML: {\n                        __html: richTextValue\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h1),\n                    style: style,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this);\n            case \"h2\":\n                return richTextValue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h2),\n                    style: style,\n                    dangerouslySetInnerHTML: {\n                        __html: richTextValue\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h2),\n                    style: style,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this);\n            case \"h3\":\n                return richTextValue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h3),\n                    style: style,\n                    dangerouslySetInnerHTML: {\n                        __html: richTextValue\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h3),\n                    style: style,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 11\n                }, this);\n            case \"h4\":\n                return richTextValue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h4),\n                    style: style,\n                    dangerouslySetInnerHTML: {\n                        __html: richTextValue\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h4),\n                    style: style,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 11\n                }, this);\n            case \"h5\":\n                return richTextValue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h5),\n                    style: style,\n                    dangerouslySetInnerHTML: {\n                        __html: richTextValue\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h5),\n                    style: style,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this);\n            case \"h6\":\n                return richTextValue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h6),\n                    style: style,\n                    dangerouslySetInnerHTML: {\n                        __html: richTextValue\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                    className: (_styles_typography_module_css__WEBPACK_IMPORTED_MODULE_3___default().h6),\n                    style: style,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((_Heading_module_css__WEBPACK_IMPORTED_MODULE_4___default())[position], className),\n        children: renderHeading(headingType, title)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Heading\\\\Heading.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Heading/Heading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Heading/index.ts":
/*!*****************************************!*\
  !*** ./src/components/Heading/index.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _Heading__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Heading__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Heading */ \"(ssr)/./src/components/Heading/Heading.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9IZWFkaW5nL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9zcmMvY29tcG9uZW50cy9IZWFkaW5nL2luZGV4LnRzPzA4MzAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gJy4vSGVhZGluZyc7XHJcbiJdLCJuYW1lcyI6WyJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Heading/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/Icons/CrossIcon.tsx":
/*!********************************************!*\
  !*** ./src/components/Icons/CrossIcon.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CrossIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction CrossIcon({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"40\",\n        height: \"40\",\n        viewBox: \"0 0 40 40\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M12 12 L28 28 M28 12 L12 28\",\n            stroke: \"white\",\n            strokeWidth: \"3\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\CrossIcon.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\CrossIcon.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9JY29ucy9Dcm9zc0ljb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQjtBQUVYLFNBQVNDLFVBQVUsRUFBRUMsU0FBUyxFQUFFO0lBQzdDLHFCQUNFLDhEQUFDQztRQUNDQyxPQUFNO1FBQ05DLFFBQU87UUFDUEMsU0FBUTtRQUNSQyxNQUFLO1FBQ0xDLE9BQU07UUFDTk4sV0FBV0E7a0JBRVgsNEVBQUNPO1lBQ0NDLEdBQUU7WUFDRkMsUUFBTztZQUNQQyxhQUFZO1lBQ1pDLGVBQWM7WUFDZEMsZ0JBQWU7Ozs7Ozs7Ozs7O0FBSXZCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9zcmMvY29tcG9uZW50cy9JY29ucy9Dcm9zc0ljb24udHN4P2Q0ZDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENyb3NzSWNvbih7IGNsYXNzTmFtZSB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxzdmdcclxuICAgICAgd2lkdGg9XCI0MFwiXHJcbiAgICAgIGhlaWdodD1cIjQwXCJcclxuICAgICAgdmlld0JveD1cIjAgMCA0MCA0MFwiXHJcbiAgICAgIGZpbGw9XCJub25lXCJcclxuICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXHJcbiAgICAgIGNsYXNzTmFtZT17Y2xhc3NOYW1lfVxyXG4gICAgPlxyXG4gICAgICA8cGF0aFxyXG4gICAgICAgIGQ9XCJNMTIgMTIgTDI4IDI4IE0yOCAxMiBMMTIgMjhcIlxyXG4gICAgICAgIHN0cm9rZT1cIndoaXRlXCJcclxuICAgICAgICBzdHJva2VXaWR0aD1cIjNcIlxyXG4gICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXHJcbiAgICAgIC8+XHJcbiAgICA8L3N2Zz5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNyb3NzSWNvbiIsImNsYXNzTmFtZSIsInN2ZyIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsImZpbGwiLCJ4bWxucyIsInBhdGgiLCJkIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Icons/CrossIcon.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Icons/DownArrowIcon.tsx":
/*!************************************************!*\
  !*** ./src/components/Icons/DownArrowIcon.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DownArrowIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction DownArrowIcon() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"24\",\n        height: \"24\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 5L12 19\",\n                stroke: \"#FCFCFC\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\DownArrowIcon.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M19 12L12 19L5 12\",\n                stroke: \"#FCFCFC\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\DownArrowIcon.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\DownArrowIcon.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9JY29ucy9Eb3duQXJyb3dJY29uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFFWCxTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFDQ0MsT0FBTTtRQUNOQyxRQUFPO1FBQ1BDLFNBQVE7UUFDUkMsTUFBSztRQUNMQyxPQUFNOzswQkFFTiw4REFBQ0M7Z0JBQ0NDLEdBQUU7Z0JBQ0ZDLFFBQU87Z0JBQ1BDLGFBQVk7Z0JBQ1pDLGVBQWM7Z0JBQ2RDLGdCQUFlOzs7Ozs7MEJBRWpCLDhEQUFDTDtnQkFDQ0MsR0FBRTtnQkFDRkMsUUFBTztnQkFDUEMsYUFBWTtnQkFDWkMsZUFBYztnQkFDZEMsZ0JBQWU7Ozs7Ozs7Ozs7OztBQUl2QiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vc3JjL2NvbXBvbmVudHMvSWNvbnMvRG93bkFycm93SWNvbi50c3g/Nzc4YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG93bkFycm93SWNvbigpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPHN2Z1xyXG4gICAgICB3aWR0aD1cIjI0XCJcclxuICAgICAgaGVpZ2h0PVwiMjRcIlxyXG4gICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcclxuICAgICAgZmlsbD1cIm5vbmVcIlxyXG4gICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgID5cclxuICAgICAgPHBhdGhcclxuICAgICAgICBkPVwiTTEyIDVMMTIgMTlcIlxyXG4gICAgICAgIHN0cm9rZT1cIiNGQ0ZDRkNcIlxyXG4gICAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXHJcbiAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcclxuICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcclxuICAgICAgLz5cclxuICAgICAgPHBhdGhcclxuICAgICAgICBkPVwiTTE5IDEyTDEyIDE5TDUgMTJcIlxyXG4gICAgICAgIHN0cm9rZT1cIiNGQ0ZDRkNcIlxyXG4gICAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXHJcbiAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcclxuICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcclxuICAgICAgLz5cclxuICAgIDwvc3ZnPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiRG93bkFycm93SWNvbiIsInN2ZyIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsImZpbGwiLCJ4bWxucyIsInBhdGgiLCJkIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Icons/DownArrowIcon.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Icons/DropdownIcon.tsx":
/*!***********************************************!*\
  !*** ./src/components/Icons/DropdownIcon.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DropdownIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction DropdownIcon() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"8\",\n        height: \"5\",\n        viewBox: \"0 0 8 5\",\n        fill: \"currentColor\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M0.167368 0.381662C0.390524 0.158505 0.752333 0.158505 0.97549 0.381662L4 3.40617L7.02451 0.381662C7.24767 0.158505 7.60948 0.158505 7.83263 0.381662C8.05579 0.604819 8.05579 0.966627 7.83263 1.18978L4.40406 4.61836C4.1809 4.84151 3.8191 4.84151 3.59594 4.61836L0.167368 1.18978C-0.0557892 0.966627 -0.0557892 0.604819 0.167368 0.381662Z\",\n            fill: \"currentColor\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\DropdownIcon.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\DropdownIcon.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9JY29ucy9Ecm9wZG93bkljb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQjtBQUVYLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUNDQyxPQUFNO1FBQ05DLFFBQU87UUFDUEMsU0FBUTtRQUNSQyxNQUFLO1FBQ0xDLE9BQU07a0JBRU4sNEVBQUNDO1lBQ0NDLFVBQVM7WUFDVEMsVUFBUztZQUNUQyxHQUFFO1lBQ0ZMLE1BQUs7Ozs7Ozs7Ozs7O0FBSWIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL3NyYy9jb21wb25lbnRzL0ljb25zL0Ryb3Bkb3duSWNvbi50c3g/YTQ0NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRHJvcGRvd25JY29uKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8c3ZnXHJcbiAgICAgIHdpZHRoPVwiOFwiXHJcbiAgICAgIGhlaWdodD1cIjVcIlxyXG4gICAgICB2aWV3Qm94PVwiMCAwIDggNVwiXHJcbiAgICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgID5cclxuICAgICAgPHBhdGhcclxuICAgICAgICBmaWxsUnVsZT1cImV2ZW5vZGRcIlxyXG4gICAgICAgIGNsaXBSdWxlPVwiZXZlbm9kZFwiXHJcbiAgICAgICAgZD1cIk0wLjE2NzM2OCAwLjM4MTY2MkMwLjM5MDUyNCAwLjE1ODUwNSAwLjc1MjMzMyAwLjE1ODUwNSAwLjk3NTQ5IDAuMzgxNjYyTDQgMy40MDYxN0w3LjAyNDUxIDAuMzgxNjYyQzcuMjQ3NjcgMC4xNTg1MDUgNy42MDk0OCAwLjE1ODUwNSA3LjgzMjYzIDAuMzgxNjYyQzguMDU1NzkgMC42MDQ4MTkgOC4wNTU3OSAwLjk2NjYyNyA3LjgzMjYzIDEuMTg5NzhMNC40MDQwNiA0LjYxODM2QzQuMTgwOSA0Ljg0MTUxIDMuODE5MSA0Ljg0MTUxIDMuNTk1OTQgNC42MTgzNkwwLjE2NzM2OCAxLjE4OTc4Qy0wLjA1NTc4OTIgMC45NjY2MjcgLTAuMDU1Nzg5MiAwLjYwNDgxOSAwLjE2NzM2OCAwLjM4MTY2MlpcIlxyXG4gICAgICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICAvPlxyXG4gICAgPC9zdmc+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJEcm9wZG93bkljb24iLCJzdmciLCJ3aWR0aCIsImhlaWdodCIsInZpZXdCb3giLCJmaWxsIiwieG1sbnMiLCJwYXRoIiwiZmlsbFJ1bGUiLCJjbGlwUnVsZSIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Icons/DropdownIcon.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Icons/HamburgerMenuIcon.tsx":
/*!****************************************************!*\
  !*** ./src/components/Icons/HamburgerMenuIcon.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HamburgerMenuIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction HamburgerMenuIcon({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"40\",\n        height: \"40\",\n        viewBox: \"0 0 40 40\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M35 16.6667H5\",\n                stroke: \"white\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\HamburgerMenuIcon.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M35 10H5\",\n                stroke: \"white\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\HamburgerMenuIcon.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M35 23.3333H5\",\n                stroke: \"white\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\HamburgerMenuIcon.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M35 30H5\",\n                stroke: \"white\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\HamburgerMenuIcon.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\HamburgerMenuIcon.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Icons/HamburgerMenuIcon.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Icons/RightArrowIcon.tsx":
/*!*************************************************!*\
  !*** ./src/components/Icons/RightArrowIcon.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RightArrowIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction RightArrowIcon() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"24\",\n        height: \"24\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M5 12H19\",\n                stroke: \"#FCFCFC\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\RightArrowIcon.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 5L19 12L12 19\",\n                stroke: \"#FCFCFC\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\RightArrowIcon.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Icons\\\\RightArrowIcon.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9JY29ucy9SaWdodEFycm93SWNvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTBCO0FBRVgsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNDO1FBQ0NDLE9BQU07UUFDTkMsUUFBTztRQUNQQyxTQUFRO1FBQ1JDLE1BQUs7UUFDTEMsT0FBTTs7MEJBRU4sOERBQUNDO2dCQUNDQyxHQUFFO2dCQUNGQyxRQUFPO2dCQUNQQyxhQUFZO2dCQUNaQyxlQUFjO2dCQUNkQyxnQkFBZTs7Ozs7OzBCQUVqQiw4REFBQ0w7Z0JBQ0NDLEdBQUU7Z0JBQ0ZDLFFBQU87Z0JBQ1BDLGFBQVk7Z0JBQ1pDLGVBQWM7Z0JBQ2RDLGdCQUFlOzs7Ozs7Ozs7Ozs7QUFJdkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL3NyYy9jb21wb25lbnRzL0ljb25zL1JpZ2h0QXJyb3dJY29uLnRzeD84ZDFiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSaWdodEFycm93SWNvbigpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPHN2Z1xyXG4gICAgICB3aWR0aD1cIjI0XCJcclxuICAgICAgaGVpZ2h0PVwiMjRcIlxyXG4gICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcclxuICAgICAgZmlsbD1cIm5vbmVcIlxyXG4gICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgID5cclxuICAgICAgPHBhdGhcclxuICAgICAgICBkPVwiTTUgMTJIMTlcIlxyXG4gICAgICAgIHN0cm9rZT1cIiNGQ0ZDRkNcIlxyXG4gICAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXHJcbiAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcclxuICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcclxuICAgICAgLz5cclxuICAgICAgPHBhdGhcclxuICAgICAgICBkPVwiTTEyIDVMMTkgMTJMMTIgMTlcIlxyXG4gICAgICAgIHN0cm9rZT1cIiNGQ0ZDRkNcIlxyXG4gICAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXHJcbiAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcclxuICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcclxuICAgICAgLz5cclxuICAgIDwvc3ZnPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiUmlnaHRBcnJvd0ljb24iLCJzdmciLCJ3aWR0aCIsImhlaWdodCIsInZpZXdCb3giLCJmaWxsIiwieG1sbnMiLCJwYXRoIiwiZCIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Icons/RightArrowIcon.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Link/Link.tsx":
/*!**************************************!*\
  !*** ./src/components/Link/Link.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Link)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _utils_classNames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @utils/classNames */ \"(ssr)/./src/utils/classNames.ts\");\n\n\n\n\nfunction Link({ id, children, href = \"\", className, style, isExternal = false, onClick, scroll = true, shallow = false, ariaLabel, dataID = null, suppressHydrationWarning = false }) {\n    return isExternal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        suppressHydrationWarning: suppressHydrationWarning,\n        id: id,\n        className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(className),\n        href: href,\n        style: style,\n        target: \"_blank\",\n        rel: \"noreferrer\",\n        \"aria-label\": ariaLabel,\n        onClick: onClick,\n        \"data-id\": dataID,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Link\\\\Link.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        href: href,\n        scroll: scroll,\n        shallow: shallow,\n        suppressHydrationWarning: suppressHydrationWarning,\n        id: id,\n        className: (0,_utils_classNames__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(className),\n        style: style,\n        onClick: onClick,\n        \"data-id\": dataID,\n        \"aria-label\": ariaLabel,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\Link\\\\Link.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Link/Link.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useMediaQueryState.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useMediaQueryState.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useMediaQueryState =  true ? ()=>false : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMediaQueryState);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlTWVkaWFRdWVyeVN0YXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRTtBQUVqRSxNQUFNSSxxQkFDSixLQUE4RCxHQUMxRCxJQUFNLFFBQ04sQ0FvQkM7QUFFUCxpRUFBZUEsa0JBQWtCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9zcmMvaG9va3MvdXNlTWVkaWFRdWVyeVN0YXRlLnRzPzIwMzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmNvbnN0IHVzZU1lZGlhUXVlcnlTdGF0ZSA9XHJcbiAgdHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcgfHwgdHlwZW9mIHdpbmRvdy5tYXRjaE1lZGlhID09PSAndW5kZWZpbmVkJ1xyXG4gICAgPyAoKSA9PiBmYWxzZVxyXG4gICAgOiAoeyBxdWVyeSB9OiB7IHF1ZXJ5OiBzdHJpbmcgfSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IFtpc01hdGNoLCBzZXRJc01hdGNoXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICAgICAgICBjb25zdCBtZWRpYVF1ZXJ5UmVmID0gdXNlUmVmKHdpbmRvdy5tYXRjaE1lZGlhKHF1ZXJ5KSk7XHJcblxyXG4gICAgICAgIGNvbnN0IGhhbmRsZU1hdGNoID0gdXNlQ2FsbGJhY2soXHJcbiAgICAgICAgICBldmVudCA9PiB7XHJcbiAgICAgICAgICAgIHNldElzTWF0Y2goZXZlbnQubWF0Y2hlcyk7XHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgW3NldElzTWF0Y2hdLFxyXG4gICAgICAgICk7XHJcblxyXG4gICAgICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgICBjb25zdCBtZWRpYVF1ZXJ5TGlzdCA9IG1lZGlhUXVlcnlSZWYuY3VycmVudDtcclxuICAgICAgICAgIHNldElzTWF0Y2gobWVkaWFRdWVyeUxpc3QubWF0Y2hlcyk7XHJcbiAgICAgICAgICBtZWRpYVF1ZXJ5TGlzdC5hZGRMaXN0ZW5lcihoYW5kbGVNYXRjaCk7XHJcblxyXG4gICAgICAgICAgcmV0dXJuICgpID0+IG1lZGlhUXVlcnlMaXN0LnJlbW92ZUxpc3RlbmVyKGhhbmRsZU1hdGNoKTtcclxuICAgICAgICB9LCBbc2V0SXNNYXRjaCwgaGFuZGxlTWF0Y2hdKTtcclxuXHJcbiAgICAgICAgcmV0dXJuIGlzTWF0Y2g7XHJcbiAgICAgIH07XHJcblxyXG5leHBvcnQgZGVmYXVsdCB1c2VNZWRpYVF1ZXJ5U3RhdGU7XHJcbiJdLCJuYW1lcyI6WyJ1c2VDYWxsYmFjayIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwidXNlTWVkaWFRdWVyeVN0YXRlIiwid2luZG93IiwibWF0Y2hNZWRpYSIsInF1ZXJ5IiwiaXNNYXRjaCIsInNldElzTWF0Y2giLCJtZWRpYVF1ZXJ5UmVmIiwiaGFuZGxlTWF0Y2giLCJldmVudCIsIm1hdGNoZXMiLCJtZWRpYVF1ZXJ5TGlzdCIsImN1cnJlbnQiLCJhZGRMaXN0ZW5lciIsInJlbW92ZUxpc3RlbmVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useMediaQueryState.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/classNames.ts":
/*!*********************************!*\
  !*** ./src/utils/classNames.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ classNames)\n/* harmony export */ });\nfunction classNames(...classes) {\n    return classes.filter(Boolean).join(\" \");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvY2xhc3NOYW1lcy50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsV0FBVyxHQUFHQyxPQUFPO0lBQzNDLE9BQU9BLFFBQVFDLE1BQU0sQ0FBQ0MsU0FBU0MsSUFBSSxDQUFDO0FBQ3RDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9zcmMvdXRpbHMvY2xhc3NOYW1lcy50cz8yM2RiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNsYXNzTmFtZXMoLi4uY2xhc3Nlcyk6IHN0cmluZyB7XHJcbiAgcmV0dXJuIGNsYXNzZXMuZmlsdGVyKEJvb2xlYW4pLmpvaW4oJyAnKTtcclxufVxyXG4iXSwibmFtZXMiOlsiY2xhc3NOYW1lcyIsImNsYXNzZXMiLCJmaWx0ZXIiLCJCb29sZWFuIiwiam9pbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/classNames.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/hashNavigation.ts":
/*!*************************************!*\
  !*** ./src/utils/hashNavigation.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearHash: () => (/* binding */ clearHash),\n/* harmony export */   createHashUrl: () => (/* binding */ createHashUrl),\n/* harmony export */   getCurrentHash: () => (/* binding */ getCurrentHash),\n/* harmony export */   handleHashNavigation: () => (/* binding */ handleHashNavigation),\n/* harmony export */   isCurrentPage: () => (/* binding */ isCurrentPage),\n/* harmony export */   navigateToHash: () => (/* binding */ navigateToHash),\n/* harmony export */   navigateToPageWithHash: () => (/* binding */ navigateToPageWithHash),\n/* harmony export */   normalizeUrl: () => (/* binding */ normalizeUrl),\n/* harmony export */   onHashChange: () => (/* binding */ onHashChange)\n/* harmony export */ });\n/**\r\n * Hash Navigation Utility\r\n * Handles hash-based navigation consistently across local development and production environments\r\n * Accounts for Next.js static export with trailingSlash: true and CloudFront behavior\r\n */ /**\r\n * Normalizes a URL to ensure consistent behavior between local and production\r\n * @param url - The URL to normalize\r\n * @returns Normalized URL with proper trailing slash handling\r\n */ function normalizeUrl(url) {\n    if (!url || typeof url !== \"string\") {\n        return \"\";\n    }\n    // Remove any existing hash fragments for base URL normalization\n    const [baseUrl] = url.split(\"#\");\n    // Ensure trailing slash for consistency with Next.js trailingSlash: true\n    const normalizedBase = baseUrl.endsWith(\"/\") ? baseUrl : `${baseUrl}/`;\n    return normalizedBase;\n}\n/**\r\n * Creates a proper hash URL for navigation\r\n * @param basePath - The base path (e.g., '/about-us', '/careers')\r\n * @param hashFragment - The hash fragment (e.g., 'our-story', 'values')\r\n * @returns Properly formatted hash URL\r\n */ function createHashUrl(basePath, hashFragment) {\n    if (!basePath || !hashFragment) {\n        return basePath || \"\";\n    }\n    const normalizedBase = normalizeUrl(basePath);\n    const cleanHash = hashFragment.startsWith(\"#\") ? hashFragment.slice(1) : hashFragment;\n    return `${normalizedBase}#${cleanHash}`;\n}\n/**\r\n * Extracts the current hash fragment from the URL\r\n * @returns The current hash fragment without the # symbol, or empty string if none\r\n */ function getCurrentHash() {\n    if (true) {\n        return \"\";\n    }\n    return window.location.hash.substring(1);\n}\n/**\r\n * Navigates to a hash section on the current page\r\n * @param hashFragment - The hash fragment to navigate to\r\n * @param smooth - Whether to use smooth scrolling (default: true)\r\n */ function navigateToHash(hashFragment, smooth = true) {\n    if (true) {\n        return;\n    }\n    const cleanHash = hashFragment.startsWith(\"#\") ? hashFragment.slice(1) : hashFragment;\n    // Update the URL hash\n    if (window.location.hash !== `#${cleanHash}`) {\n        window.location.hash = `#${cleanHash}`;\n    }\n    // Scroll to the element\n    const element = document.getElementById(cleanHash);\n    if (element) {\n        element.scrollIntoView({\n            behavior: smooth ? \"smooth\" : \"auto\",\n            block: \"start\"\n        });\n    }\n}\n/**\r\n * Navigates to a different page with a hash fragment\r\n * @param basePath - The base path to navigate to\r\n * @param hashFragment - The hash fragment to navigate to\r\n */ function navigateToPageWithHash(basePath, hashFragment) {\n    if (true) {\n        return;\n    }\n    const hashUrl = createHashUrl(basePath, hashFragment);\n    window.location.href = hashUrl;\n}\n/**\r\n * Checks if the current page matches the given base path\r\n * @param basePath - The base path to check against\r\n * @returns True if the current page matches the base path\r\n */ function isCurrentPage(basePath) {\n    if (true) {\n        return false;\n    }\n    const currentPath = window.location.pathname;\n    const normalizedBasePath = normalizeUrl(basePath);\n    const normalizedCurrentPath = normalizeUrl(currentPath);\n    return normalizedCurrentPath === normalizedBasePath;\n}\n/**\r\n * Handles hash navigation for links\r\n * Determines whether to navigate to a new page or scroll to a section on the current page\r\n * @param href - The full href including base path and hash\r\n * @param onClick - Optional click handler to call\r\n */ function handleHashNavigation(href, onClick) {\n    if (!href || typeof href !== \"string\") {\n        return;\n    }\n    // Call the onClick handler if provided\n    if (onClick) {\n        onClick();\n    }\n    // Parse the href to extract base path and hash\n    const [basePath, hashFragment] = href.split(\"#\");\n    if (!hashFragment) {\n        // No hash fragment, just navigate normally\n        if (false) {}\n        return;\n    }\n    // Check if we're navigating to the same page\n    if (isCurrentPage(basePath)) {\n        // Same page, just scroll to the hash section\n        navigateToHash(hashFragment);\n    } else {\n        // Different page, navigate with hash\n        navigateToPageWithHash(basePath, hashFragment);\n    }\n}\n/**\r\n * Clears any existing hash from the current URL\r\n */ function clearHash() {\n    if (true) {\n        return;\n    }\n    if (window.location.hash) {\n        // Use replaceState to avoid adding to browser history\n        const urlWithoutHash = window.location.href.split(\"#\")[0];\n        window.history.replaceState(null, \"\", urlWithoutHash);\n    }\n}\n/**\r\n * Sets up hash change event listener\r\n * @param callback - Function to call when hash changes\r\n * @returns Cleanup function to remove the event listener\r\n */ function onHashChange(callback) {\n    if (true) {\n        return ()=>{};\n    }\n    const handleHashChange = ()=>{\n        const currentHash = getCurrentHash();\n        callback(currentHash);\n    };\n    window.addEventListener(\"hashchange\", handleHashChange);\n    // Return cleanup function\n    return ()=>{\n        window.removeEventListener(\"hashchange\", handleHashChange);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/hashNavigation.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/base.css":
/*!*****************************!*\
  !*** ./src/styles/base.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8415a9438bea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2Jhc2UuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9zcmMvc3R5bGVzL2Jhc2UuY3NzPzI4OTgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4NDE1YTk0MzhiZWFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/base.css\n");

/***/ }),

/***/ "(ssr)/./src/app/not-found.module.css":
/*!**************************************!*\
  !*** ./src/app/not-found.module.css ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Imports\nvar ___CSS_LOADER_ICSS_IMPORT_0___ = __webpack_require__(/*! -!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!../styles/breakpoints.module.css */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/breakpoints.module.css\");\n// Exports\nmodule.exports = {\n\t\"breakpoints\": \"\\\"@styles/breakpoints.module.css\\\"\",\n\t\"breakpoint-sm\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"breakpoint-sm\"] + \"\",\n\t\"container\": \"not-found_container__LRagC\",\n\t\"image\": \"not-found_image__klXiS\",\n\t\"backToHomeButton\": \"not-found_backToHomeButton__iMCST\"\n};\n\nmodule.exports.__checksum = \"d461b93b5725\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL25vdC1mb3VuZC5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0EscUNBQXFDLG1CQUFPLENBQUMseWhCQUEwUTtBQUN2VDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vc3JjL2FwcC9ub3QtZm91bmQubW9kdWxlLmNzcz8xNTViIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEltcG9ydHNcbnZhciBfX19DU1NfTE9BREVSX0lDU1NfSU1QT1JUXzBfX18gPSByZXF1aXJlKFwiLSEuLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9jc3MtbG9hZGVyL3NyYy9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1sxMl0ub25lT2ZbN10udXNlWzFdIS4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL3Bvc3Rjc3MtbG9hZGVyL3NyYy9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1sxMl0ub25lT2ZbN10udXNlWzJdIS4uL3N0eWxlcy9icmVha3BvaW50cy5tb2R1bGUuY3NzXCIpO1xuLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiYnJlYWtwb2ludHNcIjogXCJcXFwiQHN0eWxlcy9icmVha3BvaW50cy5tb2R1bGUuY3NzXFxcIlwiLFxuXHRcImJyZWFrcG9pbnQtc21cIjogXCJcIiArIF9fX0NTU19MT0FERVJfSUNTU19JTVBPUlRfMF9fX1tcImJyZWFrcG9pbnQtc21cIl0gKyBcIlwiLFxuXHRcImNvbnRhaW5lclwiOiBcIm5vdC1mb3VuZF9jb250YWluZXJfX0xSYWdDXCIsXG5cdFwiaW1hZ2VcIjogXCJub3QtZm91bmRfaW1hZ2VfX2tsWGlTXCIsXG5cdFwiYmFja1RvSG9tZUJ1dHRvblwiOiBcIm5vdC1mb3VuZF9iYWNrVG9Ib21lQnV0dG9uX19pTUNTVFwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJkNDYxYjkzYjU3MjVcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/not-found.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/Button/Button.module.css":
/*!*************************************************!*\
  !*** ./src/components/Button/Button.module.css ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Imports\nvar ___CSS_LOADER_ICSS_IMPORT_0___ = __webpack_require__(/*! -!../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!../../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!../../styles/variables.module.css */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/variables.module.css\");\n// Exports\nmodule.exports = {\n\t\"variables\": \"\\\"@styles/variables.module.css\\\"\",\n\t\"brandColorOne\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"brandColorOne\"] + \"\",\n\t\"brandColorTwo\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"brandColorTwo\"] + \"\",\n\t\"brandColorThree\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"brandColorThree\"] + \"\",\n\t\"brandColorFour\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"brandColorFour\"] + \"\",\n\t\"brandColorFive\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"brandColorFive\"] + \"\",\n\t\"colorBlack\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"colorBlack\"] + \"\",\n\t\"gray300\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"gray300\"] + \"\",\n\t\"colorWhite\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"colorWhite\"] + \"\",\n\t\"button\": \"Button_button__exqP_\",\n\t\"link\": \"Button_link__9n7Et\",\n\t\"innerWrapper\": \"Button_innerWrapper__ITLB1\",\n\t\"leftWrapper\": \"Button_leftWrapper__fWtI9\",\n\t\"rightWrapper\": \"Button_rightWrapper__GkIh_\"\n};\n\nmodule.exports.__checksum = \"4f540f04575a\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Button/Button.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/CircularButtonWithArrow/CircularButtonWithArrow.module.css":
/*!***********************************************************************************!*\
  !*** ./src/components/CircularButtonWithArrow/CircularButtonWithArrow.module.css ***!
  \***********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"CircularButtonWithArrow_container__9Cvr1\",\n\t\"circle\": \"CircularButtonWithArrow_circle__H7jjo\",\n\t\"arrow\": \"CircularButtonWithArrow_arrow__h3ojH\",\n\t\"arrowImage\": \"CircularButtonWithArrow_arrowImage__G7E_X\",\n\t\"arrow_scroll\": \"CircularButtonWithArrow_arrow_scroll__a_DTi\"\n};\n\nmodule.exports.__checksum = \"35b7af14e3ba\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DaXJjdWxhckJ1dHRvbldpdGhBcnJvdy9DaXJjdWxhckJ1dHRvbldpdGhBcnJvdy5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9zcmMvY29tcG9uZW50cy9DaXJjdWxhckJ1dHRvbldpdGhBcnJvdy9DaXJjdWxhckJ1dHRvbldpdGhBcnJvdy5tb2R1bGUuY3NzP2M5NTMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiY29udGFpbmVyXCI6IFwiQ2lyY3VsYXJCdXR0b25XaXRoQXJyb3dfY29udGFpbmVyX185Q3ZyMVwiLFxuXHRcImNpcmNsZVwiOiBcIkNpcmN1bGFyQnV0dG9uV2l0aEFycm93X2NpcmNsZV9fSDdqam9cIixcblx0XCJhcnJvd1wiOiBcIkNpcmN1bGFyQnV0dG9uV2l0aEFycm93X2Fycm93X19oM29qSFwiLFxuXHRcImFycm93SW1hZ2VcIjogXCJDaXJjdWxhckJ1dHRvbldpdGhBcnJvd19hcnJvd0ltYWdlX19HN0VfWFwiLFxuXHRcImFycm93X3Njcm9sbFwiOiBcIkNpcmN1bGFyQnV0dG9uV2l0aEFycm93X2Fycm93X3Njcm9sbF9fYV9EVGlcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiMzViN2FmMTRlM2JhXCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CircularButtonWithArrow/CircularButtonWithArrow.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer/Footer.module.css":
/*!*************************************************!*\
  !*** ./src/components/Footer/Footer.module.css ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Imports\nvar ___CSS_LOADER_ICSS_IMPORT_0___ = __webpack_require__(/*! -!../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!../../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!../../styles/variables.module.css */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/variables.module.css\");\nvar ___CSS_LOADER_ICSS_IMPORT_1___ = __webpack_require__(/*! -!../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!../../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!../../styles/breakpoints.module.css */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/breakpoints.module.css\");\n// Exports\nmodule.exports = {\n\t\"variables\": \"\\\"@styles/variables.module.css\\\"\",\n\t\"colorBlack\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"colorBlack\"] + \"\",\n\t\"colorWhite\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"colorWhite\"] + \"\",\n\t\"brandColorThree\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"brandColorThree\"] + \"\",\n\t\"fifteenSpace\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"fifteenSpace\"] + \"\",\n\t\"grayBorder\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"grayBorder\"] + \"\",\n\t\"breakpoints\": \"\\\"@styles/breakpoints.module.css\\\"\",\n\t\"breakpoint-md\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-md\"] + \"\",\n\t\"breakpoint-sm-450\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-sm-450\"] + \"\",\n\t\"breakpoint-sm-550\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-sm-550\"] + \"\",\n\t\"breakpoint-sm-320\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-sm-320\"] + \"\",\n\t\"breakpoint-xl-1024\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-xl-1024\"] + \"\",\n\t\"breakpoint-xl-1440\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-xl-1440\"] + \"\",\n\t\"breakpoint-lg\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-lg\"] + \"\",\n\t\"breakpoint-lg-991px\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-lg-991px\"] + \"\",\n\t\"main_container\": \"Footer_main_container__LZ2hx\",\n\t\"first_second_row\": \"Footer_first_second_row__X_sZS\",\n\t\"column\": \"Footer_column__tKCuc\",\n\t\"firstrow\": \"Footer_firstrow__Sygqj\",\n\t\"title_firstrow\": \"Footer_title_firstrow__C7F_t\",\n\t\"link_title\": \"Footer_link_title__CRgh0\",\n\t\"sublink_title\": \"Footer_sublink_title__NuYkY\",\n\t\"secondrow\": \"Footer_secondrow__HqLZa\",\n\t\"thirdrow\": \"Footer_thirdrow__EDmf_\",\n\t\"terms_and_condition_title\": \"Footer_terms_and_condition_title__ml5gN\",\n\t\"company_logo_section\": \"Footer_company_logo_section__TxSfQ\",\n\t\"iconsContainer\": \"Footer_iconsContainer__u9PPI\",\n\t\"imageContainer\": \"Footer_imageContainer__rapPm\",\n\t\"copyright\": \"Footer_copyright__1v3uR\",\n\t\"icon\": \"Footer_icon__UIUYS\"\n};\n\nmodule.exports.__checksum = \"7c3c023c857b\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer/Footer.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/Header/Header.module.css":
/*!*************************************************!*\
  !*** ./src/components/Header/Header.module.css ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Imports\nvar ___CSS_LOADER_ICSS_IMPORT_0___ = __webpack_require__(/*! -!../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!../../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!../../styles/variables.module.css */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/variables.module.css\");\nvar ___CSS_LOADER_ICSS_IMPORT_1___ = __webpack_require__(/*! -!../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!../../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!../../styles/breakpoints.module.css */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/breakpoints.module.css\");\n// Exports\nmodule.exports = {\n\t\"variables\": \"\\\"@styles/variables.module.css\\\"\",\n\t\"colorBlack\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"colorBlack\"] + \"\",\n\t\"gray\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"gray\"] + \"\",\n\t\"colorWhite\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"colorWhite\"] + \"\",\n\t\"fifteenSpace\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"fifteenSpace\"] + \"\",\n\t\"grayBorder\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"grayBorder\"] + \"\",\n\t\"brandColorOne\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"brandColorOne\"] + \"\",\n\t\"brandColorTwo\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"brandColorTwo\"] + \"\",\n\t\"brandColorThree\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"brandColorThree\"] + \"\",\n\t\"brandColorFour\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"brandColorFour\"] + \"\",\n\t\"brandColorFive\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"brandColorFive\"] + \"\",\n\t\"breakpoints\": \"\\\"@styles/breakpoints.module.css\\\"\",\n\t\"breakpoint-md\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-md\"] + \"\",\n\t\"breakpoint-sm-320\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-sm-320\"] + \"\",\n\t\"breakpoint-sm-427\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-sm-427\"] + \"\",\n\t\"breakpoint-xl-1024\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-xl-1024\"] + \"\",\n\t\"breakpoint-xl-1440\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-xl-1440\"] + \"\",\n\t\"breakpoint-lg\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-lg\"] + \"\",\n\t\"breakpoint-lg-991px\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-lg-991px\"] + \"\",\n\t\"breakpoint-xl-2100\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-xl-2100\"] + \"\",\n\t\"breakpoint-md-769\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-md-769\"] + \"\",\n\t\"breakpoint-sm-430\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-sm-430\"] + \"\",\n\t\"navbarWrapper\": \"Header_navbarWrapper__HdM_E\",\n\t\"hidden\": \"Header_hidden__pcG11\",\n\t\"search-button\": \"Header_search-button__GF3KH\",\n\t\"navLink\": \"Header_navLink__bX76H\",\n\t\"navLink_mobile\": \"Header_navLink_mobile__XnrY5\",\n\t\"navItem\": \"Header_navItem__pb6e5\",\n\t\"link\": \"Header_link__p2PJz\",\n\t\"linkTitle\": \"Header_linkTitle__FTnZl\",\n\t\"linkTitle_others\": \"Header_linkTitle_others__oyfpn\",\n\t\"navbarCollapse\": \"Header_navbarCollapse__nWJ4z\",\n\t\"nav_dis\": \"Header_nav_dis__Z7pai\",\n\t\"nav_nav_style\": \"Header_nav_nav_style__bwvFu\",\n\t\"applicationContainer\": \"Header_applicationContainer__noTHf\",\n\t\"nav\": \"Header_nav__LVYU2\",\n\t\"arrowIcon\": \"Header_arrowIcon__ixZre\",\n\t\"search\": \"Header_search__m3eU6\",\n\t\"searchWrapper\": \"Header_searchWrapper__YvUwA\",\n\t\"navbar-nav\": \"Header_navbar-nav__E5HZO\",\n\t\"nav-link\": \"Header_nav-link__7xXdK\",\n\t\"navBarToggler\": \"Header_navBarToggler__cCWUA\",\n\t\"menuIcons\": \"Header_menuIcons__4NysM\",\n\t\"clicked\": \"Header_clicked__eFyFJ\",\n\t\"hide\": \"Header_hide__RbUcz\",\n\t\"brandContainer\": \"Header_brandContainer__32TYX\",\n\t\"visibility\": \"Header_visibility__Do2_f\",\n\t\"menuWrapper\": \"Header_menuWrapper__weqYh\",\n\t\"megaMenuContent\": \"Header_megaMenuContent__tg_ef\",\n\t\"largeDeviceSpacing\": \"Header_largeDeviceSpacing__Spsib\",\n\t\"megamenu\": \"Header_megamenu__N9gL8\",\n\t\"latestBlogWrapper\": \"Header_latestBlogWrapper__bgX6a\",\n\t\"blogHeading\": \"Header_blogHeading__hcugT\",\n\t\"blogTitle\": \"Header_blogTitle__0mbcv\",\n\t\"blogCTALink\": \"Header_blogCTALink__hK1J4\",\n\t\"ctaLink\": \"Header_ctaLink__UeKwP\",\n\t\"brandsButton\": \"Header_brandsButton__jLpK4\",\n\t\"flexDirectionColumn\": \"Header_flexDirectionColumn__PGxpa\",\n\t\"accordion-body\": \"Header_accordion-body__N4tpS\",\n\t\"all_service_button\": \"Header_all_service_button__93M7L\",\n\t\"mtech_logo\": \"Header_mtech_logo__B7aPt\",\n\t\"bottom_border\": \"Header_bottom_border__035sm\",\n\t\"hidden_override\": \"Header_hidden_override__0X_rN\"\n};\n\nmodule.exports.__checksum = \"ddf35459f710\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9IZWFkZXIvSGVhZGVyLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxxQ0FBcUMsbUJBQU8sQ0FBQyw4aEJBQWlSO0FBQzlULHFDQUFxQyxtQkFBTyxDQUFDLGtpQkFBbVI7QUFDaFU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vc3JjL2NvbXBvbmVudHMvSGVhZGVyL0hlYWRlci5tb2R1bGUuY3NzPzBkZmMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gSW1wb3J0c1xudmFyIF9fX0NTU19MT0FERVJfSUNTU19JTVBPUlRfMF9fXyA9IHJlcXVpcmUoXCItIS4uLy4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL2Nzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzEyXS5vbmVPZls3XS51c2VbMV0hLi4vLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvcG9zdGNzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzEyXS5vbmVPZls3XS51c2VbMl0hLi4vLi4vc3R5bGVzL3ZhcmlhYmxlcy5tb2R1bGUuY3NzXCIpO1xudmFyIF9fX0NTU19MT0FERVJfSUNTU19JTVBPUlRfMV9fXyA9IHJlcXVpcmUoXCItIS4uLy4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL2Nzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzEyXS5vbmVPZls3XS51c2VbMV0hLi4vLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvcG9zdGNzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzEyXS5vbmVPZls3XS51c2VbMl0hLi4vLi4vc3R5bGVzL2JyZWFrcG9pbnRzLm1vZHVsZS5jc3NcIik7XG4vLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ2YXJpYWJsZXNcIjogXCJcXFwiQHN0eWxlcy92YXJpYWJsZXMubW9kdWxlLmNzc1xcXCJcIixcblx0XCJjb2xvckJsYWNrXCI6IFwiXCIgKyBfX19DU1NfTE9BREVSX0lDU1NfSU1QT1JUXzBfX19bXCJjb2xvckJsYWNrXCJdICsgXCJcIixcblx0XCJncmF5XCI6IFwiXCIgKyBfX19DU1NfTE9BREVSX0lDU1NfSU1QT1JUXzBfX19bXCJncmF5XCJdICsgXCJcIixcblx0XCJjb2xvcldoaXRlXCI6IFwiXCIgKyBfX19DU1NfTE9BREVSX0lDU1NfSU1QT1JUXzBfX19bXCJjb2xvcldoaXRlXCJdICsgXCJcIixcblx0XCJmaWZ0ZWVuU3BhY2VcIjogXCJcIiArIF9fX0NTU19MT0FERVJfSUNTU19JTVBPUlRfMF9fX1tcImZpZnRlZW5TcGFjZVwiXSArIFwiXCIsXG5cdFwiZ3JheUJvcmRlclwiOiBcIlwiICsgX19fQ1NTX0xPQURFUl9JQ1NTX0lNUE9SVF8wX19fW1wiZ3JheUJvcmRlclwiXSArIFwiXCIsXG5cdFwiYnJhbmRDb2xvck9uZVwiOiBcIlwiICsgX19fQ1NTX0xPQURFUl9JQ1NTX0lNUE9SVF8wX19fW1wiYnJhbmRDb2xvck9uZVwiXSArIFwiXCIsXG5cdFwiYnJhbmRDb2xvclR3b1wiOiBcIlwiICsgX19fQ1NTX0xPQURFUl9JQ1NTX0lNUE9SVF8wX19fW1wiYnJhbmRDb2xvclR3b1wiXSArIFwiXCIsXG5cdFwiYnJhbmRDb2xvclRocmVlXCI6IFwiXCIgKyBfX19DU1NfTE9BREVSX0lDU1NfSU1QT1JUXzBfX19bXCJicmFuZENvbG9yVGhyZWVcIl0gKyBcIlwiLFxuXHRcImJyYW5kQ29sb3JGb3VyXCI6IFwiXCIgKyBfX19DU1NfTE9BREVSX0lDU1NfSU1QT1JUXzBfX19bXCJicmFuZENvbG9yRm91clwiXSArIFwiXCIsXG5cdFwiYnJhbmRDb2xvckZpdmVcIjogXCJcIiArIF9fX0NTU19MT0FERVJfSUNTU19JTVBPUlRfMF9fX1tcImJyYW5kQ29sb3JGaXZlXCJdICsgXCJcIixcblx0XCJicmVha3BvaW50c1wiOiBcIlxcXCJAc3R5bGVzL2JyZWFrcG9pbnRzLm1vZHVsZS5jc3NcXFwiXCIsXG5cdFwiYnJlYWtwb2ludC1tZFwiOiBcIlwiICsgX19fQ1NTX0xPQURFUl9JQ1NTX0lNUE9SVF8xX19fW1wiYnJlYWtwb2ludC1tZFwiXSArIFwiXCIsXG5cdFwiYnJlYWtwb2ludC1zbS0zMjBcIjogXCJcIiArIF9fX0NTU19MT0FERVJfSUNTU19JTVBPUlRfMV9fX1tcImJyZWFrcG9pbnQtc20tMzIwXCJdICsgXCJcIixcblx0XCJicmVha3BvaW50LXNtLTQyN1wiOiBcIlwiICsgX19fQ1NTX0xPQURFUl9JQ1NTX0lNUE9SVF8xX19fW1wiYnJlYWtwb2ludC1zbS00MjdcIl0gKyBcIlwiLFxuXHRcImJyZWFrcG9pbnQteGwtMTAyNFwiOiBcIlwiICsgX19fQ1NTX0xPQURFUl9JQ1NTX0lNUE9SVF8xX19fW1wiYnJlYWtwb2ludC14bC0xMDI0XCJdICsgXCJcIixcblx0XCJicmVha3BvaW50LXhsLTE0NDBcIjogXCJcIiArIF9fX0NTU19MT0FERVJfSUNTU19JTVBPUlRfMV9fX1tcImJyZWFrcG9pbnQteGwtMTQ0MFwiXSArIFwiXCIsXG5cdFwiYnJlYWtwb2ludC1sZ1wiOiBcIlwiICsgX19fQ1NTX0xPQURFUl9JQ1NTX0lNUE9SVF8xX19fW1wiYnJlYWtwb2ludC1sZ1wiXSArIFwiXCIsXG5cdFwiYnJlYWtwb2ludC1sZy05OTFweFwiOiBcIlwiICsgX19fQ1NTX0xPQURFUl9JQ1NTX0lNUE9SVF8xX19fW1wiYnJlYWtwb2ludC1sZy05OTFweFwiXSArIFwiXCIsXG5cdFwiYnJlYWtwb2ludC14bC0yMTAwXCI6IFwiXCIgKyBfX19DU1NfTE9BREVSX0lDU1NfSU1QT1JUXzFfX19bXCJicmVha3BvaW50LXhsLTIxMDBcIl0gKyBcIlwiLFxuXHRcImJyZWFrcG9pbnQtbWQtNzY5XCI6IFwiXCIgKyBfX19DU1NfTE9BREVSX0lDU1NfSU1QT1JUXzFfX19bXCJicmVha3BvaW50LW1kLTc2OVwiXSArIFwiXCIsXG5cdFwiYnJlYWtwb2ludC1zbS00MzBcIjogXCJcIiArIF9fX0NTU19MT0FERVJfSUNTU19JTVBPUlRfMV9fX1tcImJyZWFrcG9pbnQtc20tNDMwXCJdICsgXCJcIixcblx0XCJuYXZiYXJXcmFwcGVyXCI6IFwiSGVhZGVyX25hdmJhcldyYXBwZXJfX0hkTV9FXCIsXG5cdFwiaGlkZGVuXCI6IFwiSGVhZGVyX2hpZGRlbl9fcGNHMTFcIixcblx0XCJzZWFyY2gtYnV0dG9uXCI6IFwiSGVhZGVyX3NlYXJjaC1idXR0b25fX0dGM0tIXCIsXG5cdFwibmF2TGlua1wiOiBcIkhlYWRlcl9uYXZMaW5rX19iWDc2SFwiLFxuXHRcIm5hdkxpbmtfbW9iaWxlXCI6IFwiSGVhZGVyX25hdkxpbmtfbW9iaWxlX19YbnJZNVwiLFxuXHRcIm5hdkl0ZW1cIjogXCJIZWFkZXJfbmF2SXRlbV9fcGI2ZTVcIixcblx0XCJsaW5rXCI6IFwiSGVhZGVyX2xpbmtfX3AyUEp6XCIsXG5cdFwibGlua1RpdGxlXCI6IFwiSGVhZGVyX2xpbmtUaXRsZV9fRlRuWmxcIixcblx0XCJsaW5rVGl0bGVfb3RoZXJzXCI6IFwiSGVhZGVyX2xpbmtUaXRsZV9vdGhlcnNfX295ZnBuXCIsXG5cdFwibmF2YmFyQ29sbGFwc2VcIjogXCJIZWFkZXJfbmF2YmFyQ29sbGFwc2VfX25XSjR6XCIsXG5cdFwibmF2X2Rpc1wiOiBcIkhlYWRlcl9uYXZfZGlzX19aN3BhaVwiLFxuXHRcIm5hdl9uYXZfc3R5bGVcIjogXCJIZWFkZXJfbmF2X25hdl9zdHlsZV9fYnd2RnVcIixcblx0XCJhcHBsaWNhdGlvbkNvbnRhaW5lclwiOiBcIkhlYWRlcl9hcHBsaWNhdGlvbkNvbnRhaW5lcl9fbm9USGZcIixcblx0XCJuYXZcIjogXCJIZWFkZXJfbmF2X19MVllVMlwiLFxuXHRcImFycm93SWNvblwiOiBcIkhlYWRlcl9hcnJvd0ljb25fX2l4WnJlXCIsXG5cdFwic2VhcmNoXCI6IFwiSGVhZGVyX3NlYXJjaF9fbTNlVTZcIixcblx0XCJzZWFyY2hXcmFwcGVyXCI6IFwiSGVhZGVyX3NlYXJjaFdyYXBwZXJfX1l2VXdBXCIsXG5cdFwibmF2YmFyLW5hdlwiOiBcIkhlYWRlcl9uYXZiYXItbmF2X19FNUhaT1wiLFxuXHRcIm5hdi1saW5rXCI6IFwiSGVhZGVyX25hdi1saW5rX183eFhkS1wiLFxuXHRcIm5hdkJhclRvZ2dsZXJcIjogXCJIZWFkZXJfbmF2QmFyVG9nZ2xlcl9fY0NXVUFcIixcblx0XCJtZW51SWNvbnNcIjogXCJIZWFkZXJfbWVudUljb25zX180TnlzTVwiLFxuXHRcImNsaWNrZWRcIjogXCJIZWFkZXJfY2xpY2tlZF9fZUZ5RkpcIixcblx0XCJoaWRlXCI6IFwiSGVhZGVyX2hpZGVfX1JiVWN6XCIsXG5cdFwiYnJhbmRDb250YWluZXJcIjogXCJIZWFkZXJfYnJhbmRDb250YWluZXJfXzMyVFlYXCIsXG5cdFwidmlzaWJpbGl0eVwiOiBcIkhlYWRlcl92aXNpYmlsaXR5X19EbzJfZlwiLFxuXHRcIm1lbnVXcmFwcGVyXCI6IFwiSGVhZGVyX21lbnVXcmFwcGVyX193ZXFZaFwiLFxuXHRcIm1lZ2FNZW51Q29udGVudFwiOiBcIkhlYWRlcl9tZWdhTWVudUNvbnRlbnRfX3RnX2VmXCIsXG5cdFwibGFyZ2VEZXZpY2VTcGFjaW5nXCI6IFwiSGVhZGVyX2xhcmdlRGV2aWNlU3BhY2luZ19fU3BzaWJcIixcblx0XCJtZWdhbWVudVwiOiBcIkhlYWRlcl9tZWdhbWVudV9fTjlnTDhcIixcblx0XCJsYXRlc3RCbG9nV3JhcHBlclwiOiBcIkhlYWRlcl9sYXRlc3RCbG9nV3JhcHBlcl9fYmdYNmFcIixcblx0XCJibG9nSGVhZGluZ1wiOiBcIkhlYWRlcl9ibG9nSGVhZGluZ19faGN1Z1RcIixcblx0XCJibG9nVGl0bGVcIjogXCJIZWFkZXJfYmxvZ1RpdGxlX18wbWJjdlwiLFxuXHRcImJsb2dDVEFMaW5rXCI6IFwiSGVhZGVyX2Jsb2dDVEFMaW5rX19oSzFKNFwiLFxuXHRcImN0YUxpbmtcIjogXCJIZWFkZXJfY3RhTGlua19fVWVLd1BcIixcblx0XCJicmFuZHNCdXR0b25cIjogXCJIZWFkZXJfYnJhbmRzQnV0dG9uX19qTHBLNFwiLFxuXHRcImZsZXhEaXJlY3Rpb25Db2x1bW5cIjogXCJIZWFkZXJfZmxleERpcmVjdGlvbkNvbHVtbl9fUEd4cGFcIixcblx0XCJhY2NvcmRpb24tYm9keVwiOiBcIkhlYWRlcl9hY2NvcmRpb24tYm9keV9fTjR0cFNcIixcblx0XCJhbGxfc2VydmljZV9idXR0b25cIjogXCJIZWFkZXJfYWxsX3NlcnZpY2VfYnV0dG9uX185M003TFwiLFxuXHRcIm10ZWNoX2xvZ29cIjogXCJIZWFkZXJfbXRlY2hfbG9nb19fQjdhUHRcIixcblx0XCJib3R0b21fYm9yZGVyXCI6IFwiSGVhZGVyX2JvdHRvbV9ib3JkZXJfXzAzNXNtXCIsXG5cdFwiaGlkZGVuX292ZXJyaWRlXCI6IFwiSGVhZGVyX2hpZGRlbl9vdmVycmlkZV9fMFhfck5cIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiZGRmMzU0NTlmNzEwXCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header/Header.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/Heading/Heading.module.css":
/*!***************************************************!*\
  !*** ./src/components/Heading/Heading.module.css ***!
  \***************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"center\": \"Heading_center__XBGsG\",\n\t\"left\": \"Heading_left__ouHog\",\n\t\"right\": \"Heading_right__jsN_Y\"\n};\n\nmodule.exports.__checksum = \"23e2ebc8dadb\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9IZWFkaW5nL0hlYWRpbmcubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9zcmMvY29tcG9uZW50cy9IZWFkaW5nL0hlYWRpbmcubW9kdWxlLmNzcz9lMWE0Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImNlbnRlclwiOiBcIkhlYWRpbmdfY2VudGVyX19YQkdzR1wiLFxuXHRcImxlZnRcIjogXCJIZWFkaW5nX2xlZnRfX291SG9nXCIsXG5cdFwicmlnaHRcIjogXCJIZWFkaW5nX3JpZ2h0X19qc05fWVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCIyM2UyZWJjOGRhZGJcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Heading/Heading.module.css\n");

/***/ }),

/***/ "(ssr)/./src/styles/breakpoints.module.css":
/*!*******************************************!*\
  !*** ./src/styles/breakpoints.module.css ***!
  \*******************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"breakpoint-xs\": \"0\",\n\t\"breakpoint-sm-195\": \"195px\",\n\t\"breakpoint-sm-270\": \"270px\",\n\t\"breakpoint-sm-200\": \"200px\",\n\t\"breakpoint-sm-320\": \"320px\",\n\t\"breakpoint-sm-326\": \"326px\",\n\t\"breakpoint-sm-390\": \"390px\",\n\t\"breakpoint-sm-367\": \"367px\",\n\t\"breakpoint-sm-365\": \"365px\",\n\t\"breakpoint-sm-340\": \"340px\",\n\t\"breakpoint-sm-350\": \"350px\",\n\t\"breakpoint-sm-370\": \"370px\",\n\t\"breakpoint-sm-380\": \"380px\",\n\t\"breakpoint-sm-424\": \"424px\",\n\t\"breakpoint-sm-427\": \"427px\",\n\t\"breakpoint-sm-420\": \"420px\",\n\t\"breakpoint-sm-430\": \"430px\",\n\t\"breakpoint-sm-450\": \"450px\",\n\t\"breakpoint-sm-460\": \"460px\",\n\t\"breakpoint-sm-484\": \"484px\",\n\t\"breakpoint-sm-480\": \"480px\",\n\t\"breakpoint-sm-532\": \"532px\",\n\t\"breakpoint-sm-550\": \"550px\",\n\t\"breakpoint-sm\": \"576px\",\n\t\"breakpoint-md-579\": \"579px\",\n\t\"breakpoint-md-585\": \"585px\",\n\t\"breakpoint-md-767\": \"767px\",\n\t\"breakpoint-md\": \"768px\",\n\t\"breakpoint-md-769\": \"769px\",\n\t\"breakpoint-md-820\": \"820px\",\n\t\"breakpoint-md-850\": \"850px\",\n\t\"breakpoint-lg-901\": \"901px\",\n\t\"breakpoint-lg\": \"992px\",\n\t\"breakpoint-lg-991px\": \"991px\",\n\t\"breakpoint-xl-1024\": \"1024px\",\n\t\"breakpoint-xl-1051\": \"1051px\",\n\t\"breakpoint-xl-1208\": \"1208px\",\n\t\"breakpoint-xl-1023\": \"1023px\",\n\t\"breakpoint-xl-1199\": \"1199px\",\n\t\"breakpoint-xl-1188\": \"1188px\",\n\t\"breakpoint-xl\": \"1200px\",\n\t\"breakpoint-xl-1365\": \"1365px\",\n\t\"breakpoint-xl-1366\": \"1366px\",\n\t\"breakpoint-xl-1309\": \"1309px\",\n\t\"breakpoint-xl-1400\": \"1400px\",\n\t\"breakpoint-xl-1439\": \"1439px\",\n\t\"breakpoint-xl-1440\": \"1440px\",\n\t\"breakpoint-xl-1405\": \"1405px\",\n\t\"breakpoint-xl-1406\": \"1406px\",\n\t\"breakpoint-xl-1600\": \"1600px\",\n\t\"breakpoint-xl-1800\": \"1800px\",\n\t\"breakpoint-xl-2000\": \"2000px\",\n\t\"breakpoint-xl-2100\": \"2100px\",\n\t\"breakpoint-xl-2442\": \"2442px\",\n\t\"breakpoint-xl-2559\": \"2559px\",\n\t\"breakpoint-xl-2560\": \"2560px\"\n};\n\nmodule.exports.__checksum = \"692ee0ef07c5\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/styles/breakpoints.module.css\n");

/***/ }),

/***/ "(ssr)/./src/styles/typography.module.css":
/*!******************************************!*\
  !*** ./src/styles/typography.module.css ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Imports\nvar ___CSS_LOADER_ICSS_IMPORT_0___ = __webpack_require__(/*! -!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./variables.module.css */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/variables.module.css\");\nvar ___CSS_LOADER_ICSS_IMPORT_1___ = __webpack_require__(/*! -!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./breakpoints.module.css */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./src/styles/breakpoints.module.css\");\n// Exports\nmodule.exports = {\n\t\"variables\": \"\\\"./variables.module.css\\\"\",\n\t\"h1FontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h1FontSize\"] + \"\",\n\t\"h1MobileFontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h1MobileFontSize\"] + \"\",\n\t\"h2FontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h2FontSize\"] + \"\",\n\t\"h2MobileFontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h2MobileFontSize\"] + \"\",\n\t\"h3FontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h3FontSize\"] + \"\",\n\t\"h3MobileFontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h3MobileFontSize\"] + \"\",\n\t\"h4FontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h4FontSize\"] + \"\",\n\t\"h4MobileFontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h4MobileFontSize\"] + \"\",\n\t\"h5FontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h5FontSize\"] + \"\",\n\t\"h5MobileFontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h5MobileFontSize\"] + \"\",\n\t\"h6FontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h6FontSize\"] + \"\",\n\t\"h6MobileFontSize\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"h6MobileFontSize\"] + \"\",\n\t\"fontWeight600\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"fontWeight600\"] + \"\",\n\t\"fontWeight700\": \"\" + ___CSS_LOADER_ICSS_IMPORT_0___[\"fontWeight700\"] + \"\",\n\t\"breakPoints\": \"\\\"./breakpoints.module.css\\\"\",\n\t\"breakpoint-sm-450\": \"\" + ___CSS_LOADER_ICSS_IMPORT_1___[\"breakpoint-sm-450\"] + \"\",\n\t\"h1\": \"typography_h1__DecPZ\",\n\t\"h2\": \"typography_h2__Dn0zf\",\n\t\"h3\": \"typography_h3__o3Abb\",\n\t\"h4\": \"typography_h4__lGrWj\",\n\t\"h5\": \"typography_h5__DGJHL\",\n\t\"h6\": \"typography_h6__vf_A0\",\n\t\"caption\": \"typography_caption__hfk0A\"\n};\n\nmodule.exports.__checksum = \"644a8cd0b0fd\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/styles/typography.module.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_weight_100_200_300_400_500_600_700_800_900_subsets_latin_display_swap_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"weight\":[\"100\",\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"100\\\",\\\"200\\\",\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_weight_100_200_300_400_500_600_700_800_900_subsets_latin_display_swap_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_weight_100_200_300_400_500_600_700_800_900_subsets_latin_display_swap_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(rsc)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _components_BootstrapClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @components/BootstrapClient */ \"(rsc)/./src/components/BootstrapClient/index.ts\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/Header */ \"(rsc)/./src/components/Header/index.ts\");\n/* harmony import */ var bootstrap_dist_css_bootstrap_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! bootstrap/dist/css/bootstrap.css */ \"(rsc)/./node_modules/bootstrap/dist/css/bootstrap.css\");\n/* harmony import */ var _styles_base_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @styles/base.css */ \"(rsc)/./src/styles/base.css\");\n/* harmony import */ var _components_GTM__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @components/GTM */ \"(rsc)/./src/components/GTM/index.ts\");\n/* harmony import */ var _utils_fetchFromStrapi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @utils/fetchFromStrapi */ \"(rsc)/./src/utils/fetchFromStrapi.ts\");\n\n\n\n\n\n\n\n\n\n\nconst CookieConsentBannerDynamic = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_rsc_src_components_CookiesConsentBanner_index_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @components/CookiesConsentBanner */ \"(rsc)/./src/components/CookiesConsentBanner/index.ts\")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx -> \" + \"@components/CookiesConsentBanner\"\n        ]\n    },\n    ssr: false\n});\nconst CircularButtonWithArrowDynamic = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_rsc_src_components_CircularButtonWithArrow_index_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @components/CircularButtonWithArrow */ \"(rsc)/./src/components/CircularButtonWithArrow/index.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx -> \" + \"@components/CircularButtonWithArrow\"\n        ]\n    }\n});\nconst FooterDynamic = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_rsc_src_components_Footer_index_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @components/Footer */ \"(rsc)/./src/components/Footer/index.ts\")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx -> \" + \"@components/Footer\"\n        ]\n    }\n});\nconst metadata = {\n    icons: {\n        icon: `${\"https://cdn.marutitech.com\"}/marutitech_logo_fe5e4e6162.svg`\n    },\n    verification: {\n        google: `${\"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY\"}`\n    }\n};\nasync function getHeaderData() {\n    const query = `populate=logo&populate=logo.image&populate=menu.subMenu&populate=menu.subMenu.image&populate=menu.subLinks&populate=menu.button&populate=menu.titleDescription`;\n    return await (0,_utils_fetchFromStrapi__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(\"header\", query);\n}\nasync function fetchFooterData() {\n    const query = `populate=sector_row.Sublinks,pages_row.Sublinks,terms_and_condition_section,company_logo_section.image,company_logo_section.Copyright,company_logo_section.social_platforms.image`;\n    return await (0,_utils_fetchFromStrapi__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(\"footer\", query);\n}\nasync function RootLayout({ children }) {\n    const headerData = getHeaderData();\n    const footerData = fetchFooterData();\n    const [header, footer] = await Promise.all([\n        headerData,\n        footerData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GTM__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://api.ipify.org\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://ipwhois.app\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_weight_100_200_300_400_500_600_700_800_900_subsets_latin_display_swap_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_9___default().className),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CookieConsentBannerDynamic, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        headerData: header\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterDynamic, {\n                        footerData: footer\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"scroll_to_top\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircularButtonWithArrowDynamic, {\n                            variant: \"scroll_to_top\",\n                            scroll_to: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BootstrapClient__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\app\not-found.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/BootstrapClient/BootstrapClient.tsx":
/*!************************************************************!*\
  !*** ./src/components/BootstrapClient/BootstrapClient.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\BootstrapClient\BootstrapClient.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/BootstrapClient/index.ts":
/*!*************************************************!*\
  !*** ./src/components/BootstrapClient/index.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _BootstrapClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _BootstrapClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BootstrapClient */ \"(rsc)/./src/components/BootstrapClient/BootstrapClient.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9Cb290c3RyYXBDbGllbnQvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL3NyYy9jb21wb25lbnRzL0Jvb3RzdHJhcENsaWVudC9pbmRleC50cz8wY2JiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tICcuL0Jvb3RzdHJhcENsaWVudCc7XHJcbiJdLCJuYW1lcyI6WyJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/BootstrapClient/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/GTM/GTM.tsx":
/*!************************************!*\
  !*** ./src/components/GTM/GTM.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n// components/GTM.js\n\n\nconst GTM_ID = \"GTM-M53BRHCX\";\nconst GTM = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                id: \"gtm-script\",\n                strategy: \"lazyOnload\",\n                dangerouslySetInnerHTML: {\n                    __html: `\r\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\r\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\r\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\r\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\r\n            })(window,document,'script','dataLayer','${GTM_ID}');\r\n          `\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\GTM\\\\GTM.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noscript\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                    src: `https://www.googletagmanager.com/ns.html?id=${GTM_ID}`,\n                    height: \"0\",\n                    width: \"0\",\n                    loading: \"lazy\",\n                    style: {\n                        display: \"none\",\n                        visibility: \"hidden\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\GTM\\\\GTM.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\GTM\\\\GTM.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GTM);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/GTM/GTM.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/GTM/index.ts":
/*!*************************************!*\
  !*** ./src/components/GTM/index.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _GTM__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _GTM__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GTM */ \"(rsc)/./src/components/GTM/GTM.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9HVE0vaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL3NyYy9jb21wb25lbnRzL0dUTS9pbmRleC50cz83MDViIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tICcuL0dUTSc7XHJcbiJdLCJuYW1lcyI6WyJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/GTM/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/Header/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Header/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\Header\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Header/index.ts":
/*!****************************************!*\
  !*** ./src/components/Header/index.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _Header__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Header */ \"(rsc)/./src/components/Header/Header.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9IZWFkZXIvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBbUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL3NyYy9jb21wb25lbnRzL0hlYWRlci9pbmRleC50cz83Yzk2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tICcuL0hlYWRlcic7XHJcbiJdLCJuYW1lcyI6WyJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Header/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/fetchFromStrapi.ts":
/*!**************************************!*\
  !*** ./src/utils/fetchFromStrapi.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ fetchFromStrapi)\n/* harmony export */ });\nasync function fetchFromStrapi(endpoint, queryString = \"\") {\n    const baseUrl = \"https://dev-content.marutitech.com\";\n    try {\n        const url = `${baseUrl}/api/${endpoint}${queryString ? `?${queryString}` : \"\"}`;\n        const response = await fetch(url, {\n            method: \"GET\",\n            cache: \"force-cache\"\n        });\n        if (!response.ok) {\n            throw new Error(`Strapi fetch error: ${response.status} - ${response.statusText}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(`Failed to fetch ${endpoint}:`, error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/fetchFromStrapi.ts\n");

/***/ }),

/***/ "(ssr)/./src/public/arrow-right.svg":
/*!************************************!*\
  !*** ./src/public/arrow-right.svg ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/arrow-right.f4a53bdf.svg\",\"height\":24,\"width\":24,\"blurWidth\":0,\"blurHeight\":0});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHVibGljL2Fycm93LXJpZ2h0LnN2ZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyx5R0FBeUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL3NyYy9wdWJsaWMvYXJyb3ctcmlnaHQuc3ZnPzA0YzciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Fycm93LXJpZ2h0LmY0YTUzYmRmLnN2Z1wiLFwiaGVpZ2h0XCI6MjQsXCJ3aWR0aFwiOjI0LFwiYmx1cldpZHRoXCI6MCxcImJsdXJIZWlnaHRcIjowfTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/public/arrow-right.svg\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bootstrap","vendor-chunks/@restart","vendor-chunks/react-bootstrap","vendor-chunks/prop-types","vendor-chunks/react-transition-group","vendor-chunks/react-cookie-consent","vendor-chunks/uncontrollable","vendor-chunks/dom-helpers","vendor-chunks/react-is","vendor-chunks/react-lifecycles-compat","vendor-chunks/js-cookie","vendor-chunks/prop-types-extra","vendor-chunks/object-assign","vendor-chunks/@swc","vendor-chunks/classnames","vendor-chunks/invariant","vendor-chunks/@babel"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();