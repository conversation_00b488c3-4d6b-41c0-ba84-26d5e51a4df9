"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel-fade";
exports.ids = ["vendor-chunks/embla-carousel-fade"];
exports.modules = {

/***/ "(ssr)/./node_modules/embla-carousel-fade/esm/embla-carousel-fade.esm.js":
/*!*************************************************************************!*\
  !*** ./node_modules/embla-carousel-fade/esm/embla-carousel-fade.esm.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Fade)\n/* harmony export */ });\nfunction clampNumber(number, min, max) {\n    return Math.min(Math.max(number, min), max);\n}\nfunction isNumber(value) {\n    return typeof value === \"number\" && !isNaN(value);\n}\nfunction Fade(userOptions = {}) {\n    const fullOpacity = 1;\n    const noOpacity = 0;\n    const fadeFriction = 0.68;\n    let emblaApi;\n    let opacities = [];\n    let fadeToNextDistance;\n    let distanceFromPointerDown = 0;\n    let fadeVelocity = 0;\n    let progress = 0;\n    let shouldFadePair = false;\n    let defaultSettledBehaviour;\n    let defaultProgressBehaviour;\n    function init(emblaApiInstance) {\n        emblaApi = emblaApiInstance;\n        const selectedSnap = emblaApi.selectedScrollSnap();\n        const { scrollBody, containerRect, axis } = emblaApi.internalEngine();\n        const containerSize = axis.measureSize(containerRect);\n        fadeToNextDistance = clampNumber(containerSize * 0.75, 200, 500);\n        shouldFadePair = false;\n        opacities = emblaApi.scrollSnapList().map((_, index)=>index === selectedSnap ? fullOpacity : noOpacity);\n        defaultSettledBehaviour = scrollBody.settled;\n        defaultProgressBehaviour = emblaApi.scrollProgress;\n        scrollBody.settled = settled;\n        emblaApi.scrollProgress = scrollProgress;\n        emblaApi.on(\"select\", select).on(\"slideFocus\", fadeToSelectedSnapInstantly).on(\"pointerDown\", pointerDown).on(\"pointerUp\", pointerUp);\n        disableScroll();\n        fadeToSelectedSnapInstantly();\n    }\n    function destroy() {\n        const { scrollBody } = emblaApi.internalEngine();\n        scrollBody.settled = defaultSettledBehaviour;\n        emblaApi.scrollProgress = defaultProgressBehaviour;\n        emblaApi.off(\"select\", select).off(\"slideFocus\", fadeToSelectedSnapInstantly).off(\"pointerDown\", pointerDown).off(\"pointerUp\", pointerUp);\n        emblaApi.slideNodes().forEach((slideNode)=>{\n            const slideStyle = slideNode.style;\n            slideStyle.opacity = \"\";\n            slideStyle.transform = \"\";\n            slideStyle.pointerEvents = \"\";\n            if (!slideNode.getAttribute(\"style\")) slideNode.removeAttribute(\"style\");\n        });\n    }\n    function fadeToSelectedSnapInstantly() {\n        const selectedSnap = emblaApi.selectedScrollSnap();\n        setOpacities(selectedSnap, fullOpacity);\n    }\n    function pointerUp() {\n        shouldFadePair = false;\n    }\n    function pointerDown() {\n        shouldFadePair = false;\n        distanceFromPointerDown = 0;\n        fadeVelocity = 0;\n    }\n    function select() {\n        const duration = emblaApi.internalEngine().scrollBody.duration();\n        fadeVelocity = duration ? 0 : fullOpacity;\n        shouldFadePair = true;\n        if (!duration) fadeToSelectedSnapInstantly();\n    }\n    function getSlideTransform(position) {\n        const { axis } = emblaApi.internalEngine();\n        const translateAxis = axis.scroll.toUpperCase();\n        return `translate${translateAxis}(${axis.direction(position)}px)`;\n    }\n    function disableScroll() {\n        const { translate, slideLooper } = emblaApi.internalEngine();\n        translate.clear();\n        translate.toggleActive(false);\n        slideLooper.loopPoints.forEach(({ translate })=>{\n            translate.clear();\n            translate.toggleActive(false);\n        });\n    }\n    function lockExcessiveScroll(fadeIndex) {\n        const { scrollSnaps, location, target } = emblaApi.internalEngine();\n        if (!isNumber(fadeIndex) || opacities[fadeIndex] < 0.5) return;\n        location.set(scrollSnaps[fadeIndex]);\n        target.set(location);\n    }\n    function setOpacities(fadeIndex, velocity) {\n        const scrollSnaps = emblaApi.scrollSnapList();\n        scrollSnaps.forEach((_, indexA)=>{\n            const absVelocity = Math.abs(velocity);\n            const currentOpacity = opacities[indexA];\n            const isFadeIndex = indexA === fadeIndex;\n            const nextOpacity = isFadeIndex ? currentOpacity + absVelocity : currentOpacity - absVelocity;\n            const clampedOpacity = clampNumber(nextOpacity, noOpacity, fullOpacity);\n            opacities[indexA] = clampedOpacity;\n            const fadePair = isFadeIndex && shouldFadePair;\n            const indexB = emblaApi.previousScrollSnap();\n            if (fadePair) opacities[indexB] = 1 - clampedOpacity;\n            if (isFadeIndex) setProgress(fadeIndex, clampedOpacity);\n            setOpacity(indexA);\n        });\n    }\n    function setOpacity(index) {\n        const slidesInSnap = emblaApi.internalEngine().slideRegistry[index];\n        const { scrollSnaps, containerRect } = emblaApi.internalEngine();\n        const opacity = opacities[index];\n        slidesInSnap.forEach((slideIndex)=>{\n            const slideStyle = emblaApi.slideNodes()[slideIndex].style;\n            const roundedOpacity = parseFloat(opacity.toFixed(2));\n            const hasOpacity = roundedOpacity > noOpacity;\n            const position = hasOpacity ? scrollSnaps[index] : containerRect.width + 2;\n            const transform = getSlideTransform(position);\n            if (hasOpacity) slideStyle.transform = transform;\n            slideStyle.opacity = roundedOpacity.toString();\n            slideStyle.pointerEvents = opacity > 0.5 ? \"auto\" : \"none\";\n            if (!hasOpacity) slideStyle.transform = transform;\n        });\n    }\n    function setProgress(fadeIndex, opacity) {\n        const { index, dragHandler, scrollSnaps } = emblaApi.internalEngine();\n        const pointerDown = dragHandler.pointerDown();\n        const snapFraction = 1 / (scrollSnaps.length - 1);\n        let indexA = fadeIndex;\n        let indexB = pointerDown ? emblaApi.selectedScrollSnap() : emblaApi.previousScrollSnap();\n        if (pointerDown && indexA === indexB) {\n            const reverseSign = Math.sign(distanceFromPointerDown) * -1;\n            indexA = indexB;\n            indexB = index.clone().set(indexB).add(reverseSign).get();\n        }\n        const currentPosition = indexB * snapFraction;\n        const diffPosition = (indexA - indexB) * snapFraction;\n        progress = currentPosition + diffPosition * opacity;\n    }\n    function getFadeIndex() {\n        const { dragHandler, index, scrollBody } = emblaApi.internalEngine();\n        const selectedSnap = emblaApi.selectedScrollSnap();\n        if (!dragHandler.pointerDown()) return selectedSnap;\n        const directionSign = Math.sign(scrollBody.velocity());\n        const distanceSign = Math.sign(distanceFromPointerDown);\n        const nextSnap = index.clone().set(selectedSnap).add(directionSign * -1).get();\n        if (!directionSign || !distanceSign) return null;\n        return distanceSign === directionSign ? nextSnap : selectedSnap;\n    }\n    const fade = (emblaApi)=>{\n        const { dragHandler, scrollBody } = emblaApi.internalEngine();\n        const pointerDown = dragHandler.pointerDown();\n        const velocity = scrollBody.velocity();\n        const duration = scrollBody.duration();\n        const fadeIndex = getFadeIndex();\n        const noFadeIndex = !isNumber(fadeIndex);\n        if (pointerDown) {\n            if (!velocity) return;\n            distanceFromPointerDown += velocity;\n            fadeVelocity = Math.abs(velocity / fadeToNextDistance);\n            lockExcessiveScroll(fadeIndex);\n        }\n        if (!pointerDown) {\n            if (!duration || noFadeIndex) return;\n            fadeVelocity += (fullOpacity - opacities[fadeIndex]) / duration;\n            fadeVelocity *= fadeFriction;\n        }\n        if (noFadeIndex) return;\n        setOpacities(fadeIndex, fadeVelocity);\n    };\n    function settled() {\n        const { target, location } = emblaApi.internalEngine();\n        const diffToTarget = target.get() - location.get();\n        const notReachedTarget = Math.abs(diffToTarget) >= 1;\n        const fadeIndex = getFadeIndex();\n        const noFadeIndex = !isNumber(fadeIndex);\n        fade(emblaApi);\n        if (noFadeIndex || notReachedTarget) return false;\n        return opacities[fadeIndex] > 0.999;\n    }\n    function scrollProgress() {\n        return progress;\n    }\n    const self = {\n        name: \"fade\",\n        options: userOptions,\n        init,\n        destroy\n    };\n    return self;\n}\nFade.globalOptions = undefined;\n //# sourceMappingURL=embla-carousel-fade.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/embla-carousel-fade/esm/embla-carousel-fade.esm.js\n");

/***/ })

};
;