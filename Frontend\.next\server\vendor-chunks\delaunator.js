"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/delaunator";
exports.ids = ["vendor-chunks/delaunator"];
exports.modules = {

/***/ "(ssr)/./node_modules/delaunator/index.js":
/*!******************************************!*\
  !*** ./node_modules/delaunator/index.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Delaunator)\n/* harmony export */ });\n/* harmony import */ var robust_predicates__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! robust-predicates */ \"(ssr)/./node_modules/robust-predicates/index.js\");\nconst EPSILON = Math.pow(2, -52);\nconst EDGE_STACK = new Uint32Array(512);\n\nclass Delaunator {\n    static from(points, getX = defaultGetX, getY = defaultGetY) {\n        const n = points.length;\n        const coords = new Float64Array(n * 2);\n        for(let i = 0; i < n; i++){\n            const p = points[i];\n            coords[2 * i] = getX(p);\n            coords[2 * i + 1] = getY(p);\n        }\n        return new Delaunator(coords);\n    }\n    constructor(coords){\n        const n = coords.length >> 1;\n        if (n > 0 && typeof coords[0] !== \"number\") throw new Error(\"Expected coords to contain numbers.\");\n        this.coords = coords;\n        // arrays that will store the triangulation graph\n        const maxTriangles = Math.max(2 * n - 5, 0);\n        this._triangles = new Uint32Array(maxTriangles * 3);\n        this._halfedges = new Int32Array(maxTriangles * 3);\n        // temporary arrays for tracking the edges of the advancing convex hull\n        this._hashSize = Math.ceil(Math.sqrt(n));\n        this._hullPrev = new Uint32Array(n); // edge to prev edge\n        this._hullNext = new Uint32Array(n); // edge to next edge\n        this._hullTri = new Uint32Array(n); // edge to adjacent triangle\n        this._hullHash = new Int32Array(this._hashSize); // angular edge hash\n        // temporary arrays for sorting points\n        this._ids = new Uint32Array(n);\n        this._dists = new Float64Array(n);\n        this.update();\n    }\n    update() {\n        const { coords, _hullPrev: hullPrev, _hullNext: hullNext, _hullTri: hullTri, _hullHash: hullHash } = this;\n        const n = coords.length >> 1;\n        // populate an array of point indices; calculate input data bbox\n        let minX = Infinity;\n        let minY = Infinity;\n        let maxX = -Infinity;\n        let maxY = -Infinity;\n        for(let i = 0; i < n; i++){\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n            this._ids[i] = i;\n        }\n        const cx = (minX + maxX) / 2;\n        const cy = (minY + maxY) / 2;\n        let i0, i1, i2;\n        // pick a seed point close to the center\n        for(let i = 0, minDist = Infinity; i < n; i++){\n            const d = dist(cx, cy, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist) {\n                i0 = i;\n                minDist = d;\n            }\n        }\n        const i0x = coords[2 * i0];\n        const i0y = coords[2 * i0 + 1];\n        // find the point closest to the seed\n        for(let i = 0, minDist = Infinity; i < n; i++){\n            if (i === i0) continue;\n            const d = dist(i0x, i0y, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist && d > 0) {\n                i1 = i;\n                minDist = d;\n            }\n        }\n        let i1x = coords[2 * i1];\n        let i1y = coords[2 * i1 + 1];\n        let minRadius = Infinity;\n        // find the third point which forms the smallest circumcircle with the first two\n        for(let i = 0; i < n; i++){\n            if (i === i0 || i === i1) continue;\n            const r = circumradius(i0x, i0y, i1x, i1y, coords[2 * i], coords[2 * i + 1]);\n            if (r < minRadius) {\n                i2 = i;\n                minRadius = r;\n            }\n        }\n        let i2x = coords[2 * i2];\n        let i2y = coords[2 * i2 + 1];\n        if (minRadius === Infinity) {\n            // order collinear points by dx (or dy if all x are identical)\n            // and return the list as a hull\n            for(let i = 0; i < n; i++){\n                this._dists[i] = coords[2 * i] - coords[0] || coords[2 * i + 1] - coords[1];\n            }\n            quicksort(this._ids, this._dists, 0, n - 1);\n            const hull = new Uint32Array(n);\n            let j = 0;\n            for(let i = 0, d0 = -Infinity; i < n; i++){\n                const id = this._ids[i];\n                const d = this._dists[id];\n                if (d > d0) {\n                    hull[j++] = id;\n                    d0 = d;\n                }\n            }\n            this.hull = hull.subarray(0, j);\n            this.triangles = new Uint32Array(0);\n            this.halfedges = new Uint32Array(0);\n            return;\n        }\n        // swap the order of the seed points for counter-clockwise orientation\n        if ((0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(i0x, i0y, i1x, i1y, i2x, i2y) < 0) {\n            const i = i1;\n            const x = i1x;\n            const y = i1y;\n            i1 = i2;\n            i1x = i2x;\n            i1y = i2y;\n            i2 = i;\n            i2x = x;\n            i2y = y;\n        }\n        const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);\n        this._cx = center.x;\n        this._cy = center.y;\n        for(let i = 0; i < n; i++){\n            this._dists[i] = dist(coords[2 * i], coords[2 * i + 1], center.x, center.y);\n        }\n        // sort the points by distance from the seed triangle circumcenter\n        quicksort(this._ids, this._dists, 0, n - 1);\n        // set up the seed triangle as the starting hull\n        this._hullStart = i0;\n        let hullSize = 3;\n        hullNext[i0] = hullPrev[i2] = i1;\n        hullNext[i1] = hullPrev[i0] = i2;\n        hullNext[i2] = hullPrev[i1] = i0;\n        hullTri[i0] = 0;\n        hullTri[i1] = 1;\n        hullTri[i2] = 2;\n        hullHash.fill(-1);\n        hullHash[this._hashKey(i0x, i0y)] = i0;\n        hullHash[this._hashKey(i1x, i1y)] = i1;\n        hullHash[this._hashKey(i2x, i2y)] = i2;\n        this.trianglesLen = 0;\n        this._addTriangle(i0, i1, i2, -1, -1, -1);\n        for(let k = 0, xp, yp; k < this._ids.length; k++){\n            const i = this._ids[k];\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n            // skip near-duplicate points\n            if (k > 0 && Math.abs(x - xp) <= EPSILON && Math.abs(y - yp) <= EPSILON) continue;\n            xp = x;\n            yp = y;\n            // skip seed triangle points\n            if (i === i0 || i === i1 || i === i2) continue;\n            // find a visible edge on the convex hull using edge hash\n            let start = 0;\n            for(let j = 0, key = this._hashKey(x, y); j < this._hashSize; j++){\n                start = hullHash[(key + j) % this._hashSize];\n                if (start !== -1 && start !== hullNext[start]) break;\n            }\n            start = hullPrev[start];\n            let e = start, q;\n            while(q = hullNext[e], (0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(x, y, coords[2 * e], coords[2 * e + 1], coords[2 * q], coords[2 * q + 1]) >= 0){\n                e = q;\n                if (e === start) {\n                    e = -1;\n                    break;\n                }\n            }\n            if (e === -1) continue; // likely a near-duplicate point; skip it\n            // add the first triangle from the point\n            let t = this._addTriangle(e, i, hullNext[e], -1, -1, hullTri[e]);\n            // recursively flip triangles from the point until they satisfy the Delaunay condition\n            hullTri[i] = this._legalize(t + 2);\n            hullTri[e] = t; // keep track of boundary triangles on the hull\n            hullSize++;\n            // walk forward through the hull, adding more triangles and flipping recursively\n            let n = hullNext[e];\n            while(q = hullNext[n], (0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(x, y, coords[2 * n], coords[2 * n + 1], coords[2 * q], coords[2 * q + 1]) < 0){\n                t = this._addTriangle(n, i, q, hullTri[i], -1, hullTri[n]);\n                hullTri[i] = this._legalize(t + 2);\n                hullNext[n] = n; // mark as removed\n                hullSize--;\n                n = q;\n            }\n            // walk backward from the other side, adding more triangles and flipping\n            if (e === start) {\n                while(q = hullPrev[e], (0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(x, y, coords[2 * q], coords[2 * q + 1], coords[2 * e], coords[2 * e + 1]) < 0){\n                    t = this._addTriangle(q, i, e, -1, hullTri[e], hullTri[q]);\n                    this._legalize(t + 2);\n                    hullTri[q] = t;\n                    hullNext[e] = e; // mark as removed\n                    hullSize--;\n                    e = q;\n                }\n            }\n            // update the hull indices\n            this._hullStart = hullPrev[i] = e;\n            hullNext[e] = hullPrev[n] = i;\n            hullNext[i] = n;\n            // save the two new edges in the hash table\n            hullHash[this._hashKey(x, y)] = i;\n            hullHash[this._hashKey(coords[2 * e], coords[2 * e + 1])] = e;\n        }\n        this.hull = new Uint32Array(hullSize);\n        for(let i = 0, e = this._hullStart; i < hullSize; i++){\n            this.hull[i] = e;\n            e = hullNext[e];\n        }\n        // trim typed triangle mesh arrays\n        this.triangles = this._triangles.subarray(0, this.trianglesLen);\n        this.halfedges = this._halfedges.subarray(0, this.trianglesLen);\n    }\n    _hashKey(x, y) {\n        return Math.floor(pseudoAngle(x - this._cx, y - this._cy) * this._hashSize) % this._hashSize;\n    }\n    _legalize(a) {\n        const { _triangles: triangles, _halfedges: halfedges, coords } = this;\n        let i = 0;\n        let ar = 0;\n        // recursion eliminated with a fixed-size stack\n        while(true){\n            const b = halfedges[a];\n            /* if the pair of triangles doesn't satisfy the Delaunay condition\n             * (p1 is inside the circumcircle of [p0, pl, pr]), flip them,\n             * then do the same check/flip recursively for the new pair of triangles\n             *\n             *           pl                    pl\n             *          /||\\                  /  \\\n             *       al/ || \\bl            al/    \\a\n             *        /  ||  \\              /      \\\n             *       /  a||b  \\    flip    /___ar___\\\n             *     p0\\   ||   /p1   =>   p0\\---bl---/p1\n             *        \\  ||  /              \\      /\n             *       ar\\ || /br             b\\    /br\n             *          \\||/                  \\  /\n             *           pr                    pr\n             */ const a0 = a - a % 3;\n            ar = a0 + (a + 2) % 3;\n            if (b === -1) {\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n                continue;\n            }\n            const b0 = b - b % 3;\n            const al = a0 + (a + 1) % 3;\n            const bl = b0 + (b + 2) % 3;\n            const p0 = triangles[ar];\n            const pr = triangles[a];\n            const pl = triangles[al];\n            const p1 = triangles[bl];\n            const illegal = inCircle(coords[2 * p0], coords[2 * p0 + 1], coords[2 * pr], coords[2 * pr + 1], coords[2 * pl], coords[2 * pl + 1], coords[2 * p1], coords[2 * p1 + 1]);\n            if (illegal) {\n                triangles[a] = p1;\n                triangles[b] = p0;\n                const hbl = halfedges[bl];\n                // edge swapped on the other side of the hull (rare); fix the halfedge reference\n                if (hbl === -1) {\n                    let e = this._hullStart;\n                    do {\n                        if (this._hullTri[e] === bl) {\n                            this._hullTri[e] = a;\n                            break;\n                        }\n                        e = this._hullPrev[e];\n                    }while (e !== this._hullStart);\n                }\n                this._link(a, hbl);\n                this._link(b, halfedges[ar]);\n                this._link(ar, bl);\n                const br = b0 + (b + 1) % 3;\n                // don't worry about hitting the cap: it can only happen on extremely degenerate input\n                if (i < EDGE_STACK.length) {\n                    EDGE_STACK[i++] = br;\n                }\n            } else {\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n            }\n        }\n        return ar;\n    }\n    _link(a, b) {\n        this._halfedges[a] = b;\n        if (b !== -1) this._halfedges[b] = a;\n    }\n    // add a new triangle given vertex indices and adjacent half-edge ids\n    _addTriangle(i0, i1, i2, a, b, c) {\n        const t = this.trianglesLen;\n        this._triangles[t] = i0;\n        this._triangles[t + 1] = i1;\n        this._triangles[t + 2] = i2;\n        this._link(t, a);\n        this._link(t + 1, b);\n        this._link(t + 2, c);\n        this.trianglesLen += 3;\n        return t;\n    }\n}\n// monotonically increases with real angle, but doesn't need expensive trigonometry\nfunction pseudoAngle(dx, dy) {\n    const p = dx / (Math.abs(dx) + Math.abs(dy));\n    return (dy > 0 ? 3 - p : 1 + p) / 4; // [0..1]\n}\nfunction dist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\nfunction inCircle(ax, ay, bx, by, cx, cy, px, py) {\n    const dx = ax - px;\n    const dy = ay - py;\n    const ex = bx - px;\n    const ey = by - py;\n    const fx = cx - px;\n    const fy = cy - py;\n    const ap = dx * dx + dy * dy;\n    const bp = ex * ex + ey * ey;\n    const cp = fx * fx + fy * fy;\n    return dx * (ey * cp - bp * fy) - dy * (ex * cp - bp * fx) + ap * (ex * fy - ey * fx) < 0;\n}\nfunction circumradius(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n    const x = (ey * bl - dy * cl) * d;\n    const y = (dx * cl - ex * bl) * d;\n    return x * x + y * y;\n}\nfunction circumcenter(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n    const x = ax + (ey * bl - dy * cl) * d;\n    const y = ay + (dx * cl - ex * bl) * d;\n    return {\n        x,\n        y\n    };\n}\nfunction quicksort(ids, dists, left, right) {\n    if (right - left <= 20) {\n        for(let i = left + 1; i <= right; i++){\n            const temp = ids[i];\n            const tempDist = dists[temp];\n            let j = i - 1;\n            while(j >= left && dists[ids[j]] > tempDist)ids[j + 1] = ids[j--];\n            ids[j + 1] = temp;\n        }\n    } else {\n        const median = left + right >> 1;\n        let i = left + 1;\n        let j = right;\n        swap(ids, median, i);\n        if (dists[ids[left]] > dists[ids[right]]) swap(ids, left, right);\n        if (dists[ids[i]] > dists[ids[right]]) swap(ids, i, right);\n        if (dists[ids[left]] > dists[ids[i]]) swap(ids, left, i);\n        const temp = ids[i];\n        const tempDist = dists[temp];\n        while(true){\n            do i++;\n            while (dists[ids[i]] < tempDist);\n            do j--;\n            while (dists[ids[j]] > tempDist);\n            if (j < i) break;\n            swap(ids, i, j);\n        }\n        ids[left + 1] = ids[j];\n        ids[j] = temp;\n        if (right - i + 1 >= j - left) {\n            quicksort(ids, dists, i, right);\n            quicksort(ids, dists, left, j - 1);\n        } else {\n            quicksort(ids, dists, left, j - 1);\n            quicksort(ids, dists, i, right);\n        }\n    }\n}\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\nfunction defaultGetX(p) {\n    return p[0];\n}\nfunction defaultGetY(p) {\n    return p[1];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/delaunator/index.js\n");

/***/ })

};
;