"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel";
exports.ids = ["vendor-chunks/embla-carousel"];
exports.modules = {

/***/ "(ssr)/./node_modules/embla-carousel/esm/embla-carousel.esm.js":
/*!***************************************************************!*\
  !*** ./node_modules/embla-carousel/esm/embla-carousel.esm.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmblaCarousel)\n/* harmony export */ });\nfunction isNumber(subject) {\n    return typeof subject === \"number\";\n}\nfunction isString(subject) {\n    return typeof subject === \"string\";\n}\nfunction isBoolean(subject) {\n    return typeof subject === \"boolean\";\n}\nfunction isObject(subject) {\n    return Object.prototype.toString.call(subject) === \"[object Object]\";\n}\nfunction mathAbs(n) {\n    return Math.abs(n);\n}\nfunction mathSign(n) {\n    return Math.sign(n);\n}\nfunction deltaAbs(valueB, valueA) {\n    return mathAbs(valueB - valueA);\n}\nfunction factorAbs(valueB, valueA) {\n    if (valueB === 0 || valueA === 0) return 0;\n    if (mathAbs(valueB) <= mathAbs(valueA)) return 0;\n    const diff = deltaAbs(mathAbs(valueB), mathAbs(valueA));\n    return mathAbs(diff / valueB);\n}\nfunction arrayKeys(array) {\n    return objectKeys(array).map(Number);\n}\nfunction arrayLast(array) {\n    return array[arrayLastIndex(array)];\n}\nfunction arrayLastIndex(array) {\n    return Math.max(0, array.length - 1);\n}\nfunction arrayIsLastIndex(array, index) {\n    return index === arrayLastIndex(array);\n}\nfunction arrayFromNumber(n, startAt = 0) {\n    return Array.from(Array(n), (_, i)=>startAt + i);\n}\nfunction objectKeys(object) {\n    return Object.keys(object);\n}\nfunction objectsMergeDeep(objectA, objectB) {\n    return [\n        objectA,\n        objectB\n    ].reduce((mergedObjects, currentObject)=>{\n        objectKeys(currentObject).forEach((key)=>{\n            const valueA = mergedObjects[key];\n            const valueB = currentObject[key];\n            const areObjects = isObject(valueA) && isObject(valueB);\n            mergedObjects[key] = areObjects ? objectsMergeDeep(valueA, valueB) : valueB;\n        });\n        return mergedObjects;\n    }, {});\n}\nfunction isMouseEvent(evt, ownerWindow) {\n    return typeof ownerWindow.MouseEvent !== \"undefined\" && evt instanceof ownerWindow.MouseEvent;\n}\nfunction Alignment(align, viewSize) {\n    const predefined = {\n        start,\n        center,\n        end\n    };\n    function start() {\n        return 0;\n    }\n    function center(n) {\n        return end(n) / 2;\n    }\n    function end(n) {\n        return viewSize - n;\n    }\n    function measure(n, index) {\n        if (isString(align)) return predefined[align](n);\n        return align(viewSize, n, index);\n    }\n    const self = {\n        measure\n    };\n    return self;\n}\nfunction EventStore() {\n    let listeners = [];\n    function add(node, type, handler, options = {\n        passive: true\n    }) {\n        let removeListener;\n        if (\"addEventListener\" in node) {\n            node.addEventListener(type, handler, options);\n            removeListener = ()=>node.removeEventListener(type, handler, options);\n        } else {\n            const legacyMediaQueryList = node;\n            legacyMediaQueryList.addListener(handler);\n            removeListener = ()=>legacyMediaQueryList.removeListener(handler);\n        }\n        listeners.push(removeListener);\n        return self;\n    }\n    function clear() {\n        listeners = listeners.filter((remove)=>remove());\n    }\n    const self = {\n        add,\n        clear\n    };\n    return self;\n}\nfunction Animations(ownerDocument, ownerWindow, update, render) {\n    const documentVisibleHandler = EventStore();\n    const timeStep = 1000 / 60;\n    let lastTimeStamp = null;\n    let lag = 0;\n    let animationFrame = 0;\n    function init() {\n        documentVisibleHandler.add(ownerDocument, \"visibilitychange\", ()=>{\n            if (ownerDocument.hidden) reset();\n        });\n    }\n    function destroy() {\n        stop();\n        documentVisibleHandler.clear();\n    }\n    function animate(timeStamp) {\n        if (!animationFrame) return;\n        if (!lastTimeStamp) lastTimeStamp = timeStamp;\n        const elapsed = timeStamp - lastTimeStamp;\n        lastTimeStamp = timeStamp;\n        lag += elapsed;\n        while(lag >= timeStep){\n            update();\n            lag -= timeStep;\n        }\n        const lagOffset = mathAbs(lag / timeStep);\n        render(lagOffset);\n        if (animationFrame) ownerWindow.requestAnimationFrame(animate);\n    }\n    function start() {\n        if (animationFrame) return;\n        animationFrame = ownerWindow.requestAnimationFrame(animate);\n    }\n    function stop() {\n        ownerWindow.cancelAnimationFrame(animationFrame);\n        lastTimeStamp = null;\n        lag = 0;\n        animationFrame = 0;\n    }\n    function reset() {\n        lastTimeStamp = null;\n        lag = 0;\n    }\n    const self = {\n        init,\n        destroy,\n        start,\n        stop,\n        update,\n        render\n    };\n    return self;\n}\nfunction Axis(axis, contentDirection) {\n    const isRightToLeft = contentDirection === \"rtl\";\n    const isVertical = axis === \"y\";\n    const scroll = isVertical ? \"y\" : \"x\";\n    const cross = isVertical ? \"x\" : \"y\";\n    const sign = !isVertical && isRightToLeft ? -1 : 1;\n    const startEdge = getStartEdge();\n    const endEdge = getEndEdge();\n    function measureSize(nodeRect) {\n        const { height, width } = nodeRect;\n        return isVertical ? height : width;\n    }\n    function getStartEdge() {\n        if (isVertical) return \"top\";\n        return isRightToLeft ? \"right\" : \"left\";\n    }\n    function getEndEdge() {\n        if (isVertical) return \"bottom\";\n        return isRightToLeft ? \"left\" : \"right\";\n    }\n    function direction(n) {\n        return n * sign;\n    }\n    const self = {\n        scroll,\n        cross,\n        startEdge,\n        endEdge,\n        measureSize,\n        direction\n    };\n    return self;\n}\nfunction Limit(min = 0, max = 0) {\n    const length = mathAbs(min - max);\n    function reachedMin(n) {\n        return n < min;\n    }\n    function reachedMax(n) {\n        return n > max;\n    }\n    function reachedAny(n) {\n        return reachedMin(n) || reachedMax(n);\n    }\n    function constrain(n) {\n        if (!reachedAny(n)) return n;\n        return reachedMin(n) ? min : max;\n    }\n    function removeOffset(n) {\n        if (!length) return n;\n        return n - length * Math.ceil((n - max) / length);\n    }\n    const self = {\n        length,\n        max,\n        min,\n        constrain,\n        reachedAny,\n        reachedMax,\n        reachedMin,\n        removeOffset\n    };\n    return self;\n}\nfunction Counter(max, start, loop) {\n    const { constrain } = Limit(0, max);\n    const loopEnd = max + 1;\n    let counter = withinLimit(start);\n    function withinLimit(n) {\n        return !loop ? constrain(n) : mathAbs((loopEnd + n) % loopEnd);\n    }\n    function get() {\n        return counter;\n    }\n    function set(n) {\n        counter = withinLimit(n);\n        return self;\n    }\n    function add(n) {\n        return clone().set(get() + n);\n    }\n    function clone() {\n        return Counter(max, get(), loop);\n    }\n    const self = {\n        get,\n        set,\n        add,\n        clone\n    };\n    return self;\n}\nfunction DragHandler(axis, rootNode, ownerDocument, ownerWindow, target, dragTracker, location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, baseFriction, watchDrag) {\n    const { cross: crossAxis, direction } = axis;\n    const focusNodes = [\n        \"INPUT\",\n        \"SELECT\",\n        \"TEXTAREA\"\n    ];\n    const nonPassiveEvent = {\n        passive: false\n    };\n    const initEvents = EventStore();\n    const dragEvents = EventStore();\n    const goToNextThreshold = Limit(50, 225).constrain(percentOfView.measure(20));\n    const snapForceBoost = {\n        mouse: 300,\n        touch: 400\n    };\n    const freeForceBoost = {\n        mouse: 500,\n        touch: 600\n    };\n    const baseSpeed = dragFree ? 43 : 25;\n    let isMoving = false;\n    let startScroll = 0;\n    let startCross = 0;\n    let pointerIsDown = false;\n    let preventScroll = false;\n    let preventClick = false;\n    let isMouse = false;\n    function init(emblaApi) {\n        if (!watchDrag) return;\n        function downIfAllowed(evt) {\n            if (isBoolean(watchDrag) || watchDrag(emblaApi, evt)) down(evt);\n        }\n        const node = rootNode;\n        initEvents.add(node, \"dragstart\", (evt)=>evt.preventDefault(), nonPassiveEvent).add(node, \"touchmove\", ()=>undefined, nonPassiveEvent).add(node, \"touchend\", ()=>undefined).add(node, \"touchstart\", downIfAllowed).add(node, \"mousedown\", downIfAllowed).add(node, \"touchcancel\", up).add(node, \"contextmenu\", up).add(node, \"click\", click, true);\n    }\n    function destroy() {\n        initEvents.clear();\n        dragEvents.clear();\n    }\n    function addDragEvents() {\n        const node = isMouse ? ownerDocument : rootNode;\n        dragEvents.add(node, \"touchmove\", move, nonPassiveEvent).add(node, \"touchend\", up).add(node, \"mousemove\", move, nonPassiveEvent).add(node, \"mouseup\", up);\n    }\n    function isFocusNode(node) {\n        const nodeName = node.nodeName || \"\";\n        return focusNodes.includes(nodeName);\n    }\n    function forceBoost() {\n        const boost = dragFree ? freeForceBoost : snapForceBoost;\n        const type = isMouse ? \"mouse\" : \"touch\";\n        return boost[type];\n    }\n    function allowedForce(force, targetChanged) {\n        const next = index.add(mathSign(force) * -1);\n        const baseForce = scrollTarget.byDistance(force, !dragFree).distance;\n        if (dragFree || mathAbs(force) < goToNextThreshold) return baseForce;\n        if (skipSnaps && targetChanged) return baseForce * 0.5;\n        return scrollTarget.byIndex(next.get(), 0).distance;\n    }\n    function down(evt) {\n        const isMouseEvt = isMouseEvent(evt, ownerWindow);\n        isMouse = isMouseEvt;\n        preventClick = dragFree && isMouseEvt && !evt.buttons && isMoving;\n        isMoving = deltaAbs(target.get(), location.get()) >= 2;\n        if (isMouseEvt && evt.button !== 0) return;\n        if (isFocusNode(evt.target)) return;\n        pointerIsDown = true;\n        dragTracker.pointerDown(evt);\n        scrollBody.useFriction(0).useDuration(0);\n        target.set(location);\n        addDragEvents();\n        startScroll = dragTracker.readPoint(evt);\n        startCross = dragTracker.readPoint(evt, crossAxis);\n        eventHandler.emit(\"pointerDown\");\n    }\n    function move(evt) {\n        const isTouchEvt = !isMouseEvent(evt, ownerWindow);\n        if (isTouchEvt && evt.touches.length >= 2) return up(evt);\n        const lastScroll = dragTracker.readPoint(evt);\n        const lastCross = dragTracker.readPoint(evt, crossAxis);\n        const diffScroll = deltaAbs(lastScroll, startScroll);\n        const diffCross = deltaAbs(lastCross, startCross);\n        if (!preventScroll && !isMouse) {\n            if (!evt.cancelable) return up(evt);\n            preventScroll = diffScroll > diffCross;\n            if (!preventScroll) return up(evt);\n        }\n        const diff = dragTracker.pointerMove(evt);\n        if (diffScroll > dragThreshold) preventClick = true;\n        scrollBody.useFriction(0.3).useDuration(0.75);\n        animation.start();\n        target.add(direction(diff));\n        evt.preventDefault();\n    }\n    function up(evt) {\n        const currentLocation = scrollTarget.byDistance(0, false);\n        const targetChanged = currentLocation.index !== index.get();\n        const rawForce = dragTracker.pointerUp(evt) * forceBoost();\n        const force = allowedForce(direction(rawForce), targetChanged);\n        const forceFactor = factorAbs(rawForce, force);\n        const speed = baseSpeed - 10 * forceFactor;\n        const friction = baseFriction + forceFactor / 50;\n        preventScroll = false;\n        pointerIsDown = false;\n        dragEvents.clear();\n        scrollBody.useDuration(speed).useFriction(friction);\n        scrollTo.distance(force, !dragFree);\n        isMouse = false;\n        eventHandler.emit(\"pointerUp\");\n    }\n    function click(evt) {\n        if (preventClick) {\n            evt.stopPropagation();\n            evt.preventDefault();\n            preventClick = false;\n        }\n    }\n    function pointerDown() {\n        return pointerIsDown;\n    }\n    const self = {\n        init,\n        destroy,\n        pointerDown\n    };\n    return self;\n}\nfunction DragTracker(axis, ownerWindow) {\n    const logInterval = 170;\n    let startEvent;\n    let lastEvent;\n    function readTime(evt) {\n        return evt.timeStamp;\n    }\n    function readPoint(evt, evtAxis) {\n        const property = evtAxis || axis.scroll;\n        const coord = `client${property === \"x\" ? \"X\" : \"Y\"}`;\n        return (isMouseEvent(evt, ownerWindow) ? evt : evt.touches[0])[coord];\n    }\n    function pointerDown(evt) {\n        startEvent = evt;\n        lastEvent = evt;\n        return readPoint(evt);\n    }\n    function pointerMove(evt) {\n        const diff = readPoint(evt) - readPoint(lastEvent);\n        const expired = readTime(evt) - readTime(startEvent) > logInterval;\n        lastEvent = evt;\n        if (expired) startEvent = evt;\n        return diff;\n    }\n    function pointerUp(evt) {\n        if (!startEvent || !lastEvent) return 0;\n        const diffDrag = readPoint(lastEvent) - readPoint(startEvent);\n        const diffTime = readTime(evt) - readTime(startEvent);\n        const expired = readTime(evt) - readTime(lastEvent) > logInterval;\n        const force = diffDrag / diffTime;\n        const isFlick = diffTime && !expired && mathAbs(force) > 0.1;\n        return isFlick ? force : 0;\n    }\n    const self = {\n        pointerDown,\n        pointerMove,\n        pointerUp,\n        readPoint\n    };\n    return self;\n}\nfunction NodeRects() {\n    function measure(node) {\n        const { offsetTop, offsetLeft, offsetWidth, offsetHeight } = node;\n        const offset = {\n            top: offsetTop,\n            right: offsetLeft + offsetWidth,\n            bottom: offsetTop + offsetHeight,\n            left: offsetLeft,\n            width: offsetWidth,\n            height: offsetHeight\n        };\n        return offset;\n    }\n    const self = {\n        measure\n    };\n    return self;\n}\nfunction PercentOfView(viewSize) {\n    function measure(n) {\n        return viewSize * (n / 100);\n    }\n    const self = {\n        measure\n    };\n    return self;\n}\nfunction ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects) {\n    let resizeObserver;\n    let containerSize;\n    let slideSizes = [];\n    let destroyed = false;\n    function readSize(node) {\n        return axis.measureSize(nodeRects.measure(node));\n    }\n    function init(emblaApi) {\n        if (!watchResize) return;\n        containerSize = readSize(container);\n        slideSizes = slides.map(readSize);\n        function defaultCallback(entries) {\n            for (const entry of entries){\n                const isContainer = entry.target === container;\n                const slideIndex = slides.indexOf(entry.target);\n                const lastSize = isContainer ? containerSize : slideSizes[slideIndex];\n                const newSize = readSize(isContainer ? container : slides[slideIndex]);\n                const diffSize = mathAbs(newSize - lastSize);\n                if (diffSize >= 0.5) {\n                    ownerWindow.requestAnimationFrame(()=>{\n                        emblaApi.reInit();\n                        eventHandler.emit(\"resize\");\n                    });\n                    break;\n                }\n            }\n        }\n        resizeObserver = new ResizeObserver((entries)=>{\n            if (destroyed) return;\n            if (isBoolean(watchResize) || watchResize(emblaApi, entries)) {\n                defaultCallback(entries);\n            }\n        });\n        const observeNodes = [\n            container\n        ].concat(slides);\n        observeNodes.forEach((node)=>resizeObserver.observe(node));\n    }\n    function destroy() {\n        if (resizeObserver) resizeObserver.disconnect();\n        destroyed = true;\n    }\n    const self = {\n        init,\n        destroy\n    };\n    return self;\n}\nfunction ScrollBody(location, offsetLocation, target, baseDuration, baseFriction) {\n    let bodyVelocity = 0;\n    let scrollDirection = 0;\n    let scrollDuration = baseDuration;\n    let scrollFriction = baseFriction;\n    let rawLocation = location.get();\n    let rawLocationPrevious = 0;\n    function seek() {\n        const diff = target.get() - location.get();\n        const isInstant = !scrollDuration;\n        let directionDiff = 0;\n        if (isInstant) {\n            bodyVelocity = 0;\n            location.set(target);\n            directionDiff = diff;\n        } else {\n            bodyVelocity += diff / scrollDuration;\n            bodyVelocity *= scrollFriction;\n            rawLocation += bodyVelocity;\n            location.add(bodyVelocity);\n            directionDiff = rawLocation - rawLocationPrevious;\n        }\n        scrollDirection = mathSign(directionDiff);\n        rawLocationPrevious = rawLocation;\n        return self;\n    }\n    function settled() {\n        const diff = target.get() - offsetLocation.get();\n        return mathAbs(diff) < 0.001;\n    }\n    function duration() {\n        return scrollDuration;\n    }\n    function direction() {\n        return scrollDirection;\n    }\n    function velocity() {\n        return bodyVelocity;\n    }\n    function useBaseDuration() {\n        return useDuration(baseDuration);\n    }\n    function useBaseFriction() {\n        return useFriction(baseFriction);\n    }\n    function useDuration(n) {\n        scrollDuration = n;\n        return self;\n    }\n    function useFriction(n) {\n        scrollFriction = n;\n        return self;\n    }\n    const self = {\n        direction,\n        duration,\n        velocity,\n        seek,\n        settled,\n        useBaseFriction,\n        useBaseDuration,\n        useFriction,\n        useDuration\n    };\n    return self;\n}\nfunction ScrollBounds(limit, offsetLocation, target, scrollBody, percentOfView) {\n    const pullBackThreshold = percentOfView.measure(10);\n    const edgeOffsetTolerance = percentOfView.measure(50);\n    const frictionLimit = Limit(0.1, 0.99);\n    let disabled = false;\n    function shouldConstrain() {\n        if (disabled) return false;\n        if (!limit.reachedAny(target.get())) return false;\n        if (!limit.reachedAny(offsetLocation.get())) return false;\n        return true;\n    }\n    function constrain(pointerDown) {\n        if (!shouldConstrain()) return;\n        const edge = limit.reachedMin(offsetLocation.get()) ? \"min\" : \"max\";\n        const diffToEdge = mathAbs(limit[edge] - offsetLocation.get());\n        const diffToTarget = target.get() - offsetLocation.get();\n        const friction = frictionLimit.constrain(diffToEdge / edgeOffsetTolerance);\n        target.subtract(diffToTarget * friction);\n        if (!pointerDown && mathAbs(diffToTarget) < pullBackThreshold) {\n            target.set(limit.constrain(target.get()));\n            scrollBody.useDuration(25).useBaseFriction();\n        }\n    }\n    function toggleActive(active) {\n        disabled = !active;\n    }\n    const self = {\n        constrain,\n        toggleActive\n    };\n    return self;\n}\nfunction ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance) {\n    const scrollBounds = Limit(-contentSize + viewSize, 0);\n    const snapsBounded = measureBounded();\n    const scrollContainLimit = findScrollContainLimit();\n    const snapsContained = measureContained();\n    function usePixelTolerance(bound, snap) {\n        return deltaAbs(bound, snap) < 1;\n    }\n    function findScrollContainLimit() {\n        const startSnap = snapsBounded[0];\n        const endSnap = arrayLast(snapsBounded);\n        const min = snapsBounded.lastIndexOf(startSnap);\n        const max = snapsBounded.indexOf(endSnap) + 1;\n        return Limit(min, max);\n    }\n    function measureBounded() {\n        return snapsAligned.map((snapAligned, index)=>{\n            const { min, max } = scrollBounds;\n            const snap = scrollBounds.constrain(snapAligned);\n            const isFirst = !index;\n            const isLast = arrayIsLastIndex(snapsAligned, index);\n            if (isFirst) return max;\n            if (isLast) return min;\n            if (usePixelTolerance(min, snap)) return min;\n            if (usePixelTolerance(max, snap)) return max;\n            return snap;\n        }).map((scrollBound)=>parseFloat(scrollBound.toFixed(3)));\n    }\n    function measureContained() {\n        if (contentSize <= viewSize + pixelTolerance) return [\n            scrollBounds.max\n        ];\n        if (containScroll === \"keepSnaps\") return snapsBounded;\n        const { min, max } = scrollContainLimit;\n        return snapsBounded.slice(min, max);\n    }\n    const self = {\n        snapsContained,\n        scrollContainLimit\n    };\n    return self;\n}\nfunction ScrollLimit(contentSize, scrollSnaps, loop) {\n    const max = scrollSnaps[0];\n    const min = loop ? max - contentSize : arrayLast(scrollSnaps);\n    const limit = Limit(min, max);\n    const self = {\n        limit\n    };\n    return self;\n}\nfunction ScrollLooper(contentSize, limit, offsetLocation, vectors) {\n    const jointSafety = 0.1;\n    const min = limit.min + jointSafety;\n    const max = limit.max + jointSafety;\n    const { reachedMin, reachedMax } = Limit(min, max);\n    function shouldLoop(direction) {\n        if (direction === 1) return reachedMax(offsetLocation.get());\n        if (direction === -1) return reachedMin(offsetLocation.get());\n        return false;\n    }\n    function loop(direction) {\n        if (!shouldLoop(direction)) return;\n        const loopDistance = contentSize * (direction * -1);\n        vectors.forEach((v)=>v.add(loopDistance));\n    }\n    const self = {\n        loop\n    };\n    return self;\n}\nfunction ScrollProgress(limit) {\n    const { max, length } = limit;\n    function get(n) {\n        const currentLocation = n - max;\n        return length ? currentLocation / -length : 0;\n    }\n    const self = {\n        get\n    };\n    return self;\n}\nfunction ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll) {\n    const { startEdge, endEdge } = axis;\n    const { groupSlides } = slidesToScroll;\n    const alignments = measureSizes().map(alignment.measure);\n    const snaps = measureUnaligned();\n    const snapsAligned = measureAligned();\n    function measureSizes() {\n        return groupSlides(slideRects).map((rects)=>arrayLast(rects)[endEdge] - rects[0][startEdge]).map(mathAbs);\n    }\n    function measureUnaligned() {\n        return slideRects.map((rect)=>containerRect[startEdge] - rect[startEdge]).map((snap)=>-mathAbs(snap));\n    }\n    function measureAligned() {\n        return groupSlides(snaps).map((g)=>g[0]).map((snap, index)=>snap + alignments[index]);\n    }\n    const self = {\n        snaps,\n        snapsAligned\n    };\n    return self;\n}\nfunction SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes) {\n    const { groupSlides } = slidesToScroll;\n    const { min, max } = scrollContainLimit;\n    const slideRegistry = createSlideRegistry();\n    function createSlideRegistry() {\n        const groupedSlideIndexes = groupSlides(slideIndexes);\n        const doNotContain = !containSnaps || containScroll === \"keepSnaps\";\n        if (scrollSnaps.length === 1) return [\n            slideIndexes\n        ];\n        if (doNotContain) return groupedSlideIndexes;\n        return groupedSlideIndexes.slice(min, max).map((group, index, groups)=>{\n            const isFirst = !index;\n            const isLast = arrayIsLastIndex(groups, index);\n            if (isFirst) {\n                const range = arrayLast(groups[0]) + 1;\n                return arrayFromNumber(range);\n            }\n            if (isLast) {\n                const range = arrayLastIndex(slideIndexes) - arrayLast(groups)[0] + 1;\n                return arrayFromNumber(range, arrayLast(groups)[0]);\n            }\n            return group;\n        });\n    }\n    const self = {\n        slideRegistry\n    };\n    return self;\n}\nfunction ScrollTarget(loop, scrollSnaps, contentSize, limit, targetVector) {\n    const { reachedAny, removeOffset, constrain } = limit;\n    function minDistance(distances) {\n        return distances.concat().sort((a, b)=>mathAbs(a) - mathAbs(b))[0];\n    }\n    function findTargetSnap(target) {\n        const distance = loop ? removeOffset(target) : constrain(target);\n        const ascDiffsToSnaps = scrollSnaps.map((snap, index)=>({\n                diff: shortcut(snap - distance, 0),\n                index\n            })).sort((d1, d2)=>mathAbs(d1.diff) - mathAbs(d2.diff));\n        const { index } = ascDiffsToSnaps[0];\n        return {\n            index,\n            distance\n        };\n    }\n    function shortcut(target, direction) {\n        const targets = [\n            target,\n            target + contentSize,\n            target - contentSize\n        ];\n        if (!loop) return target;\n        if (!direction) return minDistance(targets);\n        const matchingTargets = targets.filter((t)=>mathSign(t) === direction);\n        if (matchingTargets.length) return minDistance(matchingTargets);\n        return arrayLast(targets) - contentSize;\n    }\n    function byIndex(index, direction) {\n        const diffToSnap = scrollSnaps[index] - targetVector.get();\n        const distance = shortcut(diffToSnap, direction);\n        return {\n            index,\n            distance\n        };\n    }\n    function byDistance(distance, snap) {\n        const target = targetVector.get() + distance;\n        const { index, distance: targetSnapDistance } = findTargetSnap(target);\n        const reachedBound = !loop && reachedAny(target);\n        if (!snap || reachedBound) return {\n            index,\n            distance\n        };\n        const diffToSnap = scrollSnaps[index] - targetSnapDistance;\n        const snapDistance = distance + shortcut(diffToSnap, 0);\n        return {\n            index,\n            distance: snapDistance\n        };\n    }\n    const self = {\n        byDistance,\n        byIndex,\n        shortcut\n    };\n    return self;\n}\nfunction ScrollTo(animation, indexCurrent, indexPrevious, scrollBody, scrollTarget, targetVector, eventHandler) {\n    function scrollTo(target) {\n        const distanceDiff = target.distance;\n        const indexDiff = target.index !== indexCurrent.get();\n        targetVector.add(distanceDiff);\n        if (distanceDiff) {\n            if (scrollBody.duration()) {\n                animation.start();\n            } else {\n                animation.update();\n                animation.render(1);\n                animation.update();\n            }\n        }\n        if (indexDiff) {\n            indexPrevious.set(indexCurrent.get());\n            indexCurrent.set(target.index);\n            eventHandler.emit(\"select\");\n        }\n    }\n    function distance(n, snap) {\n        const target = scrollTarget.byDistance(n, snap);\n        scrollTo(target);\n    }\n    function index(n, direction) {\n        const targetIndex = indexCurrent.clone().set(n);\n        const target = scrollTarget.byIndex(targetIndex.get(), direction);\n        scrollTo(target);\n    }\n    const self = {\n        distance,\n        index\n    };\n    return self;\n}\nfunction SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler) {\n    let lastTabPressTime = 0;\n    function init() {\n        eventStore.add(document, \"keydown\", registerTabPress, false);\n        slides.forEach(addSlideFocusEvent);\n    }\n    function registerTabPress(event) {\n        if (event.code === \"Tab\") lastTabPressTime = new Date().getTime();\n    }\n    function addSlideFocusEvent(slide) {\n        const focus = ()=>{\n            const nowTime = new Date().getTime();\n            const diffTime = nowTime - lastTabPressTime;\n            if (diffTime > 10) return;\n            root.scrollLeft = 0;\n            const index = slides.indexOf(slide);\n            const group = slideRegistry.findIndex((group)=>group.includes(index));\n            if (!isNumber(group)) return;\n            scrollBody.useDuration(0);\n            scrollTo.index(group, 0);\n            eventHandler.emit(\"slideFocus\");\n        };\n        eventStore.add(slide, \"focus\", focus, {\n            passive: true,\n            capture: true\n        });\n    }\n    const self = {\n        init\n    };\n    return self;\n}\nfunction Vector1D(initialValue) {\n    let value = initialValue;\n    function get() {\n        return value;\n    }\n    function set(n) {\n        value = normalizeInput(n);\n    }\n    function add(n) {\n        value += normalizeInput(n);\n    }\n    function subtract(n) {\n        value -= normalizeInput(n);\n    }\n    function normalizeInput(n) {\n        return isNumber(n) ? n : n.get();\n    }\n    const self = {\n        get,\n        set,\n        add,\n        subtract\n    };\n    return self;\n}\nfunction Translate(axis, container) {\n    const translate = axis.scroll === \"x\" ? x : y;\n    const containerStyle = container.style;\n    let disabled = false;\n    function x(n) {\n        return `translate3d(${n}px,0px,0px)`;\n    }\n    function y(n) {\n        return `translate3d(0px,${n}px,0px)`;\n    }\n    function to(target) {\n        if (disabled) return;\n        containerStyle.transform = translate(axis.direction(target));\n    }\n    function toggleActive(active) {\n        disabled = !active;\n    }\n    function clear() {\n        if (disabled) return;\n        containerStyle.transform = \"\";\n        if (!container.getAttribute(\"style\")) container.removeAttribute(\"style\");\n    }\n    const self = {\n        clear,\n        to,\n        toggleActive\n    };\n    return self;\n}\nfunction SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, offsetLocation, slides) {\n    const roundingSafety = 0.5;\n    const ascItems = arrayKeys(slideSizesWithGaps);\n    const descItems = arrayKeys(slideSizesWithGaps).reverse();\n    const loopPoints = startPoints().concat(endPoints());\n    function removeSlideSizes(indexes, from) {\n        return indexes.reduce((a, i)=>{\n            return a - slideSizesWithGaps[i];\n        }, from);\n    }\n    function slidesInGap(indexes, gap) {\n        return indexes.reduce((a, i)=>{\n            const remainingGap = removeSlideSizes(a, gap);\n            return remainingGap > 0 ? a.concat([\n                i\n            ]) : a;\n        }, []);\n    }\n    function findSlideBounds(offset) {\n        return snaps.map((snap, index)=>({\n                start: snap - slideSizes[index] + roundingSafety + offset,\n                end: snap + viewSize - roundingSafety + offset\n            }));\n    }\n    function findLoopPoints(indexes, offset, isEndEdge) {\n        const slideBounds = findSlideBounds(offset);\n        return indexes.map((index)=>{\n            const initial = isEndEdge ? 0 : -contentSize;\n            const altered = isEndEdge ? contentSize : 0;\n            const boundEdge = isEndEdge ? \"end\" : \"start\";\n            const loopPoint = slideBounds[index][boundEdge];\n            return {\n                index,\n                loopPoint,\n                slideLocation: Vector1D(-1),\n                translate: Translate(axis, slides[index]),\n                target: ()=>offsetLocation.get() > loopPoint ? initial : altered\n            };\n        });\n    }\n    function startPoints() {\n        const gap = scrollSnaps[0];\n        const indexes = slidesInGap(descItems, gap);\n        return findLoopPoints(indexes, contentSize, false);\n    }\n    function endPoints() {\n        const gap = viewSize - scrollSnaps[0] - 1;\n        const indexes = slidesInGap(ascItems, gap);\n        return findLoopPoints(indexes, -contentSize, true);\n    }\n    function canLoop() {\n        return loopPoints.every(({ index })=>{\n            const otherIndexes = ascItems.filter((i)=>i !== index);\n            return removeSlideSizes(otherIndexes, viewSize) <= 0.1;\n        });\n    }\n    function loop() {\n        loopPoints.forEach((loopPoint)=>{\n            const { target, translate, slideLocation } = loopPoint;\n            const shiftLocation = target();\n            if (shiftLocation === slideLocation.get()) return;\n            translate.to(shiftLocation);\n            slideLocation.set(shiftLocation);\n        });\n    }\n    function clear() {\n        loopPoints.forEach((loopPoint)=>loopPoint.translate.clear());\n    }\n    const self = {\n        canLoop,\n        clear,\n        loop,\n        loopPoints\n    };\n    return self;\n}\nfunction SlidesHandler(container, eventHandler, watchSlides) {\n    let mutationObserver;\n    let destroyed = false;\n    function init(emblaApi) {\n        if (!watchSlides) return;\n        function defaultCallback(mutations) {\n            for (const mutation of mutations){\n                if (mutation.type === \"childList\") {\n                    emblaApi.reInit();\n                    eventHandler.emit(\"slidesChanged\");\n                    break;\n                }\n            }\n        }\n        mutationObserver = new MutationObserver((mutations)=>{\n            if (destroyed) return;\n            if (isBoolean(watchSlides) || watchSlides(emblaApi, mutations)) {\n                defaultCallback(mutations);\n            }\n        });\n        mutationObserver.observe(container, {\n            childList: true\n        });\n    }\n    function destroy() {\n        if (mutationObserver) mutationObserver.disconnect();\n        destroyed = true;\n    }\n    const self = {\n        init,\n        destroy\n    };\n    return self;\n}\nfunction SlidesInView(container, slides, eventHandler, threshold) {\n    const intersectionEntryMap = {};\n    let inViewCache = null;\n    let notInViewCache = null;\n    let intersectionObserver;\n    let destroyed = false;\n    function init() {\n        intersectionObserver = new IntersectionObserver((entries)=>{\n            if (destroyed) return;\n            entries.forEach((entry)=>{\n                const index = slides.indexOf(entry.target);\n                intersectionEntryMap[index] = entry;\n            });\n            inViewCache = null;\n            notInViewCache = null;\n            eventHandler.emit(\"slidesInView\");\n        }, {\n            root: container.parentElement,\n            threshold\n        });\n        slides.forEach((slide)=>intersectionObserver.observe(slide));\n    }\n    function destroy() {\n        if (intersectionObserver) intersectionObserver.disconnect();\n        destroyed = true;\n    }\n    function createInViewList(inView) {\n        return objectKeys(intersectionEntryMap).reduce((list, slideIndex)=>{\n            const index = parseInt(slideIndex);\n            const { isIntersecting } = intersectionEntryMap[index];\n            const inViewMatch = inView && isIntersecting;\n            const notInViewMatch = !inView && !isIntersecting;\n            if (inViewMatch || notInViewMatch) list.push(index);\n            return list;\n        }, []);\n    }\n    function get(inView = true) {\n        if (inView && inViewCache) return inViewCache;\n        if (!inView && notInViewCache) return notInViewCache;\n        const slideIndexes = createInViewList(inView);\n        if (inView) inViewCache = slideIndexes;\n        if (!inView) notInViewCache = slideIndexes;\n        return slideIndexes;\n    }\n    const self = {\n        init,\n        destroy,\n        get\n    };\n    return self;\n}\nfunction SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow) {\n    const { measureSize, startEdge, endEdge } = axis;\n    const withEdgeGap = slideRects[0] && readEdgeGap;\n    const startGap = measureStartGap();\n    const endGap = measureEndGap();\n    const slideSizes = slideRects.map(measureSize);\n    const slideSizesWithGaps = measureWithGaps();\n    function measureStartGap() {\n        if (!withEdgeGap) return 0;\n        const slideRect = slideRects[0];\n        return mathAbs(containerRect[startEdge] - slideRect[startEdge]);\n    }\n    function measureEndGap() {\n        if (!withEdgeGap) return 0;\n        const style = ownerWindow.getComputedStyle(arrayLast(slides));\n        return parseFloat(style.getPropertyValue(`margin-${endEdge}`));\n    }\n    function measureWithGaps() {\n        return slideRects.map((rect, index, rects)=>{\n            const isFirst = !index;\n            const isLast = arrayIsLastIndex(rects, index);\n            if (isFirst) return slideSizes[index] + startGap;\n            if (isLast) return slideSizes[index] + endGap;\n            return rects[index + 1][startEdge] - rect[startEdge];\n        }).map(mathAbs);\n    }\n    const self = {\n        slideSizes,\n        slideSizesWithGaps,\n        startGap,\n        endGap\n    };\n    return self;\n}\nfunction SlidesToScroll(axis, viewSize, slidesToScroll, loop, containerRect, slideRects, startGap, endGap, pixelTolerance) {\n    const { startEdge, endEdge, direction } = axis;\n    const groupByNumber = isNumber(slidesToScroll);\n    function byNumber(array, groupSize) {\n        return arrayKeys(array).filter((i)=>i % groupSize === 0).map((i)=>array.slice(i, i + groupSize));\n    }\n    function bySize(array) {\n        if (!array.length) return [];\n        return arrayKeys(array).reduce((groups, rectB, index)=>{\n            const rectA = arrayLast(groups) || 0;\n            const isFirst = rectA === 0;\n            const isLast = rectB === arrayLastIndex(array);\n            const edgeA = containerRect[startEdge] - slideRects[rectA][startEdge];\n            const edgeB = containerRect[startEdge] - slideRects[rectB][endEdge];\n            const gapA = !loop && isFirst ? direction(startGap) : 0;\n            const gapB = !loop && isLast ? direction(endGap) : 0;\n            const chunkSize = mathAbs(edgeB - gapB - (edgeA + gapA));\n            if (index && chunkSize > viewSize + pixelTolerance) groups.push(rectB);\n            if (isLast) groups.push(array.length);\n            return groups;\n        }, []).map((currentSize, index, groups)=>{\n            const previousSize = Math.max(groups[index - 1] || 0);\n            return array.slice(previousSize, currentSize);\n        });\n    }\n    function groupSlides(array) {\n        return groupByNumber ? byNumber(array, slidesToScroll) : bySize(array);\n    }\n    const self = {\n        groupSlides\n    };\n    return self;\n}\nfunction Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler) {\n    // Options\n    const { align, axis: scrollAxis, direction, startIndex, loop, duration, dragFree, dragThreshold, inViewThreshold, slidesToScroll: groupSlides, skipSnaps, containScroll, watchResize, watchSlides, watchDrag } = options;\n    // Measurements\n    const pixelTolerance = 2;\n    const nodeRects = NodeRects();\n    const containerRect = nodeRects.measure(container);\n    const slideRects = slides.map(nodeRects.measure);\n    const axis = Axis(scrollAxis, direction);\n    const viewSize = axis.measureSize(containerRect);\n    const percentOfView = PercentOfView(viewSize);\n    const alignment = Alignment(align, viewSize);\n    const containSnaps = !loop && !!containScroll;\n    const readEdgeGap = loop || !!containScroll;\n    const { slideSizes, slideSizesWithGaps, startGap, endGap } = SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow);\n    const slidesToScroll = SlidesToScroll(axis, viewSize, groupSlides, loop, containerRect, slideRects, startGap, endGap, pixelTolerance);\n    const { snaps, snapsAligned } = ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll);\n    const contentSize = -arrayLast(snaps) + arrayLast(slideSizesWithGaps);\n    const { snapsContained, scrollContainLimit } = ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance);\n    const scrollSnaps = containSnaps ? snapsContained : snapsAligned;\n    const { limit } = ScrollLimit(contentSize, scrollSnaps, loop);\n    // Indexes\n    const index = Counter(arrayLastIndex(scrollSnaps), startIndex, loop);\n    const indexPrevious = index.clone();\n    const slideIndexes = arrayKeys(slides);\n    // Animation\n    const update = ({ dragHandler, scrollBody, scrollBounds, options: { loop } })=>{\n        if (!loop) scrollBounds.constrain(dragHandler.pointerDown());\n        scrollBody.seek();\n    };\n    const render = ({ scrollBody, translate, location, offsetLocation, scrollLooper, slideLooper, dragHandler, animation, eventHandler, options: { loop } }, lagOffset)=>{\n        const velocity = scrollBody.velocity();\n        const hasSettled = scrollBody.settled();\n        if (hasSettled && !dragHandler.pointerDown()) {\n            animation.stop();\n            eventHandler.emit(\"settle\");\n        }\n        if (!hasSettled) eventHandler.emit(\"scroll\");\n        offsetLocation.set(location.get() - velocity + velocity * lagOffset);\n        if (loop) {\n            scrollLooper.loop(scrollBody.direction());\n            slideLooper.loop();\n        }\n        translate.to(offsetLocation.get());\n    };\n    const animation = Animations(ownerDocument, ownerWindow, ()=>update(engine), (lagOffset)=>render(engine, lagOffset));\n    // Shared\n    const friction = 0.68;\n    const startLocation = scrollSnaps[index.get()];\n    const location = Vector1D(startLocation);\n    const offsetLocation = Vector1D(startLocation);\n    const target = Vector1D(startLocation);\n    const scrollBody = ScrollBody(location, offsetLocation, target, duration, friction);\n    const scrollTarget = ScrollTarget(loop, scrollSnaps, contentSize, limit, target);\n    const scrollTo = ScrollTo(animation, index, indexPrevious, scrollBody, scrollTarget, target, eventHandler);\n    const scrollProgress = ScrollProgress(limit);\n    const eventStore = EventStore();\n    const slidesInView = SlidesInView(container, slides, eventHandler, inViewThreshold);\n    const { slideRegistry } = SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes);\n    const slideFocus = SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler);\n    // Engine\n    const engine = {\n        ownerDocument,\n        ownerWindow,\n        eventHandler,\n        containerRect,\n        slideRects,\n        animation,\n        axis,\n        dragHandler: DragHandler(axis, root, ownerDocument, ownerWindow, target, DragTracker(axis, ownerWindow), location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, friction, watchDrag),\n        eventStore,\n        percentOfView,\n        index,\n        indexPrevious,\n        limit,\n        location,\n        offsetLocation,\n        options,\n        resizeHandler: ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects),\n        scrollBody,\n        scrollBounds: ScrollBounds(limit, offsetLocation, target, scrollBody, percentOfView),\n        scrollLooper: ScrollLooper(contentSize, limit, offsetLocation, [\n            location,\n            offsetLocation,\n            target\n        ]),\n        scrollProgress,\n        scrollSnapList: scrollSnaps.map(scrollProgress.get),\n        scrollSnaps,\n        scrollTarget,\n        scrollTo,\n        slideLooper: SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, offsetLocation, slides),\n        slideFocus,\n        slidesHandler: SlidesHandler(container, eventHandler, watchSlides),\n        slidesInView,\n        slideIndexes,\n        slideRegistry,\n        slidesToScroll,\n        target,\n        translate: Translate(axis, container)\n    };\n    return engine;\n}\nfunction EventHandler() {\n    let listeners = {};\n    let api;\n    function init(emblaApi) {\n        api = emblaApi;\n    }\n    function getListeners(evt) {\n        return listeners[evt] || [];\n    }\n    function emit(evt) {\n        getListeners(evt).forEach((e)=>e(api, evt));\n        return self;\n    }\n    function on(evt, cb) {\n        listeners[evt] = getListeners(evt).concat([\n            cb\n        ]);\n        return self;\n    }\n    function off(evt, cb) {\n        listeners[evt] = getListeners(evt).filter((e)=>e !== cb);\n        return self;\n    }\n    function clear() {\n        listeners = {};\n    }\n    const self = {\n        init,\n        emit,\n        off,\n        on,\n        clear\n    };\n    return self;\n}\nconst defaultOptions = {\n    align: \"center\",\n    axis: \"x\",\n    container: null,\n    slides: null,\n    containScroll: \"trimSnaps\",\n    direction: \"ltr\",\n    slidesToScroll: 1,\n    inViewThreshold: 0,\n    breakpoints: {},\n    dragFree: false,\n    dragThreshold: 10,\n    loop: false,\n    skipSnaps: false,\n    duration: 25,\n    startIndex: 0,\n    active: true,\n    watchDrag: true,\n    watchResize: true,\n    watchSlides: true\n};\nfunction OptionsHandler(ownerWindow) {\n    function mergeOptions(optionsA, optionsB) {\n        return objectsMergeDeep(optionsA, optionsB || {});\n    }\n    function optionsAtMedia(options) {\n        const optionsAtMedia = options.breakpoints || {};\n        const matchedMediaOptions = objectKeys(optionsAtMedia).filter((media)=>ownerWindow.matchMedia(media).matches).map((media)=>optionsAtMedia[media]).reduce((a, mediaOption)=>mergeOptions(a, mediaOption), {});\n        return mergeOptions(options, matchedMediaOptions);\n    }\n    function optionsMediaQueries(optionsList) {\n        return optionsList.map((options)=>objectKeys(options.breakpoints || {})).reduce((acc, mediaQueries)=>acc.concat(mediaQueries), []).map(ownerWindow.matchMedia);\n    }\n    const self = {\n        mergeOptions,\n        optionsAtMedia,\n        optionsMediaQueries\n    };\n    return self;\n}\nfunction PluginsHandler(optionsHandler) {\n    let activePlugins = [];\n    function init(emblaApi, plugins) {\n        activePlugins = plugins.filter(({ options })=>optionsHandler.optionsAtMedia(options).active !== false);\n        activePlugins.forEach((plugin)=>plugin.init(emblaApi, optionsHandler));\n        return plugins.reduce((map, plugin)=>Object.assign(map, {\n                [plugin.name]: plugin\n            }), {});\n    }\n    function destroy() {\n        activePlugins = activePlugins.filter((plugin)=>plugin.destroy());\n    }\n    const self = {\n        init,\n        destroy\n    };\n    return self;\n}\nfunction EmblaCarousel(root, userOptions, userPlugins) {\n    const ownerDocument = root.ownerDocument;\n    const ownerWindow = ownerDocument.defaultView;\n    const optionsHandler = OptionsHandler(ownerWindow);\n    const pluginsHandler = PluginsHandler(optionsHandler);\n    const mediaHandlers = EventStore();\n    const eventHandler = EventHandler();\n    const { mergeOptions, optionsAtMedia, optionsMediaQueries } = optionsHandler;\n    const { on, off, emit } = eventHandler;\n    const reInit = reActivate;\n    let destroyed = false;\n    let engine;\n    let optionsBase = mergeOptions(defaultOptions, EmblaCarousel.globalOptions);\n    let options = mergeOptions(optionsBase);\n    let pluginList = [];\n    let pluginApis;\n    let container;\n    let slides;\n    function storeElements() {\n        const { container: userContainer, slides: userSlides } = options;\n        const customContainer = isString(userContainer) ? root.querySelector(userContainer) : userContainer;\n        container = customContainer || root.children[0];\n        const customSlides = isString(userSlides) ? container.querySelectorAll(userSlides) : userSlides;\n        slides = [].slice.call(customSlides || container.children);\n    }\n    function createEngine(options) {\n        const engine = Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler);\n        if (options.loop && !engine.slideLooper.canLoop()) {\n            const optionsWithoutLoop = Object.assign({}, options, {\n                loop: false\n            });\n            return createEngine(optionsWithoutLoop);\n        }\n        return engine;\n    }\n    function activate(withOptions, withPlugins) {\n        if (destroyed) return;\n        optionsBase = mergeOptions(optionsBase, withOptions);\n        options = optionsAtMedia(optionsBase);\n        pluginList = withPlugins || pluginList;\n        storeElements();\n        engine = createEngine(options);\n        optionsMediaQueries([\n            optionsBase,\n            ...pluginList.map(({ options })=>options)\n        ]).forEach((query)=>mediaHandlers.add(query, \"change\", reActivate));\n        if (!options.active) return;\n        engine.translate.to(engine.location.get());\n        engine.animation.init();\n        engine.slidesInView.init();\n        engine.slideFocus.init();\n        engine.eventHandler.init(self);\n        engine.resizeHandler.init(self);\n        engine.slidesHandler.init(self);\n        if (engine.options.loop) engine.slideLooper.loop();\n        if (container.offsetParent && slides.length) engine.dragHandler.init(self);\n        pluginApis = pluginsHandler.init(self, pluginList);\n    }\n    function reActivate(withOptions, withPlugins) {\n        const startIndex = selectedScrollSnap();\n        deActivate();\n        activate(mergeOptions({\n            startIndex\n        }, withOptions), withPlugins);\n        eventHandler.emit(\"reInit\");\n    }\n    function deActivate() {\n        engine.dragHandler.destroy();\n        engine.eventStore.clear();\n        engine.translate.clear();\n        engine.slideLooper.clear();\n        engine.resizeHandler.destroy();\n        engine.slidesHandler.destroy();\n        engine.slidesInView.destroy();\n        engine.animation.destroy();\n        pluginsHandler.destroy();\n        mediaHandlers.clear();\n    }\n    function destroy() {\n        if (destroyed) return;\n        destroyed = true;\n        mediaHandlers.clear();\n        deActivate();\n        eventHandler.emit(\"destroy\");\n        eventHandler.clear();\n    }\n    function scrollTo(index, jump, direction) {\n        if (!options.active || destroyed) return;\n        engine.scrollBody.useBaseFriction().useDuration(jump === true ? 0 : options.duration);\n        engine.scrollTo.index(index, direction || 0);\n    }\n    function scrollNext(jump) {\n        const next = engine.index.add(1).get();\n        scrollTo(next, jump, -1);\n    }\n    function scrollPrev(jump) {\n        const prev = engine.index.add(-1).get();\n        scrollTo(prev, jump, 1);\n    }\n    function canScrollNext() {\n        const next = engine.index.add(1).get();\n        return next !== selectedScrollSnap();\n    }\n    function canScrollPrev() {\n        const prev = engine.index.add(-1).get();\n        return prev !== selectedScrollSnap();\n    }\n    function scrollSnapList() {\n        return engine.scrollSnapList;\n    }\n    function scrollProgress() {\n        return engine.scrollProgress.get(engine.location.get());\n    }\n    function selectedScrollSnap() {\n        return engine.index.get();\n    }\n    function previousScrollSnap() {\n        return engine.indexPrevious.get();\n    }\n    function slidesInView() {\n        return engine.slidesInView.get();\n    }\n    function slidesNotInView() {\n        return engine.slidesInView.get(false);\n    }\n    function plugins() {\n        return pluginApis;\n    }\n    function internalEngine() {\n        return engine;\n    }\n    function rootNode() {\n        return root;\n    }\n    function containerNode() {\n        return container;\n    }\n    function slideNodes() {\n        return slides;\n    }\n    const self = {\n        canScrollNext,\n        canScrollPrev,\n        containerNode,\n        internalEngine,\n        destroy,\n        off,\n        on,\n        emit,\n        plugins,\n        previousScrollSnap,\n        reInit,\n        rootNode,\n        scrollNext,\n        scrollPrev,\n        scrollProgress,\n        scrollSnapList,\n        scrollTo,\n        selectedScrollSnap,\n        slideNodes,\n        slidesInView,\n        slidesNotInView\n    };\n    activate(userOptions, userPlugins);\n    setTimeout(()=>eventHandler.emit(\"init\"), 0);\n    return self;\n}\nEmblaCarousel.globalOptions = undefined;\n //# sourceMappingURL=embla-carousel.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/embla-carousel/esm/embla-carousel.esm.js\n");

/***/ })

};
;