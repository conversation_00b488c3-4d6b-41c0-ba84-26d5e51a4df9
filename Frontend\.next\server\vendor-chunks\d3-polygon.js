"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-polygon";
exports.ids = ["vendor-chunks/d3-polygon"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-polygon/src/area.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-polygon/src/area.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n    var i = -1, n = polygon.length, a, b = polygon[n - 1], area = 0;\n    while(++i < n){\n        a = b;\n        b = polygon[i];\n        area += a[1] * b[0] - a[0] * b[1];\n    }\n    return area / 2;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvYXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLE9BQU87SUFDN0IsSUFBSUMsSUFBSSxDQUFDLEdBQ0xDLElBQUlGLFFBQVFHLE1BQU0sRUFDbEJDLEdBQ0FDLElBQUlMLE9BQU8sQ0FBQ0UsSUFBSSxFQUFFLEVBQ2xCSSxPQUFPO0lBRVgsTUFBTyxFQUFFTCxJQUFJQyxFQUFHO1FBQ2RFLElBQUlDO1FBQ0pBLElBQUlMLE9BQU8sQ0FBQ0MsRUFBRTtRQUNkSyxRQUFRRixDQUFDLENBQUMsRUFBRSxHQUFHQyxDQUFDLENBQUMsRUFBRSxHQUFHRCxDQUFDLENBQUMsRUFBRSxHQUFHQyxDQUFDLENBQUMsRUFBRTtJQUNuQztJQUVBLE9BQU9DLE9BQU87QUFDaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1wb2x5Z29uL3NyYy9hcmVhLmpzP2NhZDgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24ocG9seWdvbikge1xuICB2YXIgaSA9IC0xLFxuICAgICAgbiA9IHBvbHlnb24ubGVuZ3RoLFxuICAgICAgYSxcbiAgICAgIGIgPSBwb2x5Z29uW24gLSAxXSxcbiAgICAgIGFyZWEgPSAwO1xuXG4gIHdoaWxlICgrK2kgPCBuKSB7XG4gICAgYSA9IGI7XG4gICAgYiA9IHBvbHlnb25baV07XG4gICAgYXJlYSArPSBhWzFdICogYlswXSAtIGFbMF0gKiBiWzFdO1xuICB9XG5cbiAgcmV0dXJuIGFyZWEgLyAyO1xufVxuIl0sIm5hbWVzIjpbInBvbHlnb24iLCJpIiwibiIsImxlbmd0aCIsImEiLCJiIiwiYXJlYSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/area.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/centroid.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-polygon/src/centroid.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n    var i = -1, n = polygon.length, x = 0, y = 0, a, b = polygon[n - 1], c, k = 0;\n    while(++i < n){\n        a = b;\n        b = polygon[i];\n        k += c = a[0] * b[1] - b[0] * a[1];\n        x += (a[0] + b[0]) * c;\n        y += (a[1] + b[1]) * c;\n    }\n    return k *= 3, [\n        x / k,\n        y / k\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY2VudHJvaWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxPQUFPO0lBQzdCLElBQUlDLElBQUksQ0FBQyxHQUNMQyxJQUFJRixRQUFRRyxNQUFNLEVBQ2xCQyxJQUFJLEdBQ0pDLElBQUksR0FDSkMsR0FDQUMsSUFBSVAsT0FBTyxDQUFDRSxJQUFJLEVBQUUsRUFDbEJNLEdBQ0FDLElBQUk7SUFFUixNQUFPLEVBQUVSLElBQUlDLEVBQUc7UUFDZEksSUFBSUM7UUFDSkEsSUFBSVAsT0FBTyxDQUFDQyxFQUFFO1FBQ2RRLEtBQUtELElBQUlGLENBQUMsQ0FBQyxFQUFFLEdBQUdDLENBQUMsQ0FBQyxFQUFFLEdBQUdBLENBQUMsQ0FBQyxFQUFFLEdBQUdELENBQUMsQ0FBQyxFQUFFO1FBQ2xDRixLQUFLLENBQUNFLENBQUMsQ0FBQyxFQUFFLEdBQUdDLENBQUMsQ0FBQyxFQUFFLElBQUlDO1FBQ3JCSCxLQUFLLENBQUNDLENBQUMsQ0FBQyxFQUFFLEdBQUdDLENBQUMsQ0FBQyxFQUFFLElBQUlDO0lBQ3ZCO0lBRUEsT0FBT0MsS0FBSyxHQUFHO1FBQUNMLElBQUlLO1FBQUdKLElBQUlJO0tBQUU7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uL25vZGVfbW9kdWxlcy9kMy1wb2x5Z29uL3NyYy9jZW50cm9pZC5qcz8yMDBiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHBvbHlnb24pIHtcbiAgdmFyIGkgPSAtMSxcbiAgICAgIG4gPSBwb2x5Z29uLmxlbmd0aCxcbiAgICAgIHggPSAwLFxuICAgICAgeSA9IDAsXG4gICAgICBhLFxuICAgICAgYiA9IHBvbHlnb25bbiAtIDFdLFxuICAgICAgYyxcbiAgICAgIGsgPSAwO1xuXG4gIHdoaWxlICgrK2kgPCBuKSB7XG4gICAgYSA9IGI7XG4gICAgYiA9IHBvbHlnb25baV07XG4gICAgayArPSBjID0gYVswXSAqIGJbMV0gLSBiWzBdICogYVsxXTtcbiAgICB4ICs9IChhWzBdICsgYlswXSkgKiBjO1xuICAgIHkgKz0gKGFbMV0gKyBiWzFdKSAqIGM7XG4gIH1cblxuICByZXR1cm4gayAqPSAzLCBbeCAvIGssIHkgLyBrXTtcbn1cbiJdLCJuYW1lcyI6WyJwb2x5Z29uIiwiaSIsIm4iLCJsZW5ndGgiLCJ4IiwieSIsImEiLCJiIiwiYyIsImsiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/centroid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/contains.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-polygon/src/contains.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon, point) {\n    var n = polygon.length, p = polygon[n - 1], x = point[0], y = point[1], x0 = p[0], y0 = p[1], x1, y1, inside = false;\n    for(var i = 0; i < n; ++i){\n        p = polygon[i], x1 = p[0], y1 = p[1];\n        if (y1 > y !== y0 > y && x < (x0 - x1) * (y - y1) / (y0 - y1) + x1) inside = !inside;\n        x0 = x1, y0 = y1;\n    }\n    return inside;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY29udGFpbnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxPQUFPLEVBQUVDLEtBQUs7SUFDcEMsSUFBSUMsSUFBSUYsUUFBUUcsTUFBTSxFQUNsQkMsSUFBSUosT0FBTyxDQUFDRSxJQUFJLEVBQUUsRUFDbEJHLElBQUlKLEtBQUssQ0FBQyxFQUFFLEVBQUVLLElBQUlMLEtBQUssQ0FBQyxFQUFFLEVBQzFCTSxLQUFLSCxDQUFDLENBQUMsRUFBRSxFQUFFSSxLQUFLSixDQUFDLENBQUMsRUFBRSxFQUNwQkssSUFBSUMsSUFDSkMsU0FBUztJQUViLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJVixHQUFHLEVBQUVVLEVBQUc7UUFDMUJSLElBQUlKLE9BQU8sQ0FBQ1ksRUFBRSxFQUFFSCxLQUFLTCxDQUFDLENBQUMsRUFBRSxFQUFFTSxLQUFLTixDQUFDLENBQUMsRUFBRTtRQUNwQyxJQUFJLEtBQU9FLE1BQVFFLEtBQUtGLEtBQVFELElBQUksQ0FBQ0UsS0FBS0UsRUFBQyxJQUFNSCxDQUFBQSxJQUFJSSxFQUFDLElBQU1GLENBQUFBLEtBQUtFLEVBQUMsSUFBS0QsSUFBS0UsU0FBUyxDQUFDQTtRQUN0RkosS0FBS0UsSUFBSUQsS0FBS0U7SUFDaEI7SUFFQSxPQUFPQztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY29udGFpbnMuanM/ZTJkMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihwb2x5Z29uLCBwb2ludCkge1xuICB2YXIgbiA9IHBvbHlnb24ubGVuZ3RoLFxuICAgICAgcCA9IHBvbHlnb25bbiAtIDFdLFxuICAgICAgeCA9IHBvaW50WzBdLCB5ID0gcG9pbnRbMV0sXG4gICAgICB4MCA9IHBbMF0sIHkwID0gcFsxXSxcbiAgICAgIHgxLCB5MSxcbiAgICAgIGluc2lkZSA9IGZhbHNlO1xuXG4gIGZvciAodmFyIGkgPSAwOyBpIDwgbjsgKytpKSB7XG4gICAgcCA9IHBvbHlnb25baV0sIHgxID0gcFswXSwgeTEgPSBwWzFdO1xuICAgIGlmICgoKHkxID4geSkgIT09ICh5MCA+IHkpKSAmJiAoeCA8ICh4MCAtIHgxKSAqICh5IC0geTEpIC8gKHkwIC0geTEpICsgeDEpKSBpbnNpZGUgPSAhaW5zaWRlO1xuICAgIHgwID0geDEsIHkwID0geTE7XG4gIH1cblxuICByZXR1cm4gaW5zaWRlO1xufVxuIl0sIm5hbWVzIjpbInBvbHlnb24iLCJwb2ludCIsIm4iLCJsZW5ndGgiLCJwIiwieCIsInkiLCJ4MCIsInkwIiwieDEiLCJ5MSIsImluc2lkZSIsImkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/contains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/cross.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-polygon/src/cross.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Returns the 2D cross product of AB and AC vectors, i.e., the z-component of\n// the 3D cross product in a quadrant I Cartesian coordinate system (+x is\n// right, +y is up). Returns a positive value if ABC is counter-clockwise,\n// negative if clockwise, and zero if the points are collinear.\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b, c) {\n    return (b[0] - a[0]) * (c[1] - a[1]) - (b[1] - a[1]) * (c[0] - a[0]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY3Jvc3MuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDhFQUE4RTtBQUM5RSwwRUFBMEU7QUFDMUUsMEVBQTBFO0FBQzFFLCtEQUErRDtBQUMvRCw2QkFBZSxvQ0FBU0EsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLENBQUM7SUFDN0IsT0FBTyxDQUFDRCxDQUFDLENBQUMsRUFBRSxHQUFHRCxDQUFDLENBQUMsRUFBRSxJQUFLRSxDQUFBQSxDQUFDLENBQUMsRUFBRSxHQUFHRixDQUFDLENBQUMsRUFBRSxJQUFJLENBQUNDLENBQUMsQ0FBQyxFQUFFLEdBQUdELENBQUMsQ0FBQyxFQUFFLElBQUtFLENBQUFBLENBQUMsQ0FBQyxFQUFFLEdBQUdGLENBQUMsQ0FBQyxFQUFFO0FBQ3JFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY3Jvc3MuanM/OGYxMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBSZXR1cm5zIHRoZSAyRCBjcm9zcyBwcm9kdWN0IG9mIEFCIGFuZCBBQyB2ZWN0b3JzLCBpLmUuLCB0aGUgei1jb21wb25lbnQgb2Zcbi8vIHRoZSAzRCBjcm9zcyBwcm9kdWN0IGluIGEgcXVhZHJhbnQgSSBDYXJ0ZXNpYW4gY29vcmRpbmF0ZSBzeXN0ZW0gKCt4IGlzXG4vLyByaWdodCwgK3kgaXMgdXApLiBSZXR1cm5zIGEgcG9zaXRpdmUgdmFsdWUgaWYgQUJDIGlzIGNvdW50ZXItY2xvY2t3aXNlLFxuLy8gbmVnYXRpdmUgaWYgY2xvY2t3aXNlLCBhbmQgemVybyBpZiB0aGUgcG9pbnRzIGFyZSBjb2xsaW5lYXIuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihhLCBiLCBjKSB7XG4gIHJldHVybiAoYlswXSAtIGFbMF0pICogKGNbMV0gLSBhWzFdKSAtIChiWzFdIC0gYVsxXSkgKiAoY1swXSAtIGFbMF0pO1xufVxuIl0sIm5hbWVzIjpbImEiLCJiIiwiYyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/cross.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/hull.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-polygon/src/hull.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cross_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cross.js */ \"(ssr)/./node_modules/d3-polygon/src/cross.js\");\n\nfunction lexicographicOrder(a, b) {\n    return a[0] - b[0] || a[1] - b[1];\n}\n// Computes the upper convex hull per the monotone chain algorithm.\n// Assumes points.length >= 3, is sorted by x, unique in y.\n// Returns an array of indices into points in left-to-right order.\nfunction computeUpperHullIndexes(points) {\n    const n = points.length, indexes = [\n        0,\n        1\n    ];\n    let size = 2, i;\n    for(i = 2; i < n; ++i){\n        while(size > 1 && (0,_cross_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(points[indexes[size - 2]], points[indexes[size - 1]], points[i]) <= 0)--size;\n        indexes[size++] = i;\n    }\n    return indexes.slice(0, size); // remove popped points\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(points) {\n    if ((n = points.length) < 3) return null;\n    var i, n, sortedPoints = new Array(n), flippedPoints = new Array(n);\n    for(i = 0; i < n; ++i)sortedPoints[i] = [\n        +points[i][0],\n        +points[i][1],\n        i\n    ];\n    sortedPoints.sort(lexicographicOrder);\n    for(i = 0; i < n; ++i)flippedPoints[i] = [\n        sortedPoints[i][0],\n        -sortedPoints[i][1]\n    ];\n    var upperIndexes = computeUpperHullIndexes(sortedPoints), lowerIndexes = computeUpperHullIndexes(flippedPoints);\n    // Construct the hull polygon, removing possible duplicate endpoints.\n    var skipLeft = lowerIndexes[0] === upperIndexes[0], skipRight = lowerIndexes[lowerIndexes.length - 1] === upperIndexes[upperIndexes.length - 1], hull = [];\n    // Add upper hull in right-to-l order.\n    // Then add lower hull in left-to-right order.\n    for(i = upperIndexes.length - 1; i >= 0; --i)hull.push(points[sortedPoints[upperIndexes[i]][2]]);\n    for(i = +skipLeft; i < lowerIndexes.length - skipRight; ++i)hull.push(points[sortedPoints[lowerIndexes[i]][2]]);\n    return hull;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/hull.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/index.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-polygon/src/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   polygonArea: () => (/* reexport safe */ _area_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   polygonCentroid: () => (/* reexport safe */ _centroid_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   polygonContains: () => (/* reexport safe */ _contains_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   polygonHull: () => (/* reexport safe */ _hull_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   polygonLength: () => (/* reexport safe */ _length_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-polygon/src/area.js\");\n/* harmony import */ var _centroid_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./centroid.js */ \"(ssr)/./node_modules/d3-polygon/src/centroid.js\");\n/* harmony import */ var _hull_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hull.js */ \"(ssr)/./node_modules/d3-polygon/src/hull.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/./node_modules/d3-polygon/src/contains.js\");\n/* harmony import */ var _length_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./length.js */ \"(ssr)/./node_modules/d3-polygon/src/length.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFpRDtBQUNRO0FBQ1I7QUFDUTtBQUNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvaW5kZXguanM/ZjJmYSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge2RlZmF1bHQgYXMgcG9seWdvbkFyZWF9IGZyb20gXCIuL2FyZWEuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBwb2x5Z29uQ2VudHJvaWR9IGZyb20gXCIuL2NlbnRyb2lkLmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgcG9seWdvbkh1bGx9IGZyb20gXCIuL2h1bGwuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBwb2x5Z29uQ29udGFpbnN9IGZyb20gXCIuL2NvbnRhaW5zLmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgcG9seWdvbkxlbmd0aH0gZnJvbSBcIi4vbGVuZ3RoLmpzXCI7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsInBvbHlnb25BcmVhIiwicG9seWdvbkNlbnRyb2lkIiwicG9seWdvbkh1bGwiLCJwb2x5Z29uQ29udGFpbnMiLCJwb2x5Z29uTGVuZ3RoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/length.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-polygon/src/length.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n    var i = -1, n = polygon.length, b = polygon[n - 1], xa, ya, xb = b[0], yb = b[1], perimeter = 0;\n    while(++i < n){\n        xa = xb;\n        ya = yb;\n        b = polygon[i];\n        xb = b[0];\n        yb = b[1];\n        xa -= xb;\n        ya -= yb;\n        perimeter += Math.hypot(xa, ya);\n    }\n    return perimeter;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvbGVuZ3RoLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBU0EsT0FBTztJQUM3QixJQUFJQyxJQUFJLENBQUMsR0FDTEMsSUFBSUYsUUFBUUcsTUFBTSxFQUNsQkMsSUFBSUosT0FBTyxDQUFDRSxJQUFJLEVBQUUsRUFDbEJHLElBQ0FDLElBQ0FDLEtBQUtILENBQUMsQ0FBQyxFQUFFLEVBQ1RJLEtBQUtKLENBQUMsQ0FBQyxFQUFFLEVBQ1RLLFlBQVk7SUFFaEIsTUFBTyxFQUFFUixJQUFJQyxFQUFHO1FBQ2RHLEtBQUtFO1FBQ0xELEtBQUtFO1FBQ0xKLElBQUlKLE9BQU8sQ0FBQ0MsRUFBRTtRQUNkTSxLQUFLSCxDQUFDLENBQUMsRUFBRTtRQUNUSSxLQUFLSixDQUFDLENBQUMsRUFBRTtRQUNUQyxNQUFNRTtRQUNORCxNQUFNRTtRQUNOQyxhQUFhQyxLQUFLQyxLQUFLLENBQUNOLElBQUlDO0lBQzlCO0lBRUEsT0FBT0c7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vbm9kZV9tb2R1bGVzL2QzLXBvbHlnb24vc3JjL2xlbmd0aC5qcz9kZGU0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHBvbHlnb24pIHtcbiAgdmFyIGkgPSAtMSxcbiAgICAgIG4gPSBwb2x5Z29uLmxlbmd0aCxcbiAgICAgIGIgPSBwb2x5Z29uW24gLSAxXSxcbiAgICAgIHhhLFxuICAgICAgeWEsXG4gICAgICB4YiA9IGJbMF0sXG4gICAgICB5YiA9IGJbMV0sXG4gICAgICBwZXJpbWV0ZXIgPSAwO1xuXG4gIHdoaWxlICgrK2kgPCBuKSB7XG4gICAgeGEgPSB4YjtcbiAgICB5YSA9IHliO1xuICAgIGIgPSBwb2x5Z29uW2ldO1xuICAgIHhiID0gYlswXTtcbiAgICB5YiA9IGJbMV07XG4gICAgeGEgLT0geGI7XG4gICAgeWEgLT0geWI7XG4gICAgcGVyaW1ldGVyICs9IE1hdGguaHlwb3QoeGEsIHlhKTtcbiAgfVxuXG4gIHJldHVybiBwZXJpbWV0ZXI7XG59XG4iXSwibmFtZXMiOlsicG9seWdvbiIsImkiLCJuIiwibGVuZ3RoIiwiYiIsInhhIiwieWEiLCJ4YiIsInliIiwicGVyaW1ldGVyIiwiTWF0aCIsImh5cG90Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/length.js\n");

/***/ })

};
;